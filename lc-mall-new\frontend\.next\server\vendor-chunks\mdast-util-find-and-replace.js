"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-find-and-replace";
exports.ids = ["vendor-chunks/mdast-util-find-and-replace"];
exports.modules = {

/***/ "(rsc)/../node_modules/mdast-util-find-and-replace/lib/index.js":
/*!****************************************************************!*\
  !*** ../node_modules/mdast-util-find-and-replace/lib/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findAndReplace: () => (/* binding */ findAndReplace)\n/* harmony export */ });\n/* harmony import */ var escape_string_regexp__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! escape-string-regexp */ \"(rsc)/../node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp/index.js\");\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit-parents */ \"(rsc)/../node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-is */ \"(rsc)/../node_modules/unist-util-is/lib/index.js\");\n/**\n * @import {Nodes, Parents, PhrasingContent, Root, Text} from 'mdast'\n * @import {BuildVisitor, Test, VisitorResult} from 'unist-util-visit-parents'\n */ /**\n * @typedef RegExpMatchObject\n *   Info on the match.\n * @property {number} index\n *   The index of the search at which the result was found.\n * @property {string} input\n *   A copy of the search string in the text node.\n * @property {[...Array<Parents>, Text]} stack\n *   All ancestors of the text node, where the last node is the text itself.\n *\n * @typedef {RegExp | string} Find\n *   Pattern to find.\n *\n *   Strings are escaped and then turned into global expressions.\n *\n * @typedef {Array<FindAndReplaceTuple>} FindAndReplaceList\n *   Several find and replaces, in array form.\n *\n * @typedef {[Find, Replace?]} FindAndReplaceTuple\n *   Find and replace in tuple form.\n *\n * @typedef {ReplaceFunction | string | null | undefined} Replace\n *   Thing to replace with.\n *\n * @callback ReplaceFunction\n *   Callback called when a search matches.\n * @param {...any} parameters\n *   The parameters are the result of corresponding search expression:\n *\n *   * `value` (`string`) — whole match\n *   * `...capture` (`Array<string>`) — matches from regex capture groups\n *   * `match` (`RegExpMatchObject`) — info on the match\n * @returns {Array<PhrasingContent> | PhrasingContent | string | false | null | undefined}\n *   Thing to replace with.\n *\n *   * when `null`, `undefined`, `''`, remove the match\n *   * …or when `false`, do not replace at all\n *   * …or when `string`, replace with a text node of that value\n *   * …or when `Node` or `Array<Node>`, replace with those nodes\n *\n * @typedef {[RegExp, ReplaceFunction]} Pair\n *   Normalized find and replace.\n *\n * @typedef {Array<Pair>} Pairs\n *   All find and replaced.\n *\n * @typedef Options\n *   Configuration.\n * @property {Test | null | undefined} [ignore]\n *   Test for which nodes to ignore (optional).\n */ \n\n\n/**\n * Find patterns in a tree and replace them.\n *\n * The algorithm searches the tree in *preorder* for complete values in `Text`\n * nodes.\n * Partial matches are not supported.\n *\n * @param {Nodes} tree\n *   Tree to change.\n * @param {FindAndReplaceList | FindAndReplaceTuple} list\n *   Patterns to find.\n * @param {Options | null | undefined} [options]\n *   Configuration (when `find` is not `Find`).\n * @returns {undefined}\n *   Nothing.\n */ function findAndReplace(tree, list, options) {\n    const settings = options || {};\n    const ignored = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_1__.convert)(settings.ignore || []);\n    const pairs = toPairs(list);\n    let pairIndex = -1;\n    while(++pairIndex < pairs.length){\n        (0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_2__.visitParents)(tree, \"text\", visitor);\n    }\n    /** @type {BuildVisitor<Root, 'text'>} */ function visitor(node, parents) {\n        let index = -1;\n        /** @type {Parents | undefined} */ let grandparent;\n        while(++index < parents.length){\n            const parent = parents[index];\n            /** @type {Array<Nodes> | undefined} */ const siblings = grandparent ? grandparent.children : undefined;\n            if (ignored(parent, siblings ? siblings.indexOf(parent) : undefined, grandparent)) {\n                return;\n            }\n            grandparent = parent;\n        }\n        if (grandparent) {\n            return handler(node, parents);\n        }\n    }\n    /**\n   * Handle a text node which is not in an ignored parent.\n   *\n   * @param {Text} node\n   *   Text node.\n   * @param {Array<Parents>} parents\n   *   Parents.\n   * @returns {VisitorResult}\n   *   Result.\n   */ function handler(node, parents) {\n        const parent = parents[parents.length - 1];\n        const find = pairs[pairIndex][0];\n        const replace = pairs[pairIndex][1];\n        let start = 0;\n        /** @type {Array<Nodes>} */ const siblings = parent.children;\n        const index = siblings.indexOf(node);\n        let change = false;\n        /** @type {Array<PhrasingContent>} */ let nodes = [];\n        find.lastIndex = 0;\n        let match = find.exec(node.value);\n        while(match){\n            const position = match.index;\n            /** @type {RegExpMatchObject} */ const matchObject = {\n                index: match.index,\n                input: match.input,\n                stack: [\n                    ...parents,\n                    node\n                ]\n            };\n            let value = replace(...match, matchObject);\n            if (typeof value === \"string\") {\n                value = value.length > 0 ? {\n                    type: \"text\",\n                    value\n                } : undefined;\n            }\n            // It wasn’t a match after all.\n            if (value === false) {\n                // False acts as if there was no match.\n                // So we need to reset `lastIndex`, which currently being at the end of\n                // the current match, to the beginning.\n                find.lastIndex = position + 1;\n            } else {\n                if (start !== position) {\n                    nodes.push({\n                        type: \"text\",\n                        value: node.value.slice(start, position)\n                    });\n                }\n                if (Array.isArray(value)) {\n                    nodes.push(...value);\n                } else if (value) {\n                    nodes.push(value);\n                }\n                start = position + match[0].length;\n                change = true;\n            }\n            if (!find.global) {\n                break;\n            }\n            match = find.exec(node.value);\n        }\n        if (change) {\n            if (start < node.value.length) {\n                nodes.push({\n                    type: \"text\",\n                    value: node.value.slice(start)\n                });\n            }\n            parent.children.splice(index, 1, ...nodes);\n        } else {\n            nodes = [\n                node\n            ];\n        }\n        return index + nodes.length;\n    }\n}\n/**\n * Turn a tuple or a list of tuples into pairs.\n *\n * @param {FindAndReplaceList | FindAndReplaceTuple} tupleOrList\n *   Schema.\n * @returns {Pairs}\n *   Clean pairs.\n */ function toPairs(tupleOrList) {\n    /** @type {Pairs} */ const result = [];\n    if (!Array.isArray(tupleOrList)) {\n        throw new TypeError(\"Expected find and replace tuple or list of tuples\");\n    }\n    /** @type {FindAndReplaceList} */ // @ts-expect-error: correct.\n    const list = !tupleOrList[0] || Array.isArray(tupleOrList[0]) ? tupleOrList : [\n        tupleOrList\n    ];\n    let index = -1;\n    while(++index < list.length){\n        const tuple = list[index];\n        result.push([\n            toExpression(tuple[0]),\n            toFunction(tuple[1])\n        ]);\n    }\n    return result;\n}\n/**\n * Turn a find into an expression.\n *\n * @param {Find} find\n *   Find.\n * @returns {RegExp}\n *   Expression.\n */ function toExpression(find) {\n    return typeof find === \"string\" ? new RegExp((0,escape_string_regexp__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(find), \"g\") : find;\n}\n/**\n * Turn a replace into a function.\n *\n * @param {Replace} replace\n *   Replace.\n * @returns {ReplaceFunction}\n *   Function.\n */ function toFunction(replace) {\n    return typeof replace === \"function\" ? replace : function() {\n        return replace;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtZmluZC1hbmQtcmVwbGFjZS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBOzs7Q0FHQyxHQUVEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQWtEQyxHQUV3QztBQUNZO0FBQ2hCO0FBRXJDOzs7Ozs7Ozs7Ozs7Ozs7Q0FlQyxHQUNNLFNBQVNHLGVBQWVDLElBQUksRUFBRUMsSUFBSSxFQUFFQyxPQUFPO0lBQ2hELE1BQU1DLFdBQVdELFdBQVcsQ0FBQztJQUM3QixNQUFNRSxVQUFVTixzREFBT0EsQ0FBQ0ssU0FBU0UsTUFBTSxJQUFJLEVBQUU7SUFDN0MsTUFBTUMsUUFBUUMsUUFBUU47SUFDdEIsSUFBSU8sWUFBWSxDQUFDO0lBRWpCLE1BQU8sRUFBRUEsWUFBWUYsTUFBTUcsTUFBTSxDQUFFO1FBQ2pDWixzRUFBWUEsQ0FBQ0csTUFBTSxRQUFRVTtJQUM3QjtJQUVBLHVDQUF1QyxHQUN2QyxTQUFTQSxRQUFRQyxJQUFJLEVBQUVDLE9BQU87UUFDNUIsSUFBSUMsUUFBUSxDQUFDO1FBQ2IsZ0NBQWdDLEdBQ2hDLElBQUlDO1FBRUosTUFBTyxFQUFFRCxRQUFRRCxRQUFRSCxNQUFNLENBQUU7WUFDL0IsTUFBTU0sU0FBU0gsT0FBTyxDQUFDQyxNQUFNO1lBQzdCLHFDQUFxQyxHQUNyQyxNQUFNRyxXQUFXRixjQUFjQSxZQUFZRyxRQUFRLEdBQUdDO1lBRXRELElBQ0VkLFFBQ0VXLFFBQ0FDLFdBQVdBLFNBQVNHLE9BQU8sQ0FBQ0osVUFBVUcsV0FDdENKLGNBRUY7Z0JBQ0E7WUFDRjtZQUVBQSxjQUFjQztRQUNoQjtRQUVBLElBQUlELGFBQWE7WUFDZixPQUFPTSxRQUFRVCxNQUFNQztRQUN2QjtJQUNGO0lBRUE7Ozs7Ozs7OztHQVNDLEdBQ0QsU0FBU1EsUUFBUVQsSUFBSSxFQUFFQyxPQUFPO1FBQzVCLE1BQU1HLFNBQVNILE9BQU8sQ0FBQ0EsUUFBUUgsTUFBTSxHQUFHLEVBQUU7UUFDMUMsTUFBTVksT0FBT2YsS0FBSyxDQUFDRSxVQUFVLENBQUMsRUFBRTtRQUNoQyxNQUFNYyxVQUFVaEIsS0FBSyxDQUFDRSxVQUFVLENBQUMsRUFBRTtRQUNuQyxJQUFJZSxRQUFRO1FBQ1oseUJBQXlCLEdBQ3pCLE1BQU1QLFdBQVdELE9BQU9FLFFBQVE7UUFDaEMsTUFBTUosUUFBUUcsU0FBU0csT0FBTyxDQUFDUjtRQUMvQixJQUFJYSxTQUFTO1FBQ2IsbUNBQW1DLEdBQ25DLElBQUlDLFFBQVEsRUFBRTtRQUVkSixLQUFLSyxTQUFTLEdBQUc7UUFFakIsSUFBSUMsUUFBUU4sS0FBS08sSUFBSSxDQUFDakIsS0FBS2tCLEtBQUs7UUFFaEMsTUFBT0YsTUFBTztZQUNaLE1BQU1HLFdBQVdILE1BQU1kLEtBQUs7WUFDNUIsOEJBQThCLEdBQzlCLE1BQU1rQixjQUFjO2dCQUNsQmxCLE9BQU9jLE1BQU1kLEtBQUs7Z0JBQ2xCbUIsT0FBT0wsTUFBTUssS0FBSztnQkFDbEJDLE9BQU87dUJBQUlyQjtvQkFBU0Q7aUJBQUs7WUFDM0I7WUFDQSxJQUFJa0IsUUFBUVAsV0FBV0ssT0FBT0k7WUFFOUIsSUFBSSxPQUFPRixVQUFVLFVBQVU7Z0JBQzdCQSxRQUFRQSxNQUFNcEIsTUFBTSxHQUFHLElBQUk7b0JBQUN5QixNQUFNO29CQUFRTDtnQkFBSyxJQUFJWDtZQUNyRDtZQUVBLCtCQUErQjtZQUMvQixJQUFJVyxVQUFVLE9BQU87Z0JBQ25CLHVDQUF1QztnQkFDdkMsdUVBQXVFO2dCQUN2RSx1Q0FBdUM7Z0JBQ3ZDUixLQUFLSyxTQUFTLEdBQUdJLFdBQVc7WUFDOUIsT0FBTztnQkFDTCxJQUFJUCxVQUFVTyxVQUFVO29CQUN0QkwsTUFBTVUsSUFBSSxDQUFDO3dCQUNURCxNQUFNO3dCQUNOTCxPQUFPbEIsS0FBS2tCLEtBQUssQ0FBQ08sS0FBSyxDQUFDYixPQUFPTztvQkFDakM7Z0JBQ0Y7Z0JBRUEsSUFBSU8sTUFBTUMsT0FBTyxDQUFDVCxRQUFRO29CQUN4QkosTUFBTVUsSUFBSSxJQUFJTjtnQkFDaEIsT0FBTyxJQUFJQSxPQUFPO29CQUNoQkosTUFBTVUsSUFBSSxDQUFDTjtnQkFDYjtnQkFFQU4sUUFBUU8sV0FBV0gsS0FBSyxDQUFDLEVBQUUsQ0FBQ2xCLE1BQU07Z0JBQ2xDZSxTQUFTO1lBQ1g7WUFFQSxJQUFJLENBQUNILEtBQUtrQixNQUFNLEVBQUU7Z0JBQ2hCO1lBQ0Y7WUFFQVosUUFBUU4sS0FBS08sSUFBSSxDQUFDakIsS0FBS2tCLEtBQUs7UUFDOUI7UUFFQSxJQUFJTCxRQUFRO1lBQ1YsSUFBSUQsUUFBUVosS0FBS2tCLEtBQUssQ0FBQ3BCLE1BQU0sRUFBRTtnQkFDN0JnQixNQUFNVSxJQUFJLENBQUM7b0JBQUNELE1BQU07b0JBQVFMLE9BQU9sQixLQUFLa0IsS0FBSyxDQUFDTyxLQUFLLENBQUNiO2dCQUFNO1lBQzFEO1lBRUFSLE9BQU9FLFFBQVEsQ0FBQ3VCLE1BQU0sQ0FBQzNCLE9BQU8sTUFBTVk7UUFDdEMsT0FBTztZQUNMQSxRQUFRO2dCQUFDZDthQUFLO1FBQ2hCO1FBRUEsT0FBT0UsUUFBUVksTUFBTWhCLE1BQU07SUFDN0I7QUFDRjtBQUVBOzs7Ozs7O0NBT0MsR0FDRCxTQUFTRixRQUFRa0MsV0FBVztJQUMxQixrQkFBa0IsR0FDbEIsTUFBTUMsU0FBUyxFQUFFO0lBRWpCLElBQUksQ0FBQ0wsTUFBTUMsT0FBTyxDQUFDRyxjQUFjO1FBQy9CLE1BQU0sSUFBSUUsVUFBVTtJQUN0QjtJQUVBLCtCQUErQixHQUMvQiw2QkFBNkI7SUFDN0IsTUFBTTFDLE9BQ0osQ0FBQ3dDLFdBQVcsQ0FBQyxFQUFFLElBQUlKLE1BQU1DLE9BQU8sQ0FBQ0csV0FBVyxDQUFDLEVBQUUsSUFDM0NBLGNBQ0E7UUFBQ0E7S0FBWTtJQUVuQixJQUFJNUIsUUFBUSxDQUFDO0lBRWIsTUFBTyxFQUFFQSxRQUFRWixLQUFLUSxNQUFNLENBQUU7UUFDNUIsTUFBTW1DLFFBQVEzQyxJQUFJLENBQUNZLE1BQU07UUFDekI2QixPQUFPUCxJQUFJLENBQUM7WUFBQ1UsYUFBYUQsS0FBSyxDQUFDLEVBQUU7WUFBR0UsV0FBV0YsS0FBSyxDQUFDLEVBQUU7U0FBRTtJQUM1RDtJQUVBLE9BQU9GO0FBQ1Q7QUFFQTs7Ozs7OztDQU9DLEdBQ0QsU0FBU0csYUFBYXhCLElBQUk7SUFDeEIsT0FBTyxPQUFPQSxTQUFTLFdBQVcsSUFBSTBCLE9BQU9uRCxnRUFBTUEsQ0FBQ3lCLE9BQU8sT0FBT0E7QUFDcEU7QUFFQTs7Ozs7OztDQU9DLEdBQ0QsU0FBU3lCLFdBQVd4QixPQUFPO0lBQ3pCLE9BQU8sT0FBT0EsWUFBWSxhQUN0QkEsVUFDQTtRQUNFLE9BQU9BO0lBQ1Q7QUFDTiIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtZmluZC1hbmQtcmVwbGFjZS9saWIvaW5kZXguanM/NmFkYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge05vZGVzLCBQYXJlbnRzLCBQaHJhc2luZ0NvbnRlbnQsIFJvb3QsIFRleHR9IGZyb20gJ21kYXN0J1xuICogQGltcG9ydCB7QnVpbGRWaXNpdG9yLCBUZXN0LCBWaXNpdG9yUmVzdWx0fSBmcm9tICd1bmlzdC11dGlsLXZpc2l0LXBhcmVudHMnXG4gKi9cblxuLyoqXG4gKiBAdHlwZWRlZiBSZWdFeHBNYXRjaE9iamVjdFxuICogICBJbmZvIG9uIHRoZSBtYXRjaC5cbiAqIEBwcm9wZXJ0eSB7bnVtYmVyfSBpbmRleFxuICogICBUaGUgaW5kZXggb2YgdGhlIHNlYXJjaCBhdCB3aGljaCB0aGUgcmVzdWx0IHdhcyBmb3VuZC5cbiAqIEBwcm9wZXJ0eSB7c3RyaW5nfSBpbnB1dFxuICogICBBIGNvcHkgb2YgdGhlIHNlYXJjaCBzdHJpbmcgaW4gdGhlIHRleHQgbm9kZS5cbiAqIEBwcm9wZXJ0eSB7Wy4uLkFycmF5PFBhcmVudHM+LCBUZXh0XX0gc3RhY2tcbiAqICAgQWxsIGFuY2VzdG9ycyBvZiB0aGUgdGV4dCBub2RlLCB3aGVyZSB0aGUgbGFzdCBub2RlIGlzIHRoZSB0ZXh0IGl0c2VsZi5cbiAqXG4gKiBAdHlwZWRlZiB7UmVnRXhwIHwgc3RyaW5nfSBGaW5kXG4gKiAgIFBhdHRlcm4gdG8gZmluZC5cbiAqXG4gKiAgIFN0cmluZ3MgYXJlIGVzY2FwZWQgYW5kIHRoZW4gdHVybmVkIGludG8gZ2xvYmFsIGV4cHJlc3Npb25zLlxuICpcbiAqIEB0eXBlZGVmIHtBcnJheTxGaW5kQW5kUmVwbGFjZVR1cGxlPn0gRmluZEFuZFJlcGxhY2VMaXN0XG4gKiAgIFNldmVyYWwgZmluZCBhbmQgcmVwbGFjZXMsIGluIGFycmF5IGZvcm0uXG4gKlxuICogQHR5cGVkZWYge1tGaW5kLCBSZXBsYWNlP119IEZpbmRBbmRSZXBsYWNlVHVwbGVcbiAqICAgRmluZCBhbmQgcmVwbGFjZSBpbiB0dXBsZSBmb3JtLlxuICpcbiAqIEB0eXBlZGVmIHtSZXBsYWNlRnVuY3Rpb24gfCBzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSBSZXBsYWNlXG4gKiAgIFRoaW5nIHRvIHJlcGxhY2Ugd2l0aC5cbiAqXG4gKiBAY2FsbGJhY2sgUmVwbGFjZUZ1bmN0aW9uXG4gKiAgIENhbGxiYWNrIGNhbGxlZCB3aGVuIGEgc2VhcmNoIG1hdGNoZXMuXG4gKiBAcGFyYW0gey4uLmFueX0gcGFyYW1ldGVyc1xuICogICBUaGUgcGFyYW1ldGVycyBhcmUgdGhlIHJlc3VsdCBvZiBjb3JyZXNwb25kaW5nIHNlYXJjaCBleHByZXNzaW9uOlxuICpcbiAqICAgKiBgdmFsdWVgIChgc3RyaW5nYCkg4oCUIHdob2xlIG1hdGNoXG4gKiAgICogYC4uLmNhcHR1cmVgIChgQXJyYXk8c3RyaW5nPmApIOKAlCBtYXRjaGVzIGZyb20gcmVnZXggY2FwdHVyZSBncm91cHNcbiAqICAgKiBgbWF0Y2hgIChgUmVnRXhwTWF0Y2hPYmplY3RgKSDigJQgaW5mbyBvbiB0aGUgbWF0Y2hcbiAqIEByZXR1cm5zIHtBcnJheTxQaHJhc2luZ0NvbnRlbnQ+IHwgUGhyYXNpbmdDb250ZW50IHwgc3RyaW5nIHwgZmFsc2UgfCBudWxsIHwgdW5kZWZpbmVkfVxuICogICBUaGluZyB0byByZXBsYWNlIHdpdGguXG4gKlxuICogICAqIHdoZW4gYG51bGxgLCBgdW5kZWZpbmVkYCwgYCcnYCwgcmVtb3ZlIHRoZSBtYXRjaFxuICogICAqIOKApm9yIHdoZW4gYGZhbHNlYCwgZG8gbm90IHJlcGxhY2UgYXQgYWxsXG4gKiAgICog4oCmb3Igd2hlbiBgc3RyaW5nYCwgcmVwbGFjZSB3aXRoIGEgdGV4dCBub2RlIG9mIHRoYXQgdmFsdWVcbiAqICAgKiDigKZvciB3aGVuIGBOb2RlYCBvciBgQXJyYXk8Tm9kZT5gLCByZXBsYWNlIHdpdGggdGhvc2Ugbm9kZXNcbiAqXG4gKiBAdHlwZWRlZiB7W1JlZ0V4cCwgUmVwbGFjZUZ1bmN0aW9uXX0gUGFpclxuICogICBOb3JtYWxpemVkIGZpbmQgYW5kIHJlcGxhY2UuXG4gKlxuICogQHR5cGVkZWYge0FycmF5PFBhaXI+fSBQYWlyc1xuICogICBBbGwgZmluZCBhbmQgcmVwbGFjZWQuXG4gKlxuICogQHR5cGVkZWYgT3B0aW9uc1xuICogICBDb25maWd1cmF0aW9uLlxuICogQHByb3BlcnR5IHtUZXN0IHwgbnVsbCB8IHVuZGVmaW5lZH0gW2lnbm9yZV1cbiAqICAgVGVzdCBmb3Igd2hpY2ggbm9kZXMgdG8gaWdub3JlIChvcHRpb25hbCkuXG4gKi9cblxuaW1wb3J0IGVzY2FwZSBmcm9tICdlc2NhcGUtc3RyaW5nLXJlZ2V4cCdcbmltcG9ydCB7dmlzaXRQYXJlbnRzfSBmcm9tICd1bmlzdC11dGlsLXZpc2l0LXBhcmVudHMnXG5pbXBvcnQge2NvbnZlcnR9IGZyb20gJ3VuaXN0LXV0aWwtaXMnXG5cbi8qKlxuICogRmluZCBwYXR0ZXJucyBpbiBhIHRyZWUgYW5kIHJlcGxhY2UgdGhlbS5cbiAqXG4gKiBUaGUgYWxnb3JpdGhtIHNlYXJjaGVzIHRoZSB0cmVlIGluICpwcmVvcmRlciogZm9yIGNvbXBsZXRlIHZhbHVlcyBpbiBgVGV4dGBcbiAqIG5vZGVzLlxuICogUGFydGlhbCBtYXRjaGVzIGFyZSBub3Qgc3VwcG9ydGVkLlxuICpcbiAqIEBwYXJhbSB7Tm9kZXN9IHRyZWVcbiAqICAgVHJlZSB0byBjaGFuZ2UuXG4gKiBAcGFyYW0ge0ZpbmRBbmRSZXBsYWNlTGlzdCB8IEZpbmRBbmRSZXBsYWNlVHVwbGV9IGxpc3RcbiAqICAgUGF0dGVybnMgdG8gZmluZC5cbiAqIEBwYXJhbSB7T3B0aW9ucyB8IG51bGwgfCB1bmRlZmluZWR9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uICh3aGVuIGBmaW5kYCBpcyBub3QgYEZpbmRgKS5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmaW5kQW5kUmVwbGFjZSh0cmVlLCBsaXN0LCBvcHRpb25zKSB7XG4gIGNvbnN0IHNldHRpbmdzID0gb3B0aW9ucyB8fCB7fVxuICBjb25zdCBpZ25vcmVkID0gY29udmVydChzZXR0aW5ncy5pZ25vcmUgfHwgW10pXG4gIGNvbnN0IHBhaXJzID0gdG9QYWlycyhsaXN0KVxuICBsZXQgcGFpckluZGV4ID0gLTFcblxuICB3aGlsZSAoKytwYWlySW5kZXggPCBwYWlycy5sZW5ndGgpIHtcbiAgICB2aXNpdFBhcmVudHModHJlZSwgJ3RleHQnLCB2aXNpdG9yKVxuICB9XG5cbiAgLyoqIEB0eXBlIHtCdWlsZFZpc2l0b3I8Um9vdCwgJ3RleHQnPn0gKi9cbiAgZnVuY3Rpb24gdmlzaXRvcihub2RlLCBwYXJlbnRzKSB7XG4gICAgbGV0IGluZGV4ID0gLTFcbiAgICAvKiogQHR5cGUge1BhcmVudHMgfCB1bmRlZmluZWR9ICovXG4gICAgbGV0IGdyYW5kcGFyZW50XG5cbiAgICB3aGlsZSAoKytpbmRleCA8IHBhcmVudHMubGVuZ3RoKSB7XG4gICAgICBjb25zdCBwYXJlbnQgPSBwYXJlbnRzW2luZGV4XVxuICAgICAgLyoqIEB0eXBlIHtBcnJheTxOb2Rlcz4gfCB1bmRlZmluZWR9ICovXG4gICAgICBjb25zdCBzaWJsaW5ncyA9IGdyYW5kcGFyZW50ID8gZ3JhbmRwYXJlbnQuY2hpbGRyZW4gOiB1bmRlZmluZWRcblxuICAgICAgaWYgKFxuICAgICAgICBpZ25vcmVkKFxuICAgICAgICAgIHBhcmVudCxcbiAgICAgICAgICBzaWJsaW5ncyA/IHNpYmxpbmdzLmluZGV4T2YocGFyZW50KSA6IHVuZGVmaW5lZCxcbiAgICAgICAgICBncmFuZHBhcmVudFxuICAgICAgICApXG4gICAgICApIHtcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGdyYW5kcGFyZW50ID0gcGFyZW50XG4gICAgfVxuXG4gICAgaWYgKGdyYW5kcGFyZW50KSB7XG4gICAgICByZXR1cm4gaGFuZGxlcihub2RlLCBwYXJlbnRzKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBIYW5kbGUgYSB0ZXh0IG5vZGUgd2hpY2ggaXMgbm90IGluIGFuIGlnbm9yZWQgcGFyZW50LlxuICAgKlxuICAgKiBAcGFyYW0ge1RleHR9IG5vZGVcbiAgICogICBUZXh0IG5vZGUuXG4gICAqIEBwYXJhbSB7QXJyYXk8UGFyZW50cz59IHBhcmVudHNcbiAgICogICBQYXJlbnRzLlxuICAgKiBAcmV0dXJucyB7VmlzaXRvclJlc3VsdH1cbiAgICogICBSZXN1bHQuXG4gICAqL1xuICBmdW5jdGlvbiBoYW5kbGVyKG5vZGUsIHBhcmVudHMpIHtcbiAgICBjb25zdCBwYXJlbnQgPSBwYXJlbnRzW3BhcmVudHMubGVuZ3RoIC0gMV1cbiAgICBjb25zdCBmaW5kID0gcGFpcnNbcGFpckluZGV4XVswXVxuICAgIGNvbnN0IHJlcGxhY2UgPSBwYWlyc1twYWlySW5kZXhdWzFdXG4gICAgbGV0IHN0YXJ0ID0gMFxuICAgIC8qKiBAdHlwZSB7QXJyYXk8Tm9kZXM+fSAqL1xuICAgIGNvbnN0IHNpYmxpbmdzID0gcGFyZW50LmNoaWxkcmVuXG4gICAgY29uc3QgaW5kZXggPSBzaWJsaW5ncy5pbmRleE9mKG5vZGUpXG4gICAgbGV0IGNoYW5nZSA9IGZhbHNlXG4gICAgLyoqIEB0eXBlIHtBcnJheTxQaHJhc2luZ0NvbnRlbnQ+fSAqL1xuICAgIGxldCBub2RlcyA9IFtdXG5cbiAgICBmaW5kLmxhc3RJbmRleCA9IDBcblxuICAgIGxldCBtYXRjaCA9IGZpbmQuZXhlYyhub2RlLnZhbHVlKVxuXG4gICAgd2hpbGUgKG1hdGNoKSB7XG4gICAgICBjb25zdCBwb3NpdGlvbiA9IG1hdGNoLmluZGV4XG4gICAgICAvKiogQHR5cGUge1JlZ0V4cE1hdGNoT2JqZWN0fSAqL1xuICAgICAgY29uc3QgbWF0Y2hPYmplY3QgPSB7XG4gICAgICAgIGluZGV4OiBtYXRjaC5pbmRleCxcbiAgICAgICAgaW5wdXQ6IG1hdGNoLmlucHV0LFxuICAgICAgICBzdGFjazogWy4uLnBhcmVudHMsIG5vZGVdXG4gICAgICB9XG4gICAgICBsZXQgdmFsdWUgPSByZXBsYWNlKC4uLm1hdGNoLCBtYXRjaE9iamVjdClcblxuICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgdmFsdWUgPSB2YWx1ZS5sZW5ndGggPiAwID8ge3R5cGU6ICd0ZXh0JywgdmFsdWV9IDogdW5kZWZpbmVkXG4gICAgICB9XG5cbiAgICAgIC8vIEl0IHdhc27igJl0IGEgbWF0Y2ggYWZ0ZXIgYWxsLlxuICAgICAgaWYgKHZhbHVlID09PSBmYWxzZSkge1xuICAgICAgICAvLyBGYWxzZSBhY3RzIGFzIGlmIHRoZXJlIHdhcyBubyBtYXRjaC5cbiAgICAgICAgLy8gU28gd2UgbmVlZCB0byByZXNldCBgbGFzdEluZGV4YCwgd2hpY2ggY3VycmVudGx5IGJlaW5nIGF0IHRoZSBlbmQgb2ZcbiAgICAgICAgLy8gdGhlIGN1cnJlbnQgbWF0Y2gsIHRvIHRoZSBiZWdpbm5pbmcuXG4gICAgICAgIGZpbmQubGFzdEluZGV4ID0gcG9zaXRpb24gKyAxXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpZiAoc3RhcnQgIT09IHBvc2l0aW9uKSB7XG4gICAgICAgICAgbm9kZXMucHVzaCh7XG4gICAgICAgICAgICB0eXBlOiAndGV4dCcsXG4gICAgICAgICAgICB2YWx1ZTogbm9kZS52YWx1ZS5zbGljZShzdGFydCwgcG9zaXRpb24pXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICAgIG5vZGVzLnB1c2goLi4udmFsdWUpXG4gICAgICAgIH0gZWxzZSBpZiAodmFsdWUpIHtcbiAgICAgICAgICBub2Rlcy5wdXNoKHZhbHVlKVxuICAgICAgICB9XG5cbiAgICAgICAgc3RhcnQgPSBwb3NpdGlvbiArIG1hdGNoWzBdLmxlbmd0aFxuICAgICAgICBjaGFuZ2UgPSB0cnVlXG4gICAgICB9XG5cbiAgICAgIGlmICghZmluZC5nbG9iYWwpIHtcbiAgICAgICAgYnJlYWtcbiAgICAgIH1cblxuICAgICAgbWF0Y2ggPSBmaW5kLmV4ZWMobm9kZS52YWx1ZSlcbiAgICB9XG5cbiAgICBpZiAoY2hhbmdlKSB7XG4gICAgICBpZiAoc3RhcnQgPCBub2RlLnZhbHVlLmxlbmd0aCkge1xuICAgICAgICBub2Rlcy5wdXNoKHt0eXBlOiAndGV4dCcsIHZhbHVlOiBub2RlLnZhbHVlLnNsaWNlKHN0YXJ0KX0pXG4gICAgICB9XG5cbiAgICAgIHBhcmVudC5jaGlsZHJlbi5zcGxpY2UoaW5kZXgsIDEsIC4uLm5vZGVzKVxuICAgIH0gZWxzZSB7XG4gICAgICBub2RlcyA9IFtub2RlXVxuICAgIH1cblxuICAgIHJldHVybiBpbmRleCArIG5vZGVzLmxlbmd0aFxuICB9XG59XG5cbi8qKlxuICogVHVybiBhIHR1cGxlIG9yIGEgbGlzdCBvZiB0dXBsZXMgaW50byBwYWlycy5cbiAqXG4gKiBAcGFyYW0ge0ZpbmRBbmRSZXBsYWNlTGlzdCB8IEZpbmRBbmRSZXBsYWNlVHVwbGV9IHR1cGxlT3JMaXN0XG4gKiAgIFNjaGVtYS5cbiAqIEByZXR1cm5zIHtQYWlyc31cbiAqICAgQ2xlYW4gcGFpcnMuXG4gKi9cbmZ1bmN0aW9uIHRvUGFpcnModHVwbGVPckxpc3QpIHtcbiAgLyoqIEB0eXBlIHtQYWlyc30gKi9cbiAgY29uc3QgcmVzdWx0ID0gW11cblxuICBpZiAoIUFycmF5LmlzQXJyYXkodHVwbGVPckxpc3QpKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcignRXhwZWN0ZWQgZmluZCBhbmQgcmVwbGFjZSB0dXBsZSBvciBsaXN0IG9mIHR1cGxlcycpXG4gIH1cblxuICAvKiogQHR5cGUge0ZpbmRBbmRSZXBsYWNlTGlzdH0gKi9cbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogY29ycmVjdC5cbiAgY29uc3QgbGlzdCA9XG4gICAgIXR1cGxlT3JMaXN0WzBdIHx8IEFycmF5LmlzQXJyYXkodHVwbGVPckxpc3RbMF0pXG4gICAgICA/IHR1cGxlT3JMaXN0XG4gICAgICA6IFt0dXBsZU9yTGlzdF1cblxuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgbGlzdC5sZW5ndGgpIHtcbiAgICBjb25zdCB0dXBsZSA9IGxpc3RbaW5kZXhdXG4gICAgcmVzdWx0LnB1c2goW3RvRXhwcmVzc2lvbih0dXBsZVswXSksIHRvRnVuY3Rpb24odHVwbGVbMV0pXSlcbiAgfVxuXG4gIHJldHVybiByZXN1bHRcbn1cblxuLyoqXG4gKiBUdXJuIGEgZmluZCBpbnRvIGFuIGV4cHJlc3Npb24uXG4gKlxuICogQHBhcmFtIHtGaW5kfSBmaW5kXG4gKiAgIEZpbmQuXG4gKiBAcmV0dXJucyB7UmVnRXhwfVxuICogICBFeHByZXNzaW9uLlxuICovXG5mdW5jdGlvbiB0b0V4cHJlc3Npb24oZmluZCkge1xuICByZXR1cm4gdHlwZW9mIGZpbmQgPT09ICdzdHJpbmcnID8gbmV3IFJlZ0V4cChlc2NhcGUoZmluZCksICdnJykgOiBmaW5kXG59XG5cbi8qKlxuICogVHVybiBhIHJlcGxhY2UgaW50byBhIGZ1bmN0aW9uLlxuICpcbiAqIEBwYXJhbSB7UmVwbGFjZX0gcmVwbGFjZVxuICogICBSZXBsYWNlLlxuICogQHJldHVybnMge1JlcGxhY2VGdW5jdGlvbn1cbiAqICAgRnVuY3Rpb24uXG4gKi9cbmZ1bmN0aW9uIHRvRnVuY3Rpb24ocmVwbGFjZSkge1xuICByZXR1cm4gdHlwZW9mIHJlcGxhY2UgPT09ICdmdW5jdGlvbidcbiAgICA/IHJlcGxhY2VcbiAgICA6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcGxhY2VcbiAgICAgIH1cbn1cbiJdLCJuYW1lcyI6WyJlc2NhcGUiLCJ2aXNpdFBhcmVudHMiLCJjb252ZXJ0IiwiZmluZEFuZFJlcGxhY2UiLCJ0cmVlIiwibGlzdCIsIm9wdGlvbnMiLCJzZXR0aW5ncyIsImlnbm9yZWQiLCJpZ25vcmUiLCJwYWlycyIsInRvUGFpcnMiLCJwYWlySW5kZXgiLCJsZW5ndGgiLCJ2aXNpdG9yIiwibm9kZSIsInBhcmVudHMiLCJpbmRleCIsImdyYW5kcGFyZW50IiwicGFyZW50Iiwic2libGluZ3MiLCJjaGlsZHJlbiIsInVuZGVmaW5lZCIsImluZGV4T2YiLCJoYW5kbGVyIiwiZmluZCIsInJlcGxhY2UiLCJzdGFydCIsImNoYW5nZSIsIm5vZGVzIiwibGFzdEluZGV4IiwibWF0Y2giLCJleGVjIiwidmFsdWUiLCJwb3NpdGlvbiIsIm1hdGNoT2JqZWN0IiwiaW5wdXQiLCJzdGFjayIsInR5cGUiLCJwdXNoIiwic2xpY2UiLCJBcnJheSIsImlzQXJyYXkiLCJnbG9iYWwiLCJzcGxpY2UiLCJ0dXBsZU9yTGlzdCIsInJlc3VsdCIsIlR5cGVFcnJvciIsInR1cGxlIiwidG9FeHByZXNzaW9uIiwidG9GdW5jdGlvbiIsIlJlZ0V4cCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/mdast-util-find-and-replace/lib/index.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp/index.js":
/*!**********************************************************************************************!*\
  !*** ../node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp/index.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ escapeStringRegexp)\n/* harmony export */ });\nfunction escapeStringRegexp(string) {\n    if (typeof string !== \"string\") {\n        throw new TypeError(\"Expected a string\");\n    }\n    // Escape characters with special meaning either inside or outside character sets.\n    // Use a simple backslash escape when it’s always valid, and a `\\xnn` escape when the simpler form would be disallowed by Unicode patterns’ stricter grammar.\n    return string.replace(/[|\\\\{}()[\\]^$+*?.]/g, \"\\\\$&\").replace(/-/g, \"\\\\x2d\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtZmluZC1hbmQtcmVwbGFjZS9ub2RlX21vZHVsZXMvZXNjYXBlLXN0cmluZy1yZWdleHAvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLG1CQUFtQkMsTUFBTTtJQUNoRCxJQUFJLE9BQU9BLFdBQVcsVUFBVTtRQUMvQixNQUFNLElBQUlDLFVBQVU7SUFDckI7SUFFQSxrRkFBa0Y7SUFDbEYsNkpBQTZKO0lBQzdKLE9BQU9ELE9BQ0xFLE9BQU8sQ0FBQyx1QkFBdUIsUUFDL0JBLE9BQU8sQ0FBQyxNQUFNO0FBQ2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGMtbWFsbC1mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1maW5kLWFuZC1yZXBsYWNlL25vZGVfbW9kdWxlcy9lc2NhcGUtc3RyaW5nLXJlZ2V4cC9pbmRleC5qcz84MzBhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGVzY2FwZVN0cmluZ1JlZ2V4cChzdHJpbmcpIHtcblx0aWYgKHR5cGVvZiBzdHJpbmcgIT09ICdzdHJpbmcnKSB7XG5cdFx0dGhyb3cgbmV3IFR5cGVFcnJvcignRXhwZWN0ZWQgYSBzdHJpbmcnKTtcblx0fVxuXG5cdC8vIEVzY2FwZSBjaGFyYWN0ZXJzIHdpdGggc3BlY2lhbCBtZWFuaW5nIGVpdGhlciBpbnNpZGUgb3Igb3V0c2lkZSBjaGFyYWN0ZXIgc2V0cy5cblx0Ly8gVXNlIGEgc2ltcGxlIGJhY2tzbGFzaCBlc2NhcGUgd2hlbiBpdOKAmXMgYWx3YXlzIHZhbGlkLCBhbmQgYSBgXFx4bm5gIGVzY2FwZSB3aGVuIHRoZSBzaW1wbGVyIGZvcm0gd291bGQgYmUgZGlzYWxsb3dlZCBieSBVbmljb2RlIHBhdHRlcm5z4oCZIHN0cmljdGVyIGdyYW1tYXIuXG5cdHJldHVybiBzdHJpbmdcblx0XHQucmVwbGFjZSgvW3xcXFxce30oKVtcXF1eJCsqPy5dL2csICdcXFxcJCYnKVxuXHRcdC5yZXBsYWNlKC8tL2csICdcXFxceDJkJyk7XG59XG4iXSwibmFtZXMiOlsiZXNjYXBlU3RyaW5nUmVnZXhwIiwic3RyaW5nIiwiVHlwZUVycm9yIiwicmVwbGFjZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp/index.js\n");

/***/ })

};
;
// frontend/src/components/news/NewsListClient.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import NewsCard from './NewsCard';  
import CategoryFilter from './CategoryFilter';  
import Pagination from '@/components/ui/Pagination'; 
import { NewsArticle, PaginatedNewsResponse, NewsCategory } from '@/shared/types/News';

interface NewsListClientProps {
  initialPage?: number;
  initialCategory?: string;
  availableCategories: NewsCategory[];
}

const ITEMS_PER_PAGE = 10;

export default function NewsListClient({
  initialPage = 1,
  initialCategory = 'all',
  availableCategories,
}: NewsListClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchNews = useCallback(async (page: number, category: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', ITEMS_PER_PAGE.toString());
      if (category && category !== 'all') {
        params.append('category', category);
      }
      
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || '/api'; // Use relative path for client-side API calls
      const res = await fetch(`${apiUrl}/news?${params.toString()}`);
      if (!res.ok) {
        throw new Error('Failed to fetch news');
      }
      const data: PaginatedNewsResponse = await res.json();
      setArticles(data.articles);
      setTotalPages(data.totalPages);
      setCurrentPage(data.currentPage); // Ensure current page is updated from response
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setArticles([]); // Clear articles on error
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Update URL when page or category changes by user interaction
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', currentPage.toString());
    params.set('category', selectedCategory);
    // router.push(`/news?${params.toString()}`, { scroll: false }); // This might cause re-fetch if not careful
    // Instead, let's rely on initial load and direct fetchNews calls for updates.
    // The parent page.tsx handles initial params.
  }, [currentPage, selectedCategory, router, searchParams]);


  useEffect(() => {
    // Fetch news when component mounts or when page/category changes from URL (e.g. browser back/forward)
    const pageFromUrl = parseInt(searchParams.get('page') || '1');
    const categoryFromUrl = searchParams.get('category') || 'all';
    
    setCurrentPage(pageFromUrl);
    setSelectedCategory(categoryFromUrl);
    fetchNews(pageFromUrl, categoryFromUrl);
  }, [searchParams, fetchNews]);


  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateUrl(page, selectedCategory);
    fetchNews(page, selectedCategory); // Fetch new data for the page
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page on category change
    updateUrl(1, category);
    fetchNews(1, category); // Fetch new data for the category
  };
  
  const updateUrl = (page: number, category: string) => {
    const params = new URLSearchParams();
    params.set('page', page.toString());
    params.set('category', category);
    router.push(`/news?${params.toString()}`, { scroll: false });
  };
  if (isLoading) {
    return (
      <div>
        <CategoryFilter
          categories={availableCategories}
          selectedCategory={selectedCategory}
          onSelectCategory={() => {}}
        />
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mt-8">
          <div className="space-y-0">
            {Array.from({ length: 8 }).map((_, index) => (
              <div
                key={index}
                className={`px-6 py-4 animate-pulse ${
                  index !== 7 ? 'border-b border-gray-100' : ''
                }`}
              >
                <div className="flex items-center justify-between gap-4 md:gap-6">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="h-5 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-12"></div>
                    </div>
                    <div className="hidden md:block h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="flex gap-2">
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                      <div className="h-3 bg-gray-200 rounded w-20"></div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="h-4 bg-gray-200 rounded w-20 mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-12"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="text-center py-10 text-red-500">错误: {error}</div>;
  }

  return (
    <div>
      <CategoryFilter
        categories={availableCategories}
        selectedCategory={selectedCategory}
        onSelectCategory={handleCategoryChange}
      />      {articles.length > 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mt-8 overflow-hidden">
          <div className="space-y-0">
            {articles.map((article, index) => (
              <div
                key={article.id}
                className={`${
                  index !== articles.length - 1 ? 'border-b border-gray-100' : ''
                }`}
              >
                <NewsCard article={article} />
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mt-8">
          <p className="text-center py-16 text-gray-500 text-lg">暂无新闻</p>
        </div>
      )}      {totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 px-6 py-4">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      )}
    </div>
  );
}

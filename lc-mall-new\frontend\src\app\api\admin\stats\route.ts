import { NextRequest, NextResponse } from 'next/server';
import { getAdminHeaders } from '@/utils/adminAuth';

const API_BASE = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, '') || 'http://localhost:5000/api';

// Get dashboard statistics
export async function GET(request: NextRequest) {
  try {
    // 获取产品统计 - 使用更大的limit来确保获取准确的总数
    const productsResponse = await fetch(`${API_BASE}/products?page=1&limit=100`, {
      method: 'GET',
      headers: getAdminHeaders()
    });

    // 获取所有新闻文章（用于计算总访问量）
    const newsResponse = await fetch(`${API_BASE}/news?page=1&limit=999`, {
      method: 'GET',
      headers: getAdminHeaders()
    });

    let productCount = 0;
    let newsCount = 0;
    let totalViews = 0;

    // 处理产品数据
    if (productsResponse.ok) {
      const productData = await productsResponse.json();
      console.log('Product API response: success =', productData.success, ', products count =', productData.data?.products?.length || 0);

      if (productData.success && productData.data) {
        if (productData.data.pagination) {
          productCount = productData.data.pagination.total || 0;
        } else if (productData.data.products) {
          // 如果没有分页信息，使用产品数组长度
          productCount = productData.data.products.length || 0;
        }
      }
    } else {
      console.error('Products API failed:', productsResponse.status, productsResponse.statusText);
    }

    // 处理新闻数据
    if (newsResponse.ok) {
      const newsData = await newsResponse.json();
      if (newsData.success && newsData.data) {
        // 获取文章总数
        if (newsData.data.pagination) {
          newsCount = newsData.data.pagination.total || 0;
        } else if (newsData.data.articles) {
          newsCount = newsData.data.articles.length || 0;
        }

        // 计算总访问量（所有文章的浏览量总和）
        if (newsData.data.articles && Array.isArray(newsData.data.articles)) {
          totalViews = newsData.data.articles.reduce((sum: number, article: any) => {
            return sum + (article.views || 0);
          }, 0);
        }
      }
    } else {
      console.error('News API failed:', newsResponse.status, newsResponse.statusText);
    }

    // 返回统计数据
    const stats = {
      productCount: productCount || 0,
      newsCount: newsCount || 0,
      totalViews: totalViews || 0,
      systemStatus: productCount > 0 ? 'normal' : 'warning'
    };

    console.log('Final stats:', stats);
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);

    // 返回默认统计数据
    return NextResponse.json({
      productCount: 0,
      newsCount: 0,
      totalViews: 0,
      systemStatus: 'error'
    });
  }
}

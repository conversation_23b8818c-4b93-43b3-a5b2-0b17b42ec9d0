#!/bin/bash
# API 404 问题诊断脚本

echo "=========================================="
echo "     龙驰新材料商城 - API问题诊断      "
echo "=========================================="

echo "1. 检查服务进程状态..."
echo "后端进程:"
ps aux | grep -v grep | grep "node.*server.js" || echo "  ❌ 后端进程未运行"

echo "前端进程:"
ps aux | grep -v grep | grep "next start" || echo "  ❌ 前端进程未运行"

echo ""
echo "2. 检查端口监听状态..."
echo "3000端口 (前端):"
netstat -tlnp | grep ":3000" || echo "  ❌ 3000端口未监听"

echo "5000端口 (后端):"
netstat -tlnp | grep ":5000" || echo "  ❌ 5000端口未监听"

echo ""
echo "3. 检查服务响应..."
echo "后端健康检查 (localhost:5000/health):"
if curl -f -s http://localhost:5000/health > /dev/null; then
    echo "  ✅ 后端健康检查通过"
    curl -s http://localhost:5000/health | jq . 2>/dev/null || curl -s http://localhost:5000/health
else
    echo "  ❌ 后端健康检查失败"
fi

echo ""
echo "前端首页 (localhost:3000):"
if curl -f -s http://localhost:3000 > /dev/null; then
    echo "  ✅ 前端页面可访问"
else
    echo "  ❌ 前端页面无法访问"
fi

echo ""
echo "4. 检查API路由..."
echo "测试产品API (localhost:5000/api/products):"
if curl -f -s http://localhost:5000/api/products > /dev/null; then
    echo "  ✅ 产品API可访问"
else
    echo "  ❌ 产品API无法访问"
    echo "  响应内容:"
    curl -s http://localhost:5000/api/products | head -5
fi

echo ""
echo "测试管理统计API (localhost:3000/api/admin/stats):"
if curl -f -s http://localhost:3000/api/admin/stats > /dev/null; then
    echo "  ✅ 前端管理API可访问"
else
    echo "  ❌ 前端管理API无法访问"
    echo "  响应内容:"
    curl -s http://localhost:3000/api/admin/stats | head -5
fi

echo ""
echo "5. 检查环境配置..."
echo "生产环境文件:"
if [ -f ".env.production" ]; then
    echo "  ✅ .env.production 存在"
    echo "  后端API地址: $(grep NEXT_PUBLIC_API_URL .env.production)"
else
    echo "  ❌ .env.production 不存在"
fi

echo ""
echo "6. 检查日志文件..."
if [ -f "logs/backend.log" ]; then
    echo "后端日志最后10行:"
    tail -10 logs/backend.log
else
    echo "  ❌ 后端日志文件不存在"
fi

echo ""
if [ -f "logs/frontend.log" ]; then
    echo "前端日志最后10行:"
    tail -10 logs/frontend.log
else
    echo "  ❌ 前端日志文件不存在"
fi

echo ""
echo "=========================================="
echo "诊断完成！请根据以上信息排查问题。"
echo "=========================================="

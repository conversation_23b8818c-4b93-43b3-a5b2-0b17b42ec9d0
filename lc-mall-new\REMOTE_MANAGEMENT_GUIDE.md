# 龙驰商城远程服务器管理指南

## 📋 脚本使用说明

你现在有4个远程管理脚本，请按以下步骤操作：

### 🔧 1. 上传脚本到云服务器

将以下脚本上传到你的云服务器项目根目录：
- `remote-diagnose.sh` - 全面诊断工具
- `remote-fix.sh` - 快速修复工具  
- `remote-api-test.sh` - API接口测试工具

### 🚀 2. 添加执行权限

SSH连接到云服务器后，在项目根目录执行：
```bash
chmod +x remote-diagnose.sh remote-fix.sh remote-api-test.sh
```

### 🔍 3. 使用流程

#### 步骤1: 诊断当前状态
```bash
./remote-diagnose.sh
```
这个脚本会检查：
- ✅ 系统信息和环境
- ✅ Node.js/NPM版本
- ✅ 进程运行状态
- ✅ 端口监听情况
- ✅ 服务响应测试
- ✅ 日志文件分析
- ✅ Nginx和Redis状态
- ✅ API接口连通性

#### 步骤2: 如果有问题，运行修复
```bash
./remote-fix.sh
```
这个脚本会：
- 🛑 停止所有相关进程
- 🧹 清理端口占用
- 📦 重新安装依赖（如需要）
- 🔧 重新构建前端
- 🚀 重新启动前后端服务
- 🔄 重新加载Nginx配置
- ✅ 测试所有API接口

#### 步骤3: 详细测试API
```bash
./remote-api-test.sh
```
这个脚本会：
- 🌐 测试所有API端点
- ⏱️ 检查响应时间
- 📊 生成测试报告
- 🔍 分析性能指标

### 📧 4. 需要我远程协助时，请提供：

1. **诊断结果**：运行 `./remote-diagnose.sh` 的完整输出
2. **服务器信息**：
   - 云服务器提供商（阿里云/腾讯云/AWS等）
   - 服务器配置（CPU/内存/带宽）
   - 操作系统版本
3. **域名配置**：
   - 域名DNS指向
   - SSL证书配置情况
4. **当前状态**：
   - 最后一次 `./deploy.sh` 的执行时间
   - 是否有报错信息
   - 具体的API 404错误URL

### 🔧 5. 常见问题解决方案

#### 问题1: 端口被占用
```bash
./kill_ports.sh
# 然后重新部署
./deploy.sh
```

#### 问题2: 前端构建失败
```bash
cd frontend
rm -rf node_modules .next package-lock.json
npm install
npm run build
```

#### 问题3: 后端API无响应
```bash
# 检查后端日志
tail -f logs/backend.log

# 重启后端
cd backend
npm start
```

#### 问题4: Nginx配置问题
```bash
# 检查配置
sudo nginx -t

# 重新加载
sudo nginx -s reload
```

### 📞 6. 如需直接协助

如果脚本无法解决问题，请提供：

1. **SSH访问方式**：
   - 服务器IP地址
   - SSH端口（默认22）
   - 用户名和密码/密钥
   
2. **或者提供运行结果**：
   - `./remote-diagnose.sh` 的完整输出
   - `./remote-api-test.sh` 的测试结果
   - 相关错误日志内容

这样我就能为你提供精确的解决方案！

### 🎯 7. 预期正常状态

所有脚本运行正常后，你应该看到：
- ✅ 前端: http://gdlongchi.cn (通过Nginx代理)
- ✅ 前端直接: http://gdlongchi.cn:3000
- ✅ 后端API: http://gdlongchi.cn:5000/health
- ✅ 所有API接口返回正确响应
- ✅ 无404错误

// frontend/src/app/news/page.tsx
import React from 'react';
import NewsListClient from '@/components/news/NewsListClient';
import { Header } from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { newsCategories, NewsCategory } from '@/shared/types/News'; // Import categories

export default function NewsPage({
  searchParams,
}: {
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  const currentPage = parseInt(searchParams?.page as string) || 1;
  const currentCategory = searchParams?.category as string || 'all';
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-8 max-w-5xl">
        {/* Page Header */}
        <div className="text-center mb-8 pb-6 border-b border-gray-200">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">新闻中心</h1>
          <p className="text-gray-600 text-lg">了解龙驰新材料最新动态</p>
        </div>
        
        <NewsListClient
          initialPage={currentPage}
          initialCategory={currentCategory}
          availableCategories={newsCategories}
        />
      </main>
      <Footer />
    </div>
  );
}

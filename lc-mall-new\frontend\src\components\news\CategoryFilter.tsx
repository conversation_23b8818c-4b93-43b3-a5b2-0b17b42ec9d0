// frontend/src/components/news/CategoryFilter.tsx
'use client';

import React from 'react';
import { NewsCategory } from '@/shared/types/News';

interface CategoryFilterProps {
  categories: NewsCategory[];
  selectedCategory: string;
  onSelectCategory: (categoryId: string) => void;
}

export default function CategoryFilter({ categories, selectedCategory, onSelectCategory }: CategoryFilterProps) {
  return (
    <nav className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-8">
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => onSelectCategory('all')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 border
            ${selectedCategory === 'all' 
              ? 'bg-blue-600 text-white border-blue-600 shadow-sm' 
              : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50 hover:border-gray-400'}`}
        >
          全部新闻
        </button>
        {categories.map(category => (
          <button
            key={category.id}
            onClick={() => onSelectCategory(category.id)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 border
              ${selectedCategory === category.id 
                ? 'bg-blue-600 text-white border-blue-600 shadow-sm' 
                : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50 hover:border-gray-400'}`}
          >
            {category.name}
          </button>
        ))}
      </div>
    </nav>
  );
}

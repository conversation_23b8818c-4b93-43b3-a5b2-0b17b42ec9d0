'use client';

import React, { useState, useEffect } from 'react';
import { ProductCard } from './ProductCard';
import ProductDetailModal from './ProductDetailModal';
import { LoadingCard, LoadingState } from '@/components/ui/Loading';
import SearchService from '@/utils/searchService';
import type { Product } from '@/types/product';
import { Search } from 'lucide-react'; // 导入 Search 图标

interface ProductSectionProps {
  selectedCategory: string;
  searchQuery: string;
  onSearchQueryChange: (query: string) => void; // 添加 onSearchQueryChange
}


// 统一 API 基础地址，构建时注入
const apiBase = (process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, '') || 'http://localhost:5000/api');

const ProductSection: React.FC<ProductSectionProps> = ({ 
  selectedCategory, 
  searchQuery, 
  onSearchQueryChange 
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [sortBy, setSortBy] = useState<'default' | 'price-low' | 'price-high' | 'name'>('default');
    // Fetch products from API
  useEffect(() => {    const fetchProducts = async () => {
      try {
        setLoading(true);
        console.log('ProductSection: Fetching products...');

        // 使用前端API路由，添加缓存控制头确保获取最新数据
        const response = await fetch('/api/products?limit=50', {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          }
        });

        console.log('ProductSection: API response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('ProductSection: API error:', response.status, errorText);
          throw new Error(`Failed to fetch products: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log('ProductSection: API result:', result);

        // 检查是否有错误
        if (result.error) {
          throw new Error(result.error);
        }

        // 前端API返回的数据结构：{ products: [...], pagination: {...} }
        const data = result.products || result;
        console.log('ProductSection: Products data:', Array.isArray(data) ? data.length : 'not array', data);

        if (Array.isArray(data) && data.length > 0) {
          setProducts(data);
          setError(null);
          console.log('ProductSection: Successfully loaded', data.length, 'products');
        } else {
          console.warn('ProductSection: No products found, loading sample data');
          loadSampleProducts();
        }
      } catch (err) {
        console.error('ProductSection: Error fetching products:', err);
        setError(`Failed to load products: ${err instanceof Error ? err.message : 'Unknown error'}`);
        // Load sample data as fallback
        loadSampleProducts();
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Load sample products as fallback
  const loadSampleProducts = () => {
    const sampleProducts: Product[] = [
      {
        id: '1',
        name: 'Professional Hair Dryer Pro Max',
        category: 'hair-dryer',
        price: 299.99,
        image: '/images/products/hair-dryer-1.jpg',
        description: '高性能专业吹风机，采用离子技术，快速干发同时保护头发健康',
        stock: 15,
        featured: true,
        specifications: 'Brand: ProStyle, Model: HD-2000, Power: 2000W, Voltage: 220V, Weight: 800g, Warranty: 2 years',
        sku: 'HD-2000-PRO',
        details: {
          model: 'HD-2000',
          appearance: 'Black/Silver',
          standard: 'CE Certified',
          usage: 'Professional hair drying',
          flashpoint: 'N/A',
          smell: 'Odorless',
          function: 'Ionic hair drying technology',
          amineValue: 'N/A',
          content: '1 unit',
          imported: 'Yes',
          density: 'N/A',
          color: 'Black',
          viscosity: 'N/A'
        }
      },
      {
        id: '2',
        name: 'Smart Home Security Camera',
        category: 'security',
        price: 149.99,
        image: '/images/products/camera-1.jpg',
        description: '1080p高清无线安全摄像头，具有夜视功能和移动检测',
        stock: 25,
        featured: false,
        specifications: 'Brand: SecureView, Model: SV-1080, Type: Wireless, Power: 12V DC, Warranty: 1 year',
        sku: 'SV-1080-WL',
        details: {
          model: 'SV-1080',
          appearance: 'White',
          standard: 'IP65',
          usage: 'Home security monitoring',
          flashpoint: 'N/A',
          smell: 'Odorless',
          function: '1080p HD recording with night vision',
          amineValue: 'N/A',
          content: '1 unit',
          imported: 'Yes',
          density: 'N/A',
          color: 'White',
          viscosity: 'N/A'
        }
      },
      {
        id: '3',
        name: 'Bluetooth Wireless Headphones',
        category: 'electronics',
        price: 79.99,
        image: '/images/products/headphones-1.jpg',
        description: '高品质无线蓝牙耳机，具有主动降噪功能',
        stock: 30,
        featured: true,
        specifications: 'Brand: AudioMax, Model: AM-BT500, Type: Over-ear, Warranty: 1 year',
        sku: 'AM-BT500-BT',
        details: {
          model: 'AM-BT500',
          appearance: 'Black',
          standard: 'Bluetooth 5.0',
          usage: 'Audio listening',
          flashpoint: 'N/A',
          smell: 'Odorless',
          function: 'Active noise cancellation',
          amineValue: 'N/A',
          content: '1 unit',
          imported: 'Yes',
          density: 'N/A',
          color: 'Black',
          viscosity: 'N/A'
        }
      }
    ];
    setProducts(sampleProducts);
  };
  // Filter and sort products
  useEffect(() => {
    let filtered = products;

    // Filter by category
    if (selectedCategory && selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Filter by search query using SearchService
    if (searchQuery) {
      filtered = SearchService.searchProducts(filtered, searchQuery);
    } else {
      // Sort products when no search query
      switch (sortBy) {
        case 'price-low':
          filtered.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          filtered.sort((a, b) => b.price - a.price);
          break;
        case 'name':
          filtered.sort((a, b) => a.name.localeCompare(b.name));
          break;
        default:
          // Default sort: featured first, then by name
          filtered.sort((a, b) => {
            if (a.featured && !b.featured) return -1;
            if (!a.featured && b.featured) return 1;
            return a.name.localeCompare(b.name);
          });
      }
    }

    setFilteredProducts(filtered);
  }, [products, selectedCategory, searchQuery, sortBy]);

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
  };

  const handleCloseModal = () => {
    setSelectedProduct(null);
  };
  if (loading) {
    return (
      <div className="flex-1 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <LoadingCard count={8} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 p-4 md:p-6"> {/* 调整内边距 */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 md:p-8 text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-red-800 mb-2">加载失败</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-4 md:p-6"> {/* 调整内边距 */}
      {/* Search bar and Sort options */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        {/* Search Input - Placed here for better control within ProductSection */}
        <div className="relative w-full md:max-w-md">
          <input
            type="search"
            placeholder="搜索产品..."
            value={searchQuery} // 从 props 中获取 searchQuery
            onChange={(e) => onSearchQueryChange(e.target.value)} // 调用 props 中的回调
            className="w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
        </div>
        
        <div className="flex items-center space-x-2 md:space-x-4 w-full md:w-auto justify-between md:justify-end">
          <label className="text-sm text-gray-600 whitespace-nowrap">排序方式:</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto"
          >
            <option value="default">默认排序</option>
            <option value="price-low">价格从低到高</option>
            <option value="price-high">价格从高到低</option>
            <option value="name">按名称排序</option>
          </select>
        </div>
      </div>

      {/* Product Grid */}
      {filteredProducts.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6"> {/* 调整 gap 和 md 断点列数 */}
          {filteredProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              onClick={() => handleProductClick(product)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📦</div>
          <h3 className="text-lg font-medium text-gray-600 mb-2">没有找到产品</h3>
          <p className="text-gray-500">
            {searchQuery ? `没有找到包含 "${searchQuery}" 的产品` : '该分类下暂无产品'}
          </p>
        </div>
      )}

      {/* Product Detail Modal */}
      {selectedProduct && (
        <ProductDetailModal
          product={selectedProduct}
          isOpen={!!selectedProduct}
          onClose={handleCloseModal}
        />
      )}
    </div>  );
};

export { ProductSection };
export default ProductSection;

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-task-list-item";
exports.ids = ["vendor-chunks/micromark-extension-gfm-task-list-item"];
exports.modules = {

/***/ "(rsc)/../node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js":
/*!******************************************************************************!*\
  !*** ../node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItemHtml: () => (/* binding */ gfmTaskListItemHtml)\n/* harmony export */ });\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */ /**\n * Create an HTML extension for `micromark` to support GFM task list items when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */ function gfmTaskListItemHtml() {\n    return {\n        enter: {\n            taskListCheck () {\n                this.tag('<input type=\"checkbox\" disabled=\"\" ');\n            }\n        },\n        exit: {\n            taskListCheck () {\n                this.tag(\"/>\");\n            },\n            taskListCheckValueChecked () {\n                this.tag('checked=\"\" ');\n            }\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tZ2ZtLXRhc2stbGlzdC1pdGVtL2Rldi9saWIvaHRtbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7Ozs7OztDQU9DLEdBQ00sU0FBU0E7SUFDZCxPQUFPO1FBQ0xDLE9BQU87WUFDTEM7Z0JBQ0UsSUFBSSxDQUFDQyxHQUFHLENBQUM7WUFDWDtRQUNGO1FBQ0FDLE1BQU07WUFDSkY7Z0JBQ0UsSUFBSSxDQUFDQyxHQUFHLENBQUM7WUFDWDtZQUNBRTtnQkFDRSxJQUFJLENBQUNGLEdBQUcsQ0FBQztZQUNYO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGMtbWFsbC1mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFzay1saXN0LWl0ZW0vZGV2L2xpYi9odG1sLmpzPzc2YzAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtIdG1sRXh0ZW5zaW9ufSBmcm9tICdtaWNyb21hcmstdXRpbC10eXBlcydcbiAqL1xuXG4vKipcbiAqIENyZWF0ZSBhbiBIVE1MIGV4dGVuc2lvbiBmb3IgYG1pY3JvbWFya2AgdG8gc3VwcG9ydCBHRk0gdGFzayBsaXN0IGl0ZW1zIHdoZW5cbiAqIHNlcmlhbGl6aW5nIHRvIEhUTUwuXG4gKlxuICogQHJldHVybnMge0h0bWxFeHRlbnNpb259XG4gKiAgIEV4dGVuc2lvbiBmb3IgYG1pY3JvbWFya2AgdGhhdCBjYW4gYmUgcGFzc2VkIGluIGBodG1sRXh0ZW5zaW9uc2AgdG9cbiAqICAgc3VwcG9ydCBHRk0gdGFzayBsaXN0IGl0ZW1zIHdoZW4gc2VyaWFsaXppbmcgdG8gSFRNTC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdmbVRhc2tMaXN0SXRlbUh0bWwoKSB7XG4gIHJldHVybiB7XG4gICAgZW50ZXI6IHtcbiAgICAgIHRhc2tMaXN0Q2hlY2soKSB7XG4gICAgICAgIHRoaXMudGFnKCc8aW5wdXQgdHlwZT1cImNoZWNrYm94XCIgZGlzYWJsZWQ9XCJcIiAnKVxuICAgICAgfVxuICAgIH0sXG4gICAgZXhpdDoge1xuICAgICAgdGFza0xpc3RDaGVjaygpIHtcbiAgICAgICAgdGhpcy50YWcoJy8+JylcbiAgICAgIH0sXG4gICAgICB0YXNrTGlzdENoZWNrVmFsdWVDaGVja2VkKCkge1xuICAgICAgICB0aGlzLnRhZygnY2hlY2tlZD1cIlwiICcpXG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiZ2ZtVGFza0xpc3RJdGVtSHRtbCIsImVudGVyIiwidGFza0xpc3RDaGVjayIsInRhZyIsImV4aXQiLCJ0YXNrTGlzdENoZWNrVmFsdWVDaGVja2VkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js":
/*!********************************************************************************!*\
  !*** ../node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItem: () => (/* binding */ gfmTaskListItem)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(rsc)/../node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {Extension, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ \n\n\n\nconst tasklistCheck = {\n    name: \"tasklistCheck\",\n    tokenize: tokenizeTasklistCheck\n};\n/**\n * Create an HTML extension for `micromark` to support GFM task list items\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */ function gfmTaskListItem() {\n    return {\n        text: {\n            [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: tasklistCheck\n        }\n    };\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeTasklistCheck(effects, ok, nok) {\n    const self = this;\n    return open;\n    /**\n   * At start of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function open(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, \"expected `[`\");\n        if (// Exit if there’s stuff before.\n        self.previous !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || // Exit if not in the first content that is the first child of a list\n        // item.\n        !self._gfmTasklistFirstContentOfListItem) {\n            return nok(code);\n        }\n        effects.enter(\"taskListCheck\");\n        effects.enter(\"taskListCheckMarker\");\n        effects.consume(code);\n        effects.exit(\"taskListCheckMarker\");\n        return inside;\n    }\n    /**\n   * In task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */ function inside(code) {\n        // Currently we match how GH works in files.\n        // To match how GH works in comments, use `markdownSpace` (`[\\t ]`) instead\n        // of `markdownLineEndingOrSpace` (`[\\t\\n\\r ]`).\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n            effects.enter(\"taskListCheckValueUnchecked\");\n            effects.consume(code);\n            effects.exit(\"taskListCheckValueUnchecked\");\n            return close;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseX || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseX) {\n            effects.enter(\"taskListCheckValueChecked\");\n            effects.consume(code);\n            effects.exit(\"taskListCheckValueChecked\");\n            return close;\n        }\n        return nok(code);\n    }\n    /**\n   * At close of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function close(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n            effects.enter(\"taskListCheckMarker\");\n            effects.consume(code);\n            effects.exit(\"taskListCheckMarker\");\n            effects.exit(\"taskListCheck\");\n            return after;\n        }\n        return nok(code);\n    }\n    /**\n   * @type {State}\n   */ function after(code) {\n        // EOL in paragraph means there must be something else after it.\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            return ok(code);\n        }\n        // Space or tab?\n        // Check what comes after.\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return effects.check({\n                tokenize: spaceThenNonSpace\n            }, ok, nok)(code);\n        }\n        // EOF, or non-whitespace, both wrong.\n        return nok(code);\n    }\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function spaceThenNonSpace(effects, ok, nok) {\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, after, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.whitespace);\n    /**\n   * After whitespace, after task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */ function after(code) {\n        // EOF means there was nothing, so bad.\n        // EOL means there’s content after it, so good.\n        // Impossible to have more spaces.\n        // Anything else is good.\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ? nok(code) : ok(code);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js\n");

/***/ })

};
;
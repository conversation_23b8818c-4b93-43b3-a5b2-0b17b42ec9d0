#!/usr/bin/env node

/**
 * API连接测试脚本
 * 用于验证前端和后端API连接是否正常
 */

const http = require('http');
const https = require('https');

// 测试配置
const tests = [
  {
    name: '后端健康检查',
    url: 'http://localhost:5000/health',
    expected: 'health'
  },
  {
    name: '后端产品API',
    url: 'http://localhost:5000/api/products?limit=5',
    expected: 'products'
  },
  {
    name: '前端产品API代理',
    url: 'http://localhost:3000/api/products?limit=5',
    expected: 'products'
  },
  {
    name: '前端主页',
    url: 'http://localhost:3000',
    expected: 'html'
  }
];

// 生产环境测试
const prodTests = [
  {
    name: '生产后端健康检查',
    url: 'http://gdlongchi.cn:5000/health',
    expected: 'health'
  },
  {
    name: '生产后端产品API',
    url: 'http://gdlongchi.cn:5000/api/products?limit=5',
    expected: 'products'
  },
  {
    name: '生产前端产品API代理',
    url: 'http://gdlongchi.cn:3000/api/products?limit=5',
    expected: 'products'
  },
  {
    name: '生产前端主页',
    url: 'http://gdlongchi.cn:3000',
    expected: 'html'
  }
];

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    
    const req = client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function runTest(test) {
  try {
    console.log(`\n🧪 测试: ${test.name}`);
    console.log(`📡 URL: ${test.url}`);
    
    const result = await makeRequest(test.url);
    
    console.log(`✅ 状态码: ${result.status}`);
    
    if (result.status === 200) {
      let data;
      try {
        data = JSON.parse(result.data);
      } catch (e) {
        data = result.data;
      }
      
      if (test.expected === 'health') {
        console.log(`💚 健康检查通过`);
        if (typeof data === 'object' && data.status) {
          console.log(`📊 状态: ${data.status}`);
        }
      } else if (test.expected === 'products') {
        if (typeof data === 'object') {
          if (data.success && data.data && data.data.products) {
            console.log(`💚 产品API正常 - 找到 ${data.data.products.length} 个产品`);
            console.log(`📊 总数: ${data.data.pagination?.total || 'N/A'}`);
          } else if (data.products) {
            console.log(`💚 产品API正常 - 找到 ${data.products.length} 个产品`);
          } else if (data.error) {
            console.log(`❌ API错误: ${data.error}`);
          } else {
            console.log(`⚠️  未知响应格式:`, JSON.stringify(data, null, 2).substring(0, 200));
          }
        } else {
          console.log(`⚠️  非JSON响应:`, result.data.substring(0, 200));
        }
      } else if (test.expected === 'html') {
        if (result.data.includes('<html') || result.data.includes('<!DOCTYPE')) {
          console.log(`💚 HTML页面正常`);
        } else {
          console.log(`⚠️  响应不是HTML:`, result.data.substring(0, 100));
        }
      }
    } else {
      console.log(`❌ 请求失败: ${result.status}`);
      console.log(`📄 响应:`, result.data.substring(0, 200));
    }
    
    return { success: result.status === 200, test: test.name, status: result.status };
    
  } catch (error) {
    console.log(`❌ 连接失败: ${error.message}`);
    return { success: false, test: test.name, error: error.message };
  }
}

async function main() {
  console.log('🚀 LC Mall API 连接测试');
  console.log('=' .repeat(50));
  
  const isProduction = process.argv.includes('--prod');
  const testSuite = isProduction ? prodTests : tests;
  
  console.log(`🌍 环境: ${isProduction ? '生产环境' : '本地环境'}`);
  
  const results = [];
  
  for (const test of testSuite) {
    const result = await runTest(test);
    results.push(result);
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 测试结果汇总:');
  
  let passed = 0;
  let failed = 0;
  
  results.forEach(result => {
    if (result.success) {
      console.log(`✅ ${result.test}`);
      passed++;
    } else {
      console.log(`❌ ${result.test} - ${result.error || result.status}`);
      failed++;
    }
  });
  
  console.log(`\n🎯 总计: ${passed} 通过, ${failed} 失败`);
  
  if (failed > 0) {
    console.log('\n💡 故障排除建议:');
    console.log('1. 检查服务是否正在运行');
    console.log('2. 检查端口是否被占用');
    console.log('3. 检查防火墙设置');
    console.log('4. 检查环境变量配置');
    process.exit(1);
  } else {
    console.log('\n🎉 所有测试通过！');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runTest, makeRequest };

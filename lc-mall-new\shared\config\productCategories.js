/**
 * 产品分类配置 - 前后端共享
 * 包含分类映射、分类选项、分类显示名称等配置
 */

// 类型定义
export interface CategoryOption {
  value: string;
  label: string;
}

export interface StatusOption {
  value: string;
  label: string;
}

export interface UnitOption {
  value: string;
  label: string;
}

// 中文分类名称到英文分类值的映射
export const categoryMapping: Record<string, string> = {
  '样品': 'samples',
  '潜固化剂': 'latent-curing-agents',
  '催化剂': 'catalysts',
  '分散剂': 'dispersants',
  '流平剂': 'leveling-agents',
  '助剂': 'additives',
  '原料': 'raw-materials',
  '附着力促进剂': 'adhesion-promoters',
  '固化剂': 'curing-agents',
  '设备': 'equipment',
  '扩链剂': 'chain-extenders',
  '消泡剂': 'defoamers',
  '生物基树脂': 'bio-based-resins',
  '粉体助剂': 'powder-additives',
  '防沉剂': 'anti-settling-agents'
};

// 分类选项配置（用于前端下拉选择器）
export const categoryOptions: CategoryOption[] = [
  { value: '', label: '请选择分类' },
  { value: 'catalysts', label: '催化剂' },
  { value: 'curing-agents', label: '固化剂' },
  { value: 'adhesion-promoters', label: '附着力促进剂' },
  { value: 'dispersants', label: '分散剂' },
  { value: 'chain-extenders', label: '扩链剂' },
  { value: 'leveling-agents', label: '流平剂' },
  { value: 'defoamers', label: '消泡剂' },
  { value: 'latent-curing-agents', label: '潜固化剂' },
  { value: 'bio-based-resins', label: '生物基树脂' },
  { value: 'powder-additives', label: '粉体助剂' },
  { value: 'anti-settling-agents', label: '防沉剂' },
  { value: 'additives', label: '助剂' },
  { value: 'raw-materials', label: '原材料' },
  { value: 'equipment', label: '设备' },
  { value: 'samples', label: '样品' }
];

// 英文分类值到中文显示名称的映射
export const categoryDisplayNames: Record<string, string> = {
  'catalysts': '催化剂',
  'curing-agents': '固化剂',
  'adhesion-promoters': '附着力促进剂',
  'dispersants': '分散剂',
  'chain-extenders': '扩链剂',
  'leveling-agents': '流平剂',
  'defoamers': '消泡剂',
  'latent-curing-agents': '潜固化剂',
  'bio-based-resins': '生物基树脂',
  'powder-additives': '粉体助剂',
  'anti-settling-agents': '防沉剂',
  'additives': '助剂',
  'raw-materials': '原材料',
  'equipment': '设备',
  'samples': '样品'
};

// 产品状态选项
export const statusOptions: StatusOption[] = [
  { value: 'active', label: '上架' },
  { value: 'inactive', label: '下架' },
  { value: 'draft', label: '草稿' }
];

// 计量单位选项
export const unitOptions: UnitOption[] = [
  { value: '吨', label: '吨' },
  { value: '公斤', label: '公斤' },
  { value: 'kg', label: 'kg' },
  { value: '升', label: '升' },
  { value: '桶', label: '桶' },
  { value: '个', label: '个' },
  { value: '件', label: '件' }
];

// 获取分类显示名称
export const getCategoryDisplayName = (categoryValue: string): string => {
  return categoryDisplayNames[categoryValue] || categoryValue;
};

// 根据中文分类名称获取英文分类值
export const getCategoryValue = (chineseName: string): string => {
  return categoryMapping[chineseName] || 'raw-materials';
};

// 验证分类值是否有效
export const isValidCategory = (categoryValue: string): boolean => {
  return Object.values(categoryMapping).includes(categoryValue) ||
         categoryOptions.some(option => option.value === categoryValue);
};

/**
 * 访问统计系统测试脚本
 * Test Analytics System
 */

const axios = require('axios');

const BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'http://gdlongchi.cn:5000/api' 
  : 'http://localhost:5000/api';

const ADMIN_API_KEY = process.env.ADMIN_API_KEY || 'lc_admin_2025_secure_key_prod';

// 测试数据
const testData = {
  sessionId: `test-session-${Date.now()}`,
  url: 'http://test.com/test-page',
  pathname: '/test-page',
  referer: 'http://test.com/',
  duration: 30,
  deviceType: 'desktop',
  browserName: 'Chrome',
  browserVersion: '120.0.0',
  osName: 'Windows',
  osVersion: '11',
  screenResolution: '1920x1080',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  event: 'pageview'
};

async function testAnalyticsAPI() {
  console.log('========================================');
  console.log('测试访问统计系统API');
  console.log('========================================');

  try {
    // 1. 测试访问记录提交
    console.log('1. 测试访问记录提交...');
    const trackResponse = await axios.post(`${BASE_URL}/analytics/track`, testData);
    console.log('✅ 访问记录提交成功:', trackResponse.data);
    
    // 等待一秒让数据保存
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 2. 测试获取实时统计
    console.log('\n2. 测试获取实时统计...');
    const realtimeResponse = await axios.get(`${BASE_URL}/analytics/admin/realtime`, {
      headers: {
        'X-Admin-Key': ADMIN_API_KEY
      }
    });
    console.log('✅ 实时统计获取成功:', realtimeResponse.data);

    // 3. 测试获取统计数据
    console.log('\n3. 测试获取统计数据...');
    const today = new Date().toISOString().split('T')[0];
    const statsResponse = await axios.get(`${BASE_URL}/analytics/admin/stats`, {
      params: {
        startDate: today,
        endDate: today
      },
      headers: {
        'X-Admin-Key': ADMIN_API_KEY
      }
    });
    console.log('✅ 统计数据获取成功:', statsResponse.data);

    // 4. 测试获取访问记录
    console.log('\n4. 测试获取访问记录...');
    const recordsResponse = await axios.get(`${BASE_URL}/analytics/admin/records`, {
      params: {
        startDate: today,
        endDate: today,
        limit: 10
      },
      headers: {
        'X-Admin-Key': ADMIN_API_KEY
      }
    });
    console.log('✅ 访问记录获取成功:', recordsResponse.data);

    // 5. 测试获取热门页面
    console.log('\n5. 测试获取热门页面...');
    const popularResponse = await axios.get(`${BASE_URL}/analytics/admin/popular-pages`, {
      params: {
        startDate: today,
        endDate: today,
        limit: 5
      },
      headers: {
        'X-Admin-Key': ADMIN_API_KEY
      }
    });
    console.log('✅ 热门页面获取成功:', popularResponse.data);

    console.log('\n========================================');
    console.log('✅ 所有测试通过！访问统计系统运行正常');
    console.log('========================================');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    console.error('错误详情:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method
    });
    process.exit(1);
  }
}

// 批量测试（模拟多个用户访问）
async function batchTest() {
  console.log('\n========================================');
  console.log('开始批量测试（模拟多个用户访问）');
  console.log('========================================');

  const pages = [
    '/products',
    '/products/category/catalyst',
    '/products/detail/123',
    '/about',
    '/contact',
    '/news'
  ];

  const devices = ['desktop', 'mobile', 'tablet'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];

  for (let i = 0; i < 20; i++) {
    try {
      const testRecord = {
        ...testData,
        sessionId: `batch-session-${i}-${Date.now()}`,
        pathname: pages[Math.floor(Math.random() * pages.length)],
        deviceType: devices[Math.floor(Math.random() * devices.length)],
        browserName: browsers[Math.floor(Math.random() * browsers.length)],
        duration: Math.floor(Math.random() * 300) + 10, // 10-310秒
      };

      await axios.post(`${BASE_URL}/analytics/track`, testRecord);
      console.log(`✅ 批量测试 ${i + 1}/20 完成`);
      
      // 小延迟避免过快请求
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.error(`❌ 批量测试 ${i + 1} 失败:`, error.message);
    }
  }

  console.log('✅ 批量测试完成');
}

async function main() {
  // 基本API测试
  await testAnalyticsAPI();
  
  // 批量测试
  await batchTest();
  
  // 再次检查统计数据
  console.log('\n========================================');
  console.log('再次检查统计数据（应该包含批量测试的数据）');
  console.log('========================================');
  
  try {
    const today = new Date().toISOString().split('T')[0];
    const finalStatsResponse = await axios.get(`${BASE_URL}/analytics/admin/stats`, {
      params: {
        startDate: today,
        endDate: today
      },
      headers: {
        'X-Admin-Key': ADMIN_API_KEY
      }
    });
    
    console.log('📊 最终统计数据:');
    console.log(`总访问量: ${finalStatsResponse.data.data.totalViews}`);
    console.log(`独立访客: ${finalStatsResponse.data.data.uniqueVisitors}`);
    console.log(`访问会话: ${finalStatsResponse.data.data.uniqueSessions}`);
    console.log(`平均停留时间: ${finalStatsResponse.data.data.avgDuration}秒`);
    
  } catch (error) {
    console.error('❌ 获取最终统计数据失败:', error.message);
  }
  
  console.log('\n🎉 访问统计系统测试完成！');
}

// 运行测试
main().catch(console.error);

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/products/route";
exports.ids = ["app/api/admin/products/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fproducts%2Froute&page=%2Fapi%2Fadmin%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fproducts%2Froute&page=%2Fapi%2Fadmin%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_LongChiMall_lc_mall_new_frontend_src_app_api_admin_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/products/route.ts */ \"(rsc)/./src/app/api/admin/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/products/route\",\n        pathname: \"/api/admin/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/products/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\api\\\\admin\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_LongChiMall_lc_mall_new_frontend_src_app_api_admin_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZhZG1pbiUyRnByb2R1Y3RzJTJGcm91dGUmcGFnZT0lMkZhcGklMkZhZG1pbiUyRnByb2R1Y3RzJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGYWRtaW4lMkZwcm9kdWN0cyUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDZ2l0aHViJTVDTG9uZ0NoaU1hbGwlNUNsYy1tYWxsLW5ldyU1Q2Zyb250ZW5kJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz1qcyZwYWdlRXh0ZW5zaW9ucz1qc3gmcm9vdERpcj1EJTNBJTVDZ2l0aHViJTVDTG9uZ0NoaU1hbGwlNUNsYy1tYWxsLW5ldyU1Q2Zyb250ZW5kJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUN3QztBQUNySDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvP2U4NzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxcZ2l0aHViXFxcXExvbmdDaGlNYWxsXFxcXGxjLW1hbGwtbmV3XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGFkbWluXFxcXHByb2R1Y3RzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hZG1pbi9wcm9kdWN0cy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2FkbWluL3Byb2R1Y3RzXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9hZG1pbi9wcm9kdWN0cy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkQ6XFxcXGdpdGh1YlxcXFxMb25nQ2hpTWFsbFxcXFxsYy1tYWxsLW5ld1xcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhZG1pblxcXFxwcm9kdWN0c1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvYWRtaW4vcHJvZHVjdHMvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fproducts%2Froute&page=%2Fapi%2Fadmin%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/products/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/admin/products/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_adminAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/adminAuth */ \"(rsc)/./src/utils/adminAuth.ts\");\n\n\n// 禁用路由缓存\nconst dynamic = \"force-dynamic\";\nconst revalidate = 0;\nconst API_BASE = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n// Create product (POST)\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const response = await fetch(`${API_BASE}/products`, {\n            method: \"POST\",\n            headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_1__.getAdminHeaders)(),\n            body: JSON.stringify(body)\n        });\n        const result = await response.json();\n        if (!response.ok) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: result.message || \"Failed to create product\"\n            }, {\n                status: response.status\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result.data, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Get products with filters (GET)\nasync function GET(request) {\n    try {\n        const searchParams = request.nextUrl.searchParams;\n        const queryString = searchParams.toString();\n        const response = await fetch(`${API_BASE}/products?${queryString}`, {\n            method: \"GET\",\n            headers: {\n                ...(0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_1__.getAdminHeaders)(),\n                \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                \"Pragma\": \"no-cache\",\n                \"Expires\": \"0\"\n            }\n        });\n        const result = await response.json();\n        if (!response.ok) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: result.message || \"Failed to fetch products\"\n            }, {\n                status: response.status\n            });\n        }\n        // 设置响应头禁用缓存\n        const responseJson = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result.data);\n        responseJson.headers.set(\"Cache-Control\", \"no-cache, no-store, must-revalidate\");\n        responseJson.headers.set(\"Pragma\", \"no-cache\");\n        responseJson.headers.set(\"Expires\", \"0\");\n        return responseJson;\n    } catch (error) {\n        console.error(\"Error fetching products:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/adminAuth.ts":
/*!********************************!*\
  !*** ./src/utils/adminAuth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminHeaders: () => (/* binding */ getAdminHeaders)\n/* harmony export */ });\n/**\r\n * 管理员认证相关工具函数\r\n */ /**\r\n * 获取管理员API请求头\r\n * @returns 包含Content-Type和管理员API密钥的请求头对象\r\n */ function getAdminHeaders() {\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // 获取管理员API密钥\n    let adminApiKey;\n    if (true) {\n        adminApiKey = \"lc_admin_dev_key_2025\" || 0 || 0;\n        console.log(\"Server-side Admin API Key source:\",  true ? \"NEXT_PUBLIC_ADMIN_API_KEY\" : 0);\n    } else {}\n    if (adminApiKey) {\n        headers[\"x-admin-api-key\"] = adminApiKey;\n    }\n    return headers;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvYWRtaW5BdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Q0FHQyxHQUNNLFNBQVNBO0lBQ2QsTUFBTUMsVUFBa0M7UUFDdEMsZ0JBQWdCO0lBQ2xCO0lBRUEsYUFBYTtJQUNiLElBQUlDO0lBQ0YsSUFBSSxJQUFrQixFQUFhO1FBQ25DQSxjQUFjQyx1QkFBcUMsSUFDdENBLENBQXlCLElBQ3pCO1FBQ2JJLFFBQVFDLEdBQUcsQ0FBQyxxQ0FDQUwsS0FBcUMsR0FBRyw4QkFDeENBLENBQThDO0lBQzdELE9BQU8sRUFJTDtJQUVELElBQUlELGFBQWE7UUFDZkQsT0FBTyxDQUFDLGtCQUFrQixHQUFHQztJQUMvQjtJQUVBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYy1tYWxsLWZyb250ZW5kLy4vc3JjL3V0aWxzL2FkbWluQXV0aC50cz8yNWM5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiDnrqHnkIblkZjorqTor4Hnm7jlhbPlt6Xlhbflh73mlbBcclxuICovXHJcblxyXG4vKipcclxuICog6I635Y+W566h55CG5ZGYQVBJ6K+35rGC5aS0XHJcbiAqIEByZXR1cm5zIOWMheWQq0NvbnRlbnQtVHlwZeWSjOeuoeeQhuWRmEFQSeWvhumSpeeahOivt+axguWktOWvueixoVxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGdldEFkbWluSGVhZGVycygpOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+IHtcclxuICBjb25zdCBoZWFkZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xyXG4gICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICB9O1xyXG4gIFxyXG4gIC8vIOiOt+WPlueuoeeQhuWRmEFQSeWvhumSpVxyXG4gIGxldCBhZG1pbkFwaUtleTogc3RyaW5nO1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7ICAgIC8vIOacjeWKoeWZqOerryAtIOS7jueOr+Wig+WPmOmHj+iOt+WPlu+8jOS8mOWFiOS9v+eUqE5FWFRfUFVCTElDX+WJjee8gOeahOWPmOmHj1xyXG4gICAgYWRtaW5BcGlLZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BRE1JTl9BUElfS0VZIHx8IFxyXG4gICAgICAgICAgICAgICAgIHByb2Nlc3MuZW52LkFETUlOX0FQSV9LRVkgfHwgXHJcbiAgICAgICAgICAgICAgICAgJ2xjX2FkbWluX2Rldl9rZXlfMjAyNSc7XHJcbiAgICBjb25zb2xlLmxvZygnU2VydmVyLXNpZGUgQWRtaW4gQVBJIEtleSBzb3VyY2U6JywgXHJcbiAgICAgICAgICAgICAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BRE1JTl9BUElfS0VZID8gJ05FWFRfUFVCTElDX0FETUlOX0FQSV9LRVknIDogXHJcbiAgICAgICAgICAgICAgICBwcm9jZXNzLmVudi5BRE1JTl9BUElfS0VZID8gJ0FETUlOX0FQSV9LRVknIDogJ2ZhbGxiYWNrJyk7ICBcclxuXHR9IGVsc2Uge1xyXG4gICAgLy8g5a6i5oi356uvIC0g5LuO5p6E5bu65pe25rOo5YWl55qE546v5aKD5Y+Y6YeP6I635Y+WXHJcbiAgICBhZG1pbkFwaUtleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FETUlOX0FQSV9LRVkgfHwgJ2xjX2FkbWluX2Rldl9rZXlfMjAyNSc7XHJcbiAgICBjb25zb2xlLmxvZygnQ2xpZW50LXNpZGUgdXNpbmcgTkVYVF9QVUJMSUNfQURNSU5fQVBJX0tFWScpO1xyXG4gIH1cclxuICBcclxuICBpZiAoYWRtaW5BcGlLZXkpIHtcclxuICAgIGhlYWRlcnNbJ3gtYWRtaW4tYXBpLWtleSddID0gYWRtaW5BcGlLZXk7XHJcbiAgfVxyXG4gIFxyXG4gIHJldHVybiBoZWFkZXJzO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJnZXRBZG1pbkhlYWRlcnMiLCJoZWFkZXJzIiwiYWRtaW5BcGlLZXkiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQURNSU5fQVBJX0tFWSIsIkFETUlOX0FQSV9LRVkiLCJjb25zb2xlIiwibG9nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/adminAuth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fproducts%2Froute&page=%2Fapi%2Fadmin%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
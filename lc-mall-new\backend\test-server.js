/**
 * 测试服务器启动脚本
 */

console.log('🔍 开始测试服务器启动...');

try {
  console.log('1️⃣ 测试基本模块...');
  const express = require('express');
  console.log('✅ Express 加载成功');

  console.log('2️⃣ 测试日志模块...');
  const logger = require('./src/utils/logger');
  console.log('✅ Logger 加载成功');

  console.log('3️⃣ 测试产品服务...');
  const productService = require('./src/services/productService');
  console.log('✅ ProductService 加载成功');

  console.log('4️⃣ 测试产品路由...');
  const productRoutes = require('./src/routes/products');
  console.log('✅ Product routes 加载成功');

  console.log('5️⃣ 创建Express应用...');
  const app = express();
  app.use(express.json());

  // 添加刷新数据的端点
  app.get('/api/refresh-data', async (req, res) => {
    try {
      const result = await productService.refreshImportedData();
      res.json(result);
    } catch (error) {
      res.status(500).json({ success: false, message: error.message });
    }
  });

  app.use('/api/products', productRoutes);
  console.log('✅ Express 应用创建成功');

  console.log('6️⃣ 启动服务器...');
  const PORT = 5000;
  const server = app.listen(PORT, () => {
    console.log(`🚀 测试服务器启动成功: http://localhost:${PORT}`);
  });

  server.on('error', (error) => {
    console.error('❌ 服务器错误:', error);
  });

} catch (error) {
  console.error('❌ 测试过程中发生错误:', error);
  console.error('错误堆栈:', error.stack);
}

@echo off
chcp 65001 >nul
title LC Mall API密钥修复工具

echo ==========================================
echo       LC Mall API密钥修复工具
echo ==========================================
echo.

echo 检查API密钥配置...
echo 后端API密钥:
findstr "ADMIN_API_KEY=" .env.production
echo 前端API密钥:
findstr "NEXT_PUBLIC_ADMIN_API_KEY=" frontend\.env.production

echo.
echo 进入前端目录...
cd frontend

echo.
echo 清理构建缓存...
if exist ".next" rmdir /s /q .next
if exist "node_modules\.cache" rmdir /s /q node_modules\.cache

echo.
echo 重新构建前端...
call npm run build

if errorlevel 1 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)

echo ✅ 前端构建成功

echo.
echo 检查后端图片目录...
cd ..\backend
if not exist "public\images\products" mkdir public\images\products
if not exist "public\images\news" mkdir public\images\news
if not exist "public\images\uploads" mkdir public\images\uploads

echo ✅ 后端目录检查完成

echo.
echo ==========================================
echo ✅ API密钥修复完成！
echo ==========================================
echo.
echo 请在云服务器上执行以下命令:
echo 1. chmod +x fix-api-keys.sh
echo 2. ./fix-api-keys.sh
echo.
echo 或者手动重启服务:
echo 1. ./stop.sh
echo 2. ./deploy.sh
echo.

pause

#!/bin/bash

echo "🔧 LC Mall 快速修复脚本"
echo "修复前端API重定向问题..."

# 加载环境变量
if [ -f ".env.production" ]; then
    export $(grep -v '^#' .env.production | xargs)
fi

# 停止前端服务
echo "🛑 停止前端服务..."
pkill -f "next start" || true
pkill -f "node.*server.js" || true
sleep 2

# 进入前端目录
cd frontend

# 清理构建缓存
echo "🧹 清理构建缓存..."
rm -rf .next

# 重新构建
echo "🔨 重新构建前端..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

# 启动前端服务
echo "🚀 启动前端服务..."
nohup npm start > ../logs/frontend.log 2>&1 &
frontend_pid=$!

echo "📝 前端 PID: $frontend_pid"
echo $frontend_pid > ../logs/frontend.pid

# 等待启动
echo "⏳ 等待服务启动..."
sleep 8

# 测试服务
if ps -p $frontend_pid > /dev/null; then
    echo "✅ 前端服务启动成功"
    
    # 测试API
    echo "🧪 测试API..."
    sleep 3
    
    if curl -s http://localhost:3000/api/products?limit=1 | grep -q "products\|error"; then
        echo "✅ 前端API正常"
    else
        echo "⚠️  前端API可能有问题"
    fi
else
    echo "❌ 前端服务启动失败"
    exit 1
fi

cd ..

echo ""
echo "🎉 修复完成！"
echo "现在测试API连接："
node test-api.js

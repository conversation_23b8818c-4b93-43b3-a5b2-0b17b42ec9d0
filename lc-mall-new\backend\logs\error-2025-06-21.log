{"environment":"development","error":"pipeline.hgetAll is not a function","keysCount":3,"level":"error","message":"Batch get error:","service":"lc-mall-backend","timestamp":"2025-06-21 11:16:00"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: ApiResponse.success is not a function","service":"lc-mall-backend","stack":"TypeError: ApiResponse.success is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:30:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"error","message":"Unhandled error: ApiResponse.success is not a function","service":"lc-mall-backend","stack":"TypeError: ApiResponse.success is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:30:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"error","message":"Unhandled error: ApiResponse.success is not a function","service":"lc-mall-backend","stack":"TypeError: ApiResponse.success is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:30:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"error","message":"Unhandled error: ApiResponse.success is not a function","service":"lc-mall-backend","stack":"TypeError: ApiResponse.success is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:30:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","error":"productService.initializeProductData is not a function","level":"error","message":"Product import failed","service":"lc-mall-backend","timestamp":"2025-06-21 12:12:20"}
{"environment":"development","level":"error","message":"Redis smembers error: Cannot read properties of null (reading 'sMembers')","service":"lc-mall-backend","stack":"TypeError: Cannot read properties of null (reading 'sMembers')\n    at RedisService.smembers (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\redisService.js:582:32)\n    at ProductService.loadImportedData (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:714:45)\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:15:10)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:777:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)","timestamp":"2025-06-21 12:29:05"}
{"environment":"development","level":"error","message":"Redis smembers error: Cannot read properties of null (reading 'sMembers')","service":"lc-mall-backend","stack":"TypeError: Cannot read properties of null (reading 'sMembers')\n    at RedisService.smembers (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\redisService.js:582:32)\n    at ProductService.loadImportedData (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:714:45)\n    at ProductService.refreshImportedData (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:767:16)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\test-server.js:31:43\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-06-21 12:29:39"}

{"environment":"development","error":"pipeline.hgetAll is not a function","keysCount":3,"level":"error","message":"Batch get error:","service":"lc-mall-backend","timestamp":"2025-06-21 11:16:00"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}

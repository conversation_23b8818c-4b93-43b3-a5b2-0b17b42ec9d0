#!/bin/bash
# 修复Next.js standalone配置警告

echo "=========================================="
echo "   龙驰新材料商城 - 修复前端配置警告     "
echo "=========================================="

cd frontend

echo "1. 检查当前next.config.js配置..."
if [ -f "next.config.js" ]; then
    echo "当前配置内容:"
    cat next.config.js
    echo ""
else
    echo "❌ next.config.js文件不存在"
    exit 1
fi

# 检查是否有standalone配置
if grep -q "output.*standalone" next.config.js; then
    echo "⚠️  发现standalone配置，这会导致启动警告"
    echo ""
    echo "修复选项："
    echo "1. 移除standalone配置 (推荐，适用于当前部署方式)"
    echo "2. 使用standalone模式启动 (需要修改启动脚本)"
    echo ""
    echo "选择修复方式 (1/2): "
    read -r fix_option
    
    if [ "$fix_option" = "1" ]; then
        echo "正在移除standalone配置..."
        
        # 备份原文件
        cp next.config.js next.config.js.backup
        echo "✅ 已备份原配置到 next.config.js.backup"
        
        # 创建新的配置文件，移除standalone
        cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['localhost', 'gdlongchi.cn'],
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '5000',
        pathname: '/images/**',
      },
      {
        protocol: 'http',
        hostname: 'gdlongchi.cn',
        port: '5000', 
        pathname: '/images/**',
      }
    ],
  },
  // 移除了 output: 'standalone' 配置
}

module.exports = nextConfig
EOF
        
        echo "✅ 已更新next.config.js配置"
        echo ""
        echo "重新构建前端应用..."
        
        # 清理旧构建
        rm -rf .next
        
        # 重新构建
        npm run build
        
        if [ $? -eq 0 ]; then
            echo "✅ 前端重新构建成功"
            echo ""
            echo "重启前端服务..."
            
            # 停止前端进程
            if [ -f "../logs/frontend.pid" ]; then
                frontend_pid=$(cat ../logs/frontend.pid)
                if ps -p $frontend_pid > /dev/null; then
                    kill $frontend_pid
                    echo "已停止前端服务 (PID: $frontend_pid)"
                fi
            fi
            
            # 启动前端服务
            nohup npm start > ../logs/frontend.log 2>&1 &
            new_frontend_pid=$!
            echo $new_frontend_pid > ../logs/frontend.pid
            echo "✅ 前端服务已重启 (PID: $new_frontend_pid)"
            
            # 等待启动
            sleep 8
            
            # 测试访问
            if curl -f http://localhost:3000 > /dev/null 2>&1; then
                echo "✅ 前端服务启动成功，警告已修复"
            else
                echo "❌ 前端服务启动失败"
                echo "查看日志:"
                tail -20 ../logs/frontend.log
            fi
        else
            echo "❌ 前端构建失败"
            echo "恢复原配置..."
            mv next.config.js.backup next.config.js
        fi
        
    elif [ "$fix_option" = "2" ]; then
        echo "使用standalone模式需要修改启动方式..."
        echo "构建standalone版本..."
        
        npm run build
        
        if [ $? -eq 0 ]; then
            echo "✅ standalone构建成功"
            echo ""
            echo "创建standalone启动脚本..."
            
            cat > start-standalone.sh << 'EOF'
#!/bin/bash
# Next.js Standalone启动脚本

echo "启动Next.js standalone服务器..."

cd .next/standalone

# 复制静态文件
if [ ! -d "frontend/.next/static" ]; then
    mkdir -p frontend/.next/static
    cp -r ../static frontend/.next/
fi

# 启动standalone服务器
nohup node server.js > ../../logs/frontend.log 2>&1 &
echo $! > ../../logs/frontend.pid

echo "Standalone服务器已启动"
EOF
            
            chmod +x start-standalone.sh
            echo "✅ standalone启动脚本已创建: start-standalone.sh"
            echo ""
            echo "要使用standalone模式，请："
            echo "1. 停止当前前端服务"
            echo "2. 运行: ./start-standalone.sh"
        else
            echo "❌ standalone构建失败"
        fi
    fi
    
else
    echo "✅ 未发现standalone配置，配置正常"
fi

echo ""
echo "2. 检查当前前端进程状态..."
if [ -f "../logs/frontend.pid" ]; then
    frontend_pid=$(cat ../logs/frontend.pid)
    if ps -p $frontend_pid > /dev/null; then
        echo "✅ 前端服务运行正常 (PID: $frontend_pid)"
        
        # 检查是否还有警告
        echo "检查日志中的警告..."
        if grep -q "does not work with.*standalone" ../logs/frontend.log; then
            echo "⚠️  日志中仍有standalone警告，可能需要重启服务"
        else
            echo "✅ 未发现standalone警告"
        fi
    else
        echo "❌ 前端服务未运行"
    fi
else
    echo "❌ 未找到前端进程PID文件"
fi

cd ..

echo ""
echo "=========================================="
echo "前端配置修复完成！"
echo "=========================================="
echo ""
echo "📋 验证步骤："
echo "1. 检查前端日志: tail -f logs/frontend.log"
echo "2. 访问前端页面: http://localhost:3000"
echo "3. 通过域名访问: http://gdlongchi.cn"
echo "4. 如仍有问题，可运行: ./quick-restart.sh"
echo "=========================================="

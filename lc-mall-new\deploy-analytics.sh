#!/bin/bash

# 部署访问统计系统
# Deploy Analytics System

echo "=========================================="
echo "部署龙驰新材料访问统计系统"
echo "=========================================="

# 设置错误时退出
set -e

# 进入项目根目录
cd "$(dirname "$0")"

echo "1. 停止现有服务..."
# 停止后端服务
pm2 stop lc-mall-backend 2>/dev/null || echo "后端服务未运行"

# 停止前端服务
pm2 stop lc-mall-frontend 2>/dev/null || echo "前端服务未运行"

echo "2. 更新后端依赖..."
cd backend
npm install

echo "3. 构建前端..."
cd ../frontend
npm install
npm run build

echo "4. 重新加载nginx配置..."
sudo nginx -t && sudo nginx -s reload

echo "5. 启动后端服务..."
cd ../backend
pm2 start ecosystem.config.js --only lc-mall-backend

echo "6. 启动前端服务..."
cd ../frontend
pm2 start ecosystem.config.js --only lc-mall-frontend

echo "7. 检查服务状态..."
pm2 status

echo "=========================================="
echo "访问统计系统部署完成！"
echo "=========================================="
echo "后台管理访问统计页面: http://gdlongchi.cn/admin/analytics"
echo "API端点: http://gdlongchi.cn:5000/api/analytics"
echo "=========================================="

# 等待5秒后检查服务健康状态
echo "等待5秒后检查服务健康状态..."
sleep 5

echo "检查后端API健康状态..."
curl -f http://localhost:5000/api/health || echo "后端健康检查失败"

echo "检查前端页面..."
curl -f http://localhost:3000 || echo "前端页面检查失败"

echo "部署完成！如有问题请检查日志："
echo "后端日志: pm2 logs lc-mall-backend"
echo "前端日志: pm2 logs lc-mall-frontend"
echo "nginx日志: sudo tail -f /var/log/nginx/error.log"

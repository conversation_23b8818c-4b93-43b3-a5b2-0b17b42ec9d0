/**
 * 测试Redis连接
 */

console.log('🔍 测试Redis连接...');

async function testRedis() {
  try {
    console.log('1️⃣ 加载Redis服务...');
    const redisService = require('./src/services/redisService');
    console.log('✅ Redis服务加载成功');

    console.log('2️⃣ 检查Redis连接状态...');
    console.log('Redis client:', redisService.client ? '存在' : '不存在');
    console.log('Redis isConnected:', redisService.isConnected);

    console.log('3️⃣ 尝试连接Redis...');
    await redisService.connect();
    console.log('✅ Redis连接成功');

    console.log('4️⃣ 测试Redis操作...');
    await redisService.set('test:key', 'test-value');
    const value = await redisService.get('test:key');
    console.log('测试值:', value);

    console.log('5️⃣ 检查导入的产品数据...');
    const productIds = await redisService.smembers('imported:products:all');
    console.log('导入的产品ID数量:', productIds ? productIds.length : 0);
    console.log('产品ID列表:', productIds);

    if (productIds && productIds.length > 0) {
      console.log('6️⃣ 获取第一个产品详情...');
      const firstProductData = await redisService.hgetAll(`imported:product:${productIds[0]}`);
      console.log('第一个产品:', firstProductData);
    }

    console.log('🎉 Redis测试完成！');

  } catch (error) {
    console.error('❌ Redis测试失败:', error);
    console.error('错误堆栈:', error.stack);
  }
}

testRedis();

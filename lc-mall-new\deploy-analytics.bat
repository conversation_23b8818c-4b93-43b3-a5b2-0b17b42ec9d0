@echo off
REM 部署访问统计系统 (Windows版本)
REM Deploy Analytics System for Windows

echo ==========================================
echo 部署龙驰新材料访问统计系统
echo ==========================================

echo 1. 停止现有服务...
pm2 stop lc-mall-backend 2>nul || echo 后端服务未运行
pm2 stop lc-mall-frontend 2>nul || echo 前端服务未运行

echo 2. 更新后端依赖...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo 后端依赖安装失败！
    pause
    exit /b 1
)

echo 3. 构建前端...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo 前端依赖安装失败！
    pause
    exit /b 1
)

call npm run build
if %errorlevel% neq 0 (
    echo 前端构建失败！
    pause
    exit /b 1
)

echo 4. 启动后端服务...
cd ..\backend
pm2 start src\server.js --name lc-mall-backend
if %errorlevel% neq 0 (
    echo 后端服务启动失败！
    pause
    exit /b 1
)

echo 5. 启动前端服务...
cd ..\frontend
pm2 start npm --name lc-mall-frontend -- start
if %errorlevel% neq 0 (
    echo 前端服务启动失败！
    pause
    exit /b 1
)

echo 6. 检查服务状态...
pm2 status

echo ==========================================
echo 访问统计系统部署完成！
echo ==========================================
echo 后台管理访问统计页面: http://localhost:3000/admin/analytics
echo API端点: http://localhost:5000/api/analytics
echo ==========================================

echo 等待5秒后检查服务健康状态...
timeout /t 5 /nobreak >nul

echo 检查后端API健康状态...
curl -f http://localhost:5000/api/health || echo 后端健康检查失败

echo 部署完成！如有问题请检查日志：
echo 后端日志: pm2 logs lc-mall-backend
echo 前端日志: pm2 logs lc-mall-frontend

pause

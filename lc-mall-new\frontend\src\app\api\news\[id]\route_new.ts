// frontend/src/app/api/news/[id]/route.ts
import { NextResponse } from 'next/server';
import { NewsArticle, newsCategories, NewsCategory } from '@/shared/types/News';
import { findArticle, incrementViews, mockNewsDb } from '@/shared/mockdata/news';
import { v4 as uuidv4 } from 'uuid';

// --- Redis Client Setup (Placeholder) ---
// import { createClient } from 'redis';
// const redisClient = createClient({ url: process.env.REDIS_URL || 'redis://localhost:6379' });
// redisClient.on('error', (err) => console.error('Redis Client Error', err));
// async function connectRedis() {
//   if (!redisClient.isOpen) {
//     await redisClient.connect();
//   }
// }
// connectRedis();

interface Params {
  id: string;
}

export async function GET(request: Request, { params }: { params: Params }) {
  const { id } = params;
  try {
    // Find article by ID or slug using shared helper
    const article = findArticle(id);
    if (!article) {
      return NextResponse.json({ message: 'News article not found' }, { status: 404 });
    }
    
    // Increment views using shared helper
    incrementViews(id);

    return NextResponse.json({ article });

  } catch (error) {
    console.error(`API Error fetching news article ${id}:`, error);
    return NextResponse.json({ message: 'Error fetching news article', error: (error as Error).message }, { status: 500 });
  }
}

export async function PUT(request: Request, { params }: { params: Params }) {
  const { id } = params;
  try {
    const updatedArticleData = await request.json() as Partial<Omit<NewsArticle, 'id' | 'createdAt' | 'slug'>>;

    // Update mock DB
    const articleIndex = mockNewsDb.findIndex(n => n.id === id || n.slug === id);
    if (articleIndex === -1) {
      return NextResponse.json({ message: 'News article not found for update' }, { status: 404 });
    }
    
    mockNewsDb[articleIndex] = { 
      ...mockNewsDb[articleIndex], 
      ...updatedArticleData, 
      updatedAt: new Date().toISOString(),
      category: (() => {
        const existingCategory = mockNewsDb[articleIndex].category;
        if (typeof updatedArticleData.category === 'string') {
          const foundCategory = newsCategories.find(c => c.id === updatedArticleData.category);
          return foundCategory || existingCategory;
        } else if (updatedArticleData.category && typeof updatedArticleData.category === 'object' && 'id' in updatedArticleData.category) {
          return updatedArticleData.category as NewsCategory;
        }
        return existingCategory;
      })(),
    };

    return NextResponse.json({ message: 'News article updated successfully', article: mockNewsDb[articleIndex] });

  } catch (error) {
    console.error(`API Error updating news article ${id}:`, error);
    return NextResponse.json({ message: 'Error updating news article', error: (error as Error).message }, { status: 500 });
  }
}

export async function DELETE(request: Request, { params }: { params: Params }) {
  const { id } = params;
  try {
    // Delete from mock DB - support both ID and slug
    const initialLength = mockNewsDb.length;
    const filteredDb = mockNewsDb.filter(n => n.id !== id && n.slug !== id);
    if (filteredDb.length === initialLength) {
      return NextResponse.json({ message: 'News article not found for deletion' }, { status: 404 });
    }
    
    // Update the shared mock database
    mockNewsDb.length = 0;
    mockNewsDb.push(...filteredDb);

    return NextResponse.json({ message: 'News article deleted successfully' }, { status: 200 });

  } catch (error) {
    console.error(`API Error deleting news article ${id}:`, error);
    return NextResponse.json({ message: 'Error deleting news article', error: (error as Error).message }, { status: 500 });
  }
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-subtokenize";
exports.ids = ["vendor-chunks/micromark-util-subtokenize"];
exports.modules = {

/***/ "(rsc)/../node_modules/micromark-util-subtokenize/dev/index.js":
/*!***************************************************************!*\
  !*** ../node_modules/micromark-util-subtokenize/dev/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* reexport safe */ _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer),\n/* harmony export */   subtokenize: () => (/* binding */ subtokenize)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-chunked */ \"(rsc)/../node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/splice-buffer.js */ \"(rsc)/../node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\");\n/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */ \n\n\n\n// Hidden API exposed for testing.\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */ // eslint-disable-next-line complexity\nfunction subtokenize(eventsArray) {\n    /** @type {Record<string, number>} */ const jumps = {};\n    let index = -1;\n    /** @type {Event} */ let event;\n    /** @type {number | undefined} */ let lineIndex;\n    /** @type {number} */ let otherIndex;\n    /** @type {Event} */ let otherEvent;\n    /** @type {Array<Event>} */ let parameters;\n    /** @type {Array<Event>} */ let subevents;\n    /** @type {boolean | undefined} */ let more;\n    const events = new _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer(eventsArray);\n    while(++index < events.length){\n        while(index in jumps){\n            index = jumps[index];\n        }\n        event = events.get(index);\n        // Add a hook for the GFM tasklist extension, which needs to know if text\n        // is in the first content of a list item.\n        if (index && event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow && events.get(index - 1)[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefix) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(event[1]._tokenizer, \"expected `_tokenizer` on subtokens\");\n            subevents = event[1]._tokenizer.events;\n            otherIndex = 0;\n            if (otherIndex < subevents.length && subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank) {\n                otherIndex += 2;\n            }\n            if (otherIndex < subevents.length && subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n                while(++otherIndex < subevents.length){\n                    if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n                        break;\n                    }\n                    if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkText) {\n                        subevents[otherIndex][1]._isInFirstContentOfListItem = true;\n                        otherIndex++;\n                    }\n                }\n            }\n        }\n        // Enter.\n        if (event[0] === \"enter\") {\n            if (event[1].contentType) {\n                Object.assign(jumps, subcontent(events, index));\n                index = jumps[index];\n                more = true;\n            }\n        } else if (event[1]._container) {\n            otherIndex = index;\n            lineIndex = undefined;\n            while(otherIndex--){\n                otherEvent = events.get(otherIndex);\n                if (otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding || otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank) {\n                    if (otherEvent[0] === \"enter\") {\n                        if (lineIndex) {\n                            events.get(lineIndex)[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank;\n                        }\n                        otherEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding;\n                        lineIndex = otherIndex;\n                    }\n                } else if (otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix || otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemIndent) {\n                // Move past.\n                } else {\n                    break;\n                }\n            }\n            if (lineIndex) {\n                // Fix position.\n                event[1].end = {\n                    ...events.get(lineIndex)[1].start\n                };\n                // Switch container exit w/ line endings.\n                parameters = events.slice(lineIndex, index);\n                parameters.unshift(event);\n                events.splice(lineIndex, index - lineIndex + 1, parameters);\n            }\n        }\n    }\n    // The changes to the `events` buffer must be copied back into the eventsArray\n    (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__.splice)(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0));\n    return !more;\n}\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */ function subcontent(events, eventIndex) {\n    const token = events.get(eventIndex)[1];\n    const context = events.get(eventIndex)[2];\n    let startPosition = eventIndex - 1;\n    /** @type {Array<number>} */ const startPositions = [];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(token.contentType, \"expected `contentType` on subtokens\");\n    let tokenizer = token._tokenizer;\n    if (!tokenizer) {\n        tokenizer = context.parser[token.contentType](token.start);\n        if (token._contentTypeTextTrailing) {\n            tokenizer._contentTypeTextTrailing = true;\n        }\n    }\n    const childEvents = tokenizer.events;\n    /** @type {Array<[number, number]>} */ const jumps = [];\n    /** @type {Record<string, number>} */ const gaps = {};\n    /** @type {Array<Chunk>} */ let stream;\n    /** @type {Token | undefined} */ let previous;\n    let index = -1;\n    /** @type {Token | undefined} */ let current = token;\n    let adjust = 0;\n    let start = 0;\n    const breaks = [\n        start\n    ];\n    // Loop forward through the linked tokens to pass them in order to the\n    // subtokenizer.\n    while(current){\n        // Find the position of the event for this token.\n        while(events.get(++startPosition)[1] !== current){\n        // Empty.\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!previous || current.previous === previous, \"expected previous to match\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!previous || previous.next === current, \"expected next to match\");\n        startPositions.push(startPosition);\n        if (!current._tokenizer) {\n            stream = context.sliceStream(current);\n            if (!current.next) {\n                stream.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof);\n            }\n            if (previous) {\n                tokenizer.defineSkip(current.start);\n            }\n            if (current._isInFirstContentOfListItem) {\n                tokenizer._gfmTasklistFirstContentOfListItem = true;\n            }\n            tokenizer.write(stream);\n            if (current._isInFirstContentOfListItem) {\n                tokenizer._gfmTasklistFirstContentOfListItem = undefined;\n            }\n        }\n        // Unravel the next token.\n        previous = current;\n        current = current.next;\n    }\n    // Now, loop back through all events (and linked tokens), to figure out which\n    // parts belong where.\n    current = token;\n    while(++index < childEvents.length){\n        if (// Find a void token that includes a break.\n        childEvents[index][0] === \"exit\" && childEvents[index - 1][0] === \"enter\" && childEvents[index][1].type === childEvents[index - 1][1].type && childEvents[index][1].start.line !== childEvents[index][1].end.line) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(current, \"expected a current token\");\n            start = index + 1;\n            breaks.push(start);\n            // Help GC.\n            current._tokenizer = undefined;\n            current.previous = undefined;\n            current = current.next;\n        }\n    }\n    // Help GC.\n    tokenizer.events = [];\n    // If there’s one more token (which is the cases for lines that end in an\n    // EOF), that’s perfect: the last point we found starts it.\n    // If there isn’t then make sure any remaining content is added to it.\n    if (current) {\n        // Help GC.\n        current._tokenizer = undefined;\n        current.previous = undefined;\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!current.next, \"expected no next token\");\n    } else {\n        breaks.pop();\n    }\n    // Now splice the events from the subtokenizer into the current events,\n    // moving back to front so that splice indices aren’t affected.\n    index = breaks.length;\n    while(index--){\n        const slice = childEvents.slice(breaks[index], breaks[index + 1]);\n        const start = startPositions.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start !== undefined, \"expected a start position when splicing\");\n        jumps.push([\n            start,\n            start + slice.length - 1\n        ]);\n        events.splice(start, 2, slice);\n    }\n    jumps.reverse();\n    index = -1;\n    while(++index < jumps.length){\n        gaps[adjust + jumps[index][0]] = adjust + jumps[index][1];\n        adjust += jumps[index][1] - jumps[index][0] - 1;\n    }\n    return gaps;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark-util-subtokenize/dev/index.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js":
/*!***************************************************************************!*\
  !*** ../node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* binding */ SpliceBuffer)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/constants.js\");\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */ class SpliceBuffer {\n    /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */ constructor(initial){\n        /** @type {Array<T>} */ this.left = initial ? [\n            ...initial\n        ] : [];\n        /** @type {Array<T>} */ this.right = [];\n    }\n    /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */ get(index) {\n        if (index < 0 || index >= this.left.length + this.right.length) {\n            throw new RangeError(\"Cannot access index `\" + index + \"` in a splice buffer of size `\" + (this.left.length + this.right.length) + \"`\");\n        }\n        if (index < this.left.length) return this.left[index];\n        return this.right[this.right.length - index + this.left.length - 1];\n    }\n    /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */ get length() {\n        return this.left.length + this.right.length;\n    }\n    /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */ shift() {\n        this.setCursor(0);\n        return this.right.pop();\n    }\n    /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */ slice(start, end) {\n        /** @type {number} */ const stop = end === null || end === undefined ? Number.POSITIVE_INFINITY : end;\n        if (stop < this.left.length) {\n            return this.left.slice(start, stop);\n        }\n        if (start > this.left.length) {\n            return this.right.slice(this.right.length - stop + this.left.length, this.right.length - start + this.left.length).reverse();\n        }\n        return this.left.slice(start).concat(this.right.slice(this.right.length - stop + this.left.length).reverse());\n    }\n    /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */ splice(start, deleteCount, items) {\n        /** @type {number} */ const count = deleteCount || 0;\n        this.setCursor(Math.trunc(start));\n        const removed = this.right.splice(this.right.length - count, Number.POSITIVE_INFINITY);\n        if (items) chunkedPush(this.left, items);\n        return removed.reverse();\n    }\n    /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */ pop() {\n        this.setCursor(Number.POSITIVE_INFINITY);\n        return this.left.pop();\n    }\n    /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */ push(item) {\n        this.setCursor(Number.POSITIVE_INFINITY);\n        this.left.push(item);\n    }\n    /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */ pushMany(items) {\n        this.setCursor(Number.POSITIVE_INFINITY);\n        chunkedPush(this.left, items);\n    }\n    /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */ unshift(item) {\n        this.setCursor(0);\n        this.right.push(item);\n    }\n    /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */ unshiftMany(items) {\n        this.setCursor(0);\n        chunkedPush(this.right, items.reverse());\n    }\n    /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */ setCursor(n) {\n        if (n === this.left.length || n > this.left.length && this.right.length === 0 || n < 0 && this.left.length === 0) return;\n        if (n < this.left.length) {\n            // Move cursor to the this.left\n            const removed = this.left.splice(n, Number.POSITIVE_INFINITY);\n            chunkedPush(this.right, removed.reverse());\n        } else {\n            // Move cursor to the this.right\n            const removed = this.right.splice(this.left.length + this.right.length - n, Number.POSITIVE_INFINITY);\n            chunkedPush(this.left, removed.reverse());\n        }\n    }\n}\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */ function chunkedPush(list, right) {\n    /** @type {number} */ let chunkStart = 0;\n    if (right.length < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize) {\n        list.push(...right);\n    } else {\n        while(chunkStart < right.length){\n            list.push(...right.slice(chunkStart, chunkStart + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize));\n            chunkStart += micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\n");

/***/ })

};
;
#!/bin/bash

echo "=========================================="
echo "    LC Mall - Frontend Fix Script        "
echo "=========================================="

# 检查是否在正确的目录
if [ ! -f "package.json" ] && [ ! -d "frontend" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 进入项目根目录
if [ -d "frontend" ]; then
    echo "📁 当前在项目根目录"
else
    echo "📁 当前在前端目录"
    cd ..
fi

# 加载环境变量
if [ -f ".env.production" ]; then
    echo "📋 加载生产环境变量..."
    export $(grep -v '^#' .env.production | xargs)
    echo "🌐 NEXT_PUBLIC_API_URL: $NEXT_PUBLIC_API_URL"
else
    echo "⚠️  警告: .env.production 文件不存在"
    export NEXT_PUBLIC_API_URL="http://gdlongchi.cn:5000/api"
    echo "🌐 使用默认 API URL: $NEXT_PUBLIC_API_URL"
fi

# 停止前端服务
echo "🛑 停止现有前端服务..."
pkill -f "next start" || true
sleep 2

# 进入前端目录
cd frontend

# 清理缓存和构建文件
echo "🧹 清理前端缓存和构建文件..."
rm -rf .next
rm -rf node_modules/.cache
npm cache clean --force

# 重新安装依赖（如果需要）
if [ "$1" = "--reinstall" ]; then
    echo "📦 重新安装前端依赖..."
    rm -rf node_modules package-lock.json
    npm install --include=dev
fi

# 重新构建前端
echo "🔨 重新构建前端应用..."
echo "使用 API URL: $NEXT_PUBLIC_API_URL"
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

echo "✅ 前端构建成功"

# 启动前端服务
echo "🚀 启动前端服务..."
nohup npm start > ../logs/frontend.log 2>&1 &
frontend_pid=$!

echo "📝 前端 PID: $frontend_pid"
echo $frontend_pid > ../logs/frontend.pid

# 等待服务启动
echo "⏳ 等待前端服务启动..."
sleep 10

# 检查服务状态
if ps -p $frontend_pid > /dev/null; then
    echo "✅ 前端服务启动成功"
    
    # 测试服务健康
    echo "🔍 测试前端服务..."
    sleep 3
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ 前端健康检查通过"
    else
        echo "⚠️  前端健康检查失败，但服务正在运行"
    fi
    
    # 测试API代理
    echo "🔍 测试前端API代理..."
    if curl -f http://localhost:3000/api/products?limit=1 > /dev/null 2>&1; then
        echo "✅ 前端API代理正常"
    else
        echo "⚠️  前端API代理可能有问题"
    fi
    
else
    echo "❌ 前端服务启动失败"
    echo "📄 前端日志:"
    tail -20 ../logs/frontend.log
    exit 1
fi

cd ..

echo ""
echo "=========================================="
echo "         🎉 前端修复完成!                  "
echo "=========================================="
echo "前端 URL: http://localhost:3000"
echo "前端 PID: $frontend_pid"
echo "日志文件: logs/frontend.log"
echo ""
echo "测试命令:"
echo "  node test-api.js          # 本地测试"
echo "  node test-api.js --prod   # 生产环境测试"
echo ""
echo "如果问题仍然存在，请检查:"
echo "1. 后端服务是否正常运行 (端口 5000)"
echo "2. 环境变量 NEXT_PUBLIC_API_URL 是否正确"
echo "3. 防火墙是否阻止了端口访问"
echo "4. 查看日志: tail -f logs/frontend.log"
echo "=========================================="

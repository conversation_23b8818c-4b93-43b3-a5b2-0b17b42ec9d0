"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_LongChiMall_lc_mall_new_frontend_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/route.ts */ \"(rsc)/./src/app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_LongChiMall_lc_mall_new_frontend_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/products/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/products/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// 禁用路由缓存\nconst dynamic = \"force-dynamic\";\nconst revalidate = 0;\n// Proxy to backend API for products\nasync function GET(request) {\n    try {\n        const apiBase = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n        // 转发URL参数到后端\n        const { searchParams } = new URL(request.url);\n        const backendQuery = new URLSearchParams(searchParams);\n        const backendUrl = `${apiBase}/products?${backendQuery.toString()}`;\n        console.log(\"Frontend API: Fetching from backend:\", backendUrl);\n        console.log(\"Environment NEXT_PUBLIC_API_URL:\", \"http://localhost:5000/api\");\n        // 添加缓存控制头\n        const response = await fetch(backendUrl, {\n            headers: {\n                \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                \"Pragma\": \"no-cache\",\n                \"Expires\": \"0\"\n            }\n        });\n        console.log(\"Backend response status:\", response.status);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Backend API error:\", response.status, errorText);\n            throw new Error(`Backend API request failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log(\"Backend response success:\", result.success, \"Products count:\", result.data?.products?.length);\n        // Backend returns data in ApiResponse format: { success: true, data: { products: [...], pagination: {...} }, message: '' }\n        if (result.success && result.data) {\n            // 返回完整的数据结构，包括products和pagination\n            const responseData = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result.data);\n            // 设置响应头禁用缓存\n            responseData.headers.set(\"Cache-Control\", \"no-cache, no-store, must-revalidate\");\n            responseData.headers.set(\"Pragma\", \"no-cache\");\n            responseData.headers.set(\"Expires\", \"0\");\n            return responseData;\n        } else {\n            console.error(\"Invalid backend response format:\", result);\n            throw new Error(\"Invalid backend response format\");\n        }\n    } catch (error) {\n        console.error(\"Error proxying to backend:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch products\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Get single product by ID\nasync function POST(request) {\n    let id;\n    try {\n        const requestBody = await request.json();\n        id = requestBody.id;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Product ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const apiBase = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n        const response = await fetch(`${apiBase}/products/${id}`);\n        if (!response.ok) {\n            if (response.status === 404) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Product not found\"\n                }, {\n                    status: 404\n                });\n            }\n            throw new Error(\"Backend API request failed\");\n        }\n        const result = await response.json();\n        if (result.success && result.data) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result.data);\n        } else {\n            throw new Error(\"Invalid backend response format\");\n        }\n    } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Product not found\"\n        }, {\n            status: 404\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
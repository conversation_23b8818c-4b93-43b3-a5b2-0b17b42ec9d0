"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_LongChiMall_lc_mall_new_frontend_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/route.ts */ \"(rsc)/./src/app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_LongChiMall_lc_mall_new_frontend_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/products/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/products/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// 禁用路由缓存\nconst dynamic = \"force-dynamic\";\nconst revalidate = 0;\n// Proxy to backend API for products\nasync function GET(request) {\n    try {\n        const apiBase = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n        // 获取URL参数，处理可能的重定向问题\n        const url = new URL(request.url);\n        const searchParams = url.searchParams;\n        // 构建后端查询参数\n        const backendQuery = new URLSearchParams();\n        searchParams.forEach((value, key)=>{\n            backendQuery.append(key, value);\n        });\n        const backendUrl = `${apiBase}/products${backendQuery.toString() ? \"?\" + backendQuery.toString() : \"\"}`;\n        console.log(\"Frontend API: Fetching from backend:\", backendUrl);\n        console.log(\"Environment NEXT_PUBLIC_API_URL:\", \"http://localhost:5000/api\");\n        console.log(\"Request URL:\", request.url);\n        console.log(\"Search params:\", Array.from(searchParams.entries()));\n        // 添加缓存控制头和超时设置\n        const response = await fetch(backendUrl, {\n            method: \"GET\",\n            headers: {\n                \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                \"Pragma\": \"no-cache\",\n                \"Expires\": \"0\",\n                \"Content-Type\": \"application/json\"\n            },\n            // 添加超时处理\n            signal: AbortSignal.timeout(10000) // 10秒超时\n        });\n        console.log(\"Backend response status:\", response.status);\n        console.log(\"Backend response headers:\", Object.fromEntries(response.headers.entries()));\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Backend API error:\", response.status, errorText);\n            throw new Error(`Backend API request failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log(\"Backend response success:\", result.success, \"Products count:\", result.data?.products?.length);\n        // Backend returns data in ApiResponse format: { success: true, data: { products: [...], pagination: {...} }, message: '' }\n        if (result.success && result.data) {\n            // 返回完整的数据结构，包括products和pagination\n            const responseData = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result.data);\n            // 设置响应头禁用缓存\n            responseData.headers.set(\"Cache-Control\", \"no-cache, no-store, must-revalidate\");\n            responseData.headers.set(\"Pragma\", \"no-cache\");\n            responseData.headers.set(\"Expires\", \"0\");\n            responseData.headers.set(\"Content-Type\", \"application/json\");\n            return responseData;\n        } else {\n            console.error(\"Invalid backend response format:\", result);\n            throw new Error(\"Invalid backend response format\");\n        }\n    } catch (error) {\n        console.error(\"Error proxying to backend:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch products\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Cache-Control\": \"no-cache\"\n            }\n        });\n    }\n}\n// Get single product by ID\nasync function POST(request) {\n    let id;\n    try {\n        const requestBody = await request.json();\n        id = requestBody.id;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Product ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const apiBase = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n        const response = await fetch(`${apiBase}/products/${id}`);\n        if (!response.ok) {\n            if (response.status === 404) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Product not found\"\n                }, {\n                    status: 404\n                });\n            }\n            throw new Error(\"Backend API request failed\");\n        }\n        const result = await response.json();\n        if (result.success && result.data) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result.data);\n        } else {\n            throw new Error(\"Invalid backend response format\");\n        }\n    } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Product not found\"\n        }, {\n            status: 404\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
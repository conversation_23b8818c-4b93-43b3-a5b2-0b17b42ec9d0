{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:07"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:07"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:07"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:07"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:07"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:07"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:07"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:08"}
{"environment":"production","level":"info","message":"Analytics record saved: 1363f925-42b1-42b0-b2ae-e9dbfc893078","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:21"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:07:21 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:21"}
{"environment":"production","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:26"}
{"environment":"production","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:26"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5hc3g3gbbygy5levm","responseSize":10704,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 08:07:26"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5hc3g5iks4d3y066","responseSize":10703,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 08:07:26"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:07:26 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:26"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:07:26 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:26"}
{"environment":"production","level":"info","message":"Analytics record saved: 50044139-e68d-479f-a9af-e3f9d1521a90","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:26"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:07:26 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:26"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:07:26 +0000] \"GET /images/products/WL-5201.jpg HTTP/1.1\" 404 90 \"http://localhost:3000/admin/products/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:26"}
{"environment":"production","level":"info","message":"Analytics record saved: c24ec94c-bc03-4fb5-a6a3-76399de17edb","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:30"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:07:30 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:30"}
{"environment":"production","level":"info","message":"Analytics record saved: 87910cb4-bef7-4cab-947a-1562769fcdd9","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:30"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:07:30 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:07:30"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"Analytics record saved: 604afba1-b3a6-41e7-b9ec-1c477887e4d1","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:08:29 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:29"}
{"environment":"production","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:30"}
{"environment":"production","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:30"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5hdgcwmbxypfi96rd","responseSize":34858,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 08:08:30"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5hdgcz41bc69l1ws","responseSize":34857,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 08:08:30"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:08:30 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:30"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:08:30 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:30"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:08:30 +0000] \"GET /images/products/WL-5201.jpg HTTP/1.1\" 404 90 \"http://localhost:3000/products/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:30"}
{"environment":"production","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:41"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:44"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:44"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:44"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:44"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:44"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:44"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:44"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:45"}
{"environment":"production","level":"info","message":"Analytics record saved: bc5fe185-88d0-417d-888c-6eeb8368bb5f","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:56"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:08:56 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:56"}
{"environment":"production","level":"info","message":"Analytics record saved: 0969b0be-7968-4d32-80ac-75038673487b","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:57"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:08:57 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:57"}
{"environment":"production","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:57"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5he1kctxx80h8kzzb","responseSize":34858,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 08:08:57"}
{"environment":"production","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:57"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:08:57 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:57"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5he1kkbysw8fes1se","responseSize":34858,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 08:08:57"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:08:57 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:57"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:08:57 +0000] \"GET /images/products/WL-5201.jpg HTTP/1.1\" 404 90 \"http://localhost:3000/products/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:08:57"}
{"environment":"production","level":"info","message":"Analytics record saved: ffd48d4a-c46e-4cd6-a51a-d4ec0d3b1a2b","service":"lc-mall-backend","timestamp":"2025-06-21 08:09:40"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:00:09:40 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 08:09:40"}
{"environment":"production","level":"info","message":"Analytics record saved: ec5986e8-574f-48dc-b46d-340eb9a88256","service":"lc-mall-backend","timestamp":"2025-06-21 10:11:05"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:02:11:05 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 10:11:05"}
{"environment":"production","level":"info","message":"Analytics record saved: 7b57efc5-9e0d-46ec-aaa0-5c0a18e0ffec","service":"lc-mall-backend","timestamp":"2025-06-21 10:11:05"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:02:11:05 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 10:11:05"}
{"environment":"production","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-21 10:11:06"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5lr46fnhojdxxhyxi","responseSize":34858,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 10:11:06"}
{"environment":"production","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-21 10:11:06"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:02:11:06 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 10:11:06"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5lr46mydw2gqyq6d","responseSize":34857,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 10:11:06"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:02:11:06 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 10:11:06"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:02:11:06 +0000] \"GET /images/products/WL-5201.jpg HTTP/1.1\" 404 90 \"http://localhost:3000/products/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 10:11:06"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:01:54"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:01:54"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:01:54"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:01:54"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:01:54"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:01:54"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:01:54"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:01:54"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:02:52"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:02:52"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:02:52"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:02:52"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:02:52"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:02:52"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:02:52"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:02:52"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:03:39"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:03:39"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:03:39"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:03:39"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:03:39"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:03:39"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:03:39"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:03:39"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:42"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:42"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:42"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:42"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:42"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:42"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:42"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:42"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:59"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:59"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:59"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:59"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:59"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:59"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:59"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:04:59"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:05:51"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:05:51"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:05:51"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:05:51"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:05:51"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:05:51"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:05:51"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:05:51"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:06:48"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:06:48"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:06:48"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:06:48"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:06:48"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:06:48"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:06:48"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:06:48"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:07:55"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:07:55"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:07:55"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:07:55"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:07:55"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:07:55"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:07:55"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:07:56"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:08:40"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:08:40"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:08:40"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:08:40"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:08:40"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:08:40"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:08:40"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:08:40"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:29"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:29"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:29"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:29"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:29"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:29"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:29"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:30"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:53"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:53"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:53"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:53"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:53"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:53"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:53"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:09:54"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:10:30"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:10:30"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:10:30"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:10:30"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:10:30"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:10:30"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:10:30"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:10:31"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:11:28"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:11:28"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:11:28"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:11:28"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:11:28"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:11:28"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:11:28"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:11:29"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:22"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:22"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:22"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:22"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:22"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:22"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:22"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:23"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:30"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:30"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:30"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:30"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:30"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:30"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:30"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:31"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:47"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:47"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:47"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:47"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:47"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:47"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:47"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:12:47"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:13:49"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:13:49"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:13:49"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 11:13:49"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:13:49"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:13:49"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:13:49"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:13:49"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:16:00"}
{"environment":"development","error":"pipeline.hgetAll is not a function","keysCount":3,"level":"error","message":"Batch get error:","service":"lc-mall-backend","timestamp":"2025-06-21 11:16:00"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:16:45"}
{"count":5,"environment":"development","level":"info","message":"Products retrieved successfully","page":1,"service":"lc-mall-backend","timestamp":"2025-06-21 11:16:45","total":31}
{"environment":"development","level":"info","message":"Product retrieved successfully","name":"TDI-80 聚氨酯原料","productId":"sample-001","service":"lc-mall-backend","timestamp":"2025-06-21 11:16:45"}
{"count":0,"environment":"development","level":"info","message":"Product search completed","query":"钢","service":"lc-mall-backend","timestamp":"2025-06-21 11:16:45","total":0}
{"categoriesCount":10,"environment":"development","level":"info","message":"Product categories retrieved","service":"lc-mall-backend","timestamp":"2025-06-21 11:16:45"}
{"count":20,"environment":"development","level":"info","message":"Products retrieved successfully","page":1,"service":"lc-mall-backend","timestamp":"2025-06-21 11:16:45","total":31}
{"count":5,"environment":"development","level":"info","message":"Product search completed","query":"","service":"lc-mall-backend","timestamp":"2025-06-21 11:16:45","total":31}
{"active":31,"categoriesCount":10,"environment":"development","featured":9,"hot":10,"inStock":28,"inactive":0,"level":"info","message":"Product statistics calculated","new":0,"outOfStock":3,"service":"lc-mall-backend","timestamp":"2025-06-21 11:16:45","total":31,"totalViews":0}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:39:50"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:39:50"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:39:50"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:39:50"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:39:50"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:39:50"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:39:50"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:39:50"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:15"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:15"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:15"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:15"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:15"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:15"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:15"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:16"}
{"environment":"development","level":"info","message":"日志模块测试","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:24"}
{"environment":"development","level":"warn","message":"Email service is disabled","service":"lc-mall-backend","timestamp":"2025-06-21 11:40:24"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:01"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:01"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:01"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:01"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:01"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:01"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:01"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:01"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:23"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:23"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:23"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:23"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:23"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:23"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:41:23"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:42:13"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:44:42"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:44:42"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:44:42"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:44:42"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:44:42"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:44:42"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:44:42"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:44:42"}
{"environment":"development","level":"info","message":"Analytics record saved: ff248a30-b8da-46a4-a7e8-30f401a55f2b","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:18"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:45:18 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:18"}
{"environment":"development","level":"info","message":"Analytics record saved: 58ab1de0-ba32-4364-823e-e77eb2ecb927","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:28"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:45:28 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:28"}
{"environment":"development","level":"info","message":"Analytics record saved: 3b107b7b-81c2-4072-b3f3-7fb0ec3730dc","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:45:29 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:45:29 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 500 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:45:29 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 500 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:45:29 +0000] \"OPTIONS /api/products?limit=50 HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:45:29 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 500 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:45:29 +0000] \"OPTIONS /api/products?limit=50 HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"error","message":"Unhandled error: productService.getProducts is not a function","service":"lc-mall-backend","stack":"TypeError: productService.getProducts is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:27:41\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\errorHandler.js:80:21\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\middleware\\validation.js:35:5\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:45:29 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 500 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:45:29"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:11"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:12"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:46:12"}
{"environment":"development","level":"info","message":"Analytics record saved: 21f8eb45-5b3e-463c-b525-6fb221bf7fad","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:58:09 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"Analytics record saved: 76ca0e4e-9f27-4717-8884-1fea39f3fa2e","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:58:09 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"debug","limit":50,"message":"Getting products with options:","page":1,"service":"lc-mall-backend","sortBy":"createdAt","sortOrder":"desc","timestamp":"2025-06-21 11:58:09"}
{"count":31,"environment":"development","level":"info","message":"Products retrieved successfully","page":1,"service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09","total":31}
{"environment":"development","level":"error","message":"Unhandled error: ApiResponse.success is not a function","service":"lc-mall-backend","stack":"TypeError: ApiResponse.success is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:30:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:58:09 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 500 310 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"debug","limit":50,"message":"Getting products with options:","page":1,"service":"lc-mall-backend","sortBy":"createdAt","sortOrder":"desc","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:58:09 +0000] \"OPTIONS /api/products?limit=50 HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"debug","limit":50,"message":"Getting products with options:","page":1,"service":"lc-mall-backend","sortBy":"createdAt","sortOrder":"desc","timestamp":"2025-06-21 11:58:09"}
{"count":31,"environment":"development","level":"info","message":"Products retrieved successfully","page":1,"service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09","total":31}
{"environment":"development","level":"error","message":"Unhandled error: ApiResponse.success is not a function","service":"lc-mall-backend","stack":"TypeError: ApiResponse.success is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:30:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:58:09 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 500 310 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"count":31,"environment":"development","level":"info","message":"Products retrieved successfully","page":1,"service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09","total":31}
{"environment":"development","level":"error","message":"Unhandled error: ApiResponse.success is not a function","service":"lc-mall-backend","stack":"TypeError: ApiResponse.success is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:30:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:58:09 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 500 310 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:58:09 +0000] \"OPTIONS /api/products?limit=50 HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"debug","limit":50,"message":"Getting products with options:","page":1,"service":"lc-mall-backend","sortBy":"createdAt","sortOrder":"desc","timestamp":"2025-06-21 11:58:09"}
{"count":31,"environment":"development","level":"info","message":"Products retrieved successfully","page":1,"service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09","total":31}
{"environment":"development","level":"error","message":"Unhandled error: ApiResponse.success is not a function","service":"lc-mall-backend","stack":"TypeError: ApiResponse.success is not a function\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:30:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:58:09 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 500 310 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:09"}
{"environment":"development","level":"info","message":"Analytics record saved: 0f0c5939-3225-48f1-961b-06bcbc9dbdca","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:16"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:03:58:16 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 11:58:16"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:58"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:59"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 11:59:59"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:00:18"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:13"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:14"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:01:14"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:01"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:44"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:44"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:44"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:44"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:44"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:44"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:44"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:45"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:58"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:58"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:58"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:58"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:58"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:58"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:02:59"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:12"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:12"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:12"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:12"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:12"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:12"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:12"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:12"}
{"environment":"development","level":"info","message":"日志模块测试","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:23"}
{"environment":"development","level":"warn","message":"Email service is disabled","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:23"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5ptgpmt91646vg5ts","responseSize":2365,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:04:54"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:04:54 +0000] \"GET /api/products?limit=3 HTTP/1.1\" 200 2995 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:04:54"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:05:12"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:05:12"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:05:12"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:05:12"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:05:12"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:05:12"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:05:12"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:06:33"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:06:33"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:06:33"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:06:33"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:06:33"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:06:33"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:06:33"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:14"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:14"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:14"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:14"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:14"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:14"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:14"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:14"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:45"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:45"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:45"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:45"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:45"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:45"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:07:45"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:08:28"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:08:28"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:08:28"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-21 12:08:28"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:08:28"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:08:28"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:08:28"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:08:28"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:09:13 +0000] \"GET /health HTTP/1.1\" 200 260 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:09:13"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5pz7ukgznzxh26l5k","responseSize":2374,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:09:22"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:09:22 +0000] \"GET /api/products?limit=3 HTTP/1.1\" 200 3004 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:09:22"}
{"environment":"development","level":"info","message":"Analytics record saved: f9fb9183-9a98-4da1-b345-62e8ebb874ce","service":"lc-mall-backend","timestamp":"2025-06-21 12:09:31"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:09:31 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:09:31"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5pzew59whmqrxgode","responseSize":35903,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:09:31"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:09:31 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:09:31"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5pzewa10sam6uneee","responseSize":35903,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:09:31"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:09:31 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:09:31"}
{"environment":"development","level":"info","message":"Analytics record saved: ebd6413c-bc20-437c-990c-ef39d9892ab8","service":"lc-mall-backend","timestamp":"2025-06-21 12:09:58"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:09:58 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:09:58"}
{"environment":"development","level":"info","message":"Analytics record saved: c042e5f3-fb5a-4224-b667-1224b797f25a","service":"lc-mall-backend","timestamp":"2025-06-21 12:10:39"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:10:39 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:10:39"}
{"environment":"development","level":"info","message":"Analytics record saved: 15fc9807-89f9-4260-9fdc-f07f4fb5ffe7","service":"lc-mall-backend","timestamp":"2025-06-21 12:10:39"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:10:39 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:10:39"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5q0vxuw2fgshwcll","responseSize":35902,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:10:40"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:10:40 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:10:40"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5q0vxzd6swdqgd4dq","responseSize":35903,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:10:40"}
{"environment":"development","level":"info","message":"::1 - - [21/Jun/2025:04:10:40 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:10:40"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:08"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:41"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:41"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:42"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:42"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:42"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:42"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:42"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:42"}
{"environment":"production","level":"info","message":"Analytics record saved: 412779de-d474-40b6-9102-0034136ee84d","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:51"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:11:51 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:51"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5q2gwe6psogideomq","responseSize":35961,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:11:54"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5q2gwicivisqdyd3","responseSize":35960,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:11:54"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:11:54 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:54"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:11:54 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:54"}
{"environment":"production","level":"info","message":"Analytics record saved: 5cb4b03e-e7f8-4448-a397-76a4612c5b7c","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:54"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:11:54 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:11:54"}
{"environment":"production","level":"info","message":"Analytics record saved: 20929f3b-6cfa-467b-bd13-cc5c924f3348","service":"lc-mall-backend","timestamp":"2025-06-21 12:12:11"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:12:11 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:12:11"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:12:20"}
{"environment":"development","error":"productService.initializeProductData is not a function","level":"error","message":"Product import failed","service":"lc-mall-backend","timestamp":"2025-06-21 12:12:20"}
{"environment":"production","level":"info","message":"Analytics record saved: f6d404dc-703f-4319-bb5a-8431d860766b","service":"lc-mall-backend","timestamp":"2025-06-21 12:12:57"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:12:57 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:12:57"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:13:50"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:13:50"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:13:50"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:13:50"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:13:50"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:13:50"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:13:50"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:13:51"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:15"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:15"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:15"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:15"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:15"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:15"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:15"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:15"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:46"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:46"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:46"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:46"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:46"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:46"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:46"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:14:47"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:11"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:11"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:11"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:11"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:11"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:11"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:11"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:11"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:51"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:51"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:51"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:51"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:51"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:51"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:51"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:51"}
{"environment":"production","level":"info","message":"Analytics record saved: 2705bab4-cd8c-4250-b6ad-89cea092407c","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:59"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:15:59 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:59"}
{"environment":"production","level":"info","message":"Analytics record saved: d2752cce-8443-4c51-8acd-6fee71c2f60d","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:59"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:15:59 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:15:59"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:03"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:03"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:03"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:03"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:03"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:03"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:03"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:03"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product data structure initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product created: 产品样品 50g (ID: 0)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product created: 潜固化剂 WL-1031 (ID: 1)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product created: 潜固化剂 WL-104 (ID: 3)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product created: 潜固化剂 WL-101 (ID: 4)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product created: 生物基树脂LC-450 (ID: 5)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product created: 生物基树脂LC-320 (ID: 6)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product created: 催化剂WL-11 (ID: 7)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product created: 产品样品 50g (ID: 8)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:12"}
{"environment":"development","level":"info","message":"Product created: 潜固化剂 WL-102 (ID: 9)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:13"}
{"environment":"development","level":"info","message":"Product created: 生物基树脂LC-170 (ID: 10)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:13"}
{"environment":"development","level":"info","message":"Product created: 生物基树脂LC-140 (ID: 11)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:13"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-469 (ID: 12)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:13"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-480 (ID: 13)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:13"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-470 (ID: 14)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:13"}
{"environment":"development","level":"info","message":"Product created: 流平剂 LC-310 (ID: 15)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:13"}
{"environment":"development","level":"info","message":"Product created: 流平剂 LC-312 (ID: 16)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:13"}
{"environment":"development","level":"info","message":"Product created: 分散剂WL-201 (ID: 17)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:13"}
{"environment":"development","level":"info","message":"Product created: 润湿分散剂 WL-204A (ID: 18)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"development","level":"info","message":"Product created: 降粘分散剂 WL-210 (ID: 19)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"development","level":"info","message":"Product created: 分散剂 WL-244 (ID: 20)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"development","level":"info","message":"Product created: 消泡剂 WL-300 (ID: 21)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"development","level":"info","message":"Product created: 消泡剂 WL-301 (ID: 22)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"production","level":"info","message":"Analytics record saved: 4f781c63-89f2-4d71-a0ab-35d7396d7e91","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:16:14 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"development","level":"info","message":"Product created: 消泡剂 WL-319 (ID: 31)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"development","level":"info","message":"Product created: 油性消泡剂 WL-3066 (ID: 32)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"development","level":"info","message":"Product created: 防变剂 WL-5410 (ID: 33)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"development","level":"info","message":"Product created: 防沉触变剂 WL-5201 (ID: 34)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:14"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-469 (ID: 35)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:15"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-470 (ID: 36)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:15"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-480 (ID: 37)","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:15"}
{"environment":"production","level":"info","message":"Analytics record saved: 481f4cd8-690a-406f-b263-b2fadcb5ee7f","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:22"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:16:22 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:22"}
{"environment":"production","level":"info","message":"Analytics record saved: d1080239-86d6-442a-9ce8-2b563141398a","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:22"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:16:22 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:22"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5q8817u7xoula9jn","responseSize":35908,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:16:22"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:16:22 +0000] \"GET /api/products?page=1&limit=100 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:22"}
{"environment":"production","level":"debug","message":"获取文章列表成功，共 0 篇文章","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:22"}
{"environment":"production","level":"debug","message":"API Response 获取新闻列表成功","requestId":"mc5q881h2qy5x61x0rg","responseSize":738,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:16:22"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:16:22 +0000] \"GET /api/news?page=1&limit=999 HTTP/1.1\" 200 972 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:22"}
{"environment":"production","level":"info","message":"Analytics record saved: 607dcded-7916-4565-98c6-09b4d16b6022","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:29"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:16:29 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:29"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5q8etdig7cxw4aqb","responseSize":35907,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:16:31"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:16:31 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:31"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5q8etit6acpk7cxl","responseSize":35907,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:16:31"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:16:31 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:31"}
{"environment":"production","level":"info","message":"Analytics record saved: 30b49670-504f-4ad1-b40a-ff13ec23371e","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:31"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:16:31 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:31"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:52"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:52"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:52"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:52"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:52"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:52"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:52"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:16:52"}
{"environment":"production","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-21 12:17:22"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:17"}
{"environment":"development","level":"info","message":"Product data structure initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:17"}
{"environment":"development","level":"info","message":"Product created: 产品样品 50g (ID: 0)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:17"}
{"environment":"development","level":"info","message":"Product created: 潜固化剂 WL-1031 (ID: 1)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:17"}
{"environment":"development","level":"info","message":"Product created: 潜固化剂 WL-104 (ID: 3)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:17"}
{"environment":"development","level":"info","message":"Product created: 潜固化剂 WL-101 (ID: 4)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:17"}
{"environment":"development","level":"info","message":"Product created: 生物基树脂LC-450 (ID: 5)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:17"}
{"environment":"development","level":"info","message":"Product created: 生物基树脂LC-320 (ID: 6)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:17"}
{"environment":"development","level":"info","message":"Product created: 催化剂WL-11 (ID: 7)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:18"}
{"environment":"development","level":"info","message":"Product created: 产品样品 50g (ID: 8)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:18"}
{"environment":"development","level":"info","message":"Product created: 潜固化剂 WL-102 (ID: 9)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:18"}
{"environment":"development","level":"info","message":"Product created: 生物基树脂LC-170 (ID: 10)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:18"}
{"environment":"development","level":"info","message":"Product created: 生物基树脂LC-140 (ID: 11)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:18"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-469 (ID: 12)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:18"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-480 (ID: 13)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:18"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-470 (ID: 14)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:18"}
{"environment":"development","level":"info","message":"Product created: 流平剂 LC-310 (ID: 15)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:18"}
{"environment":"development","level":"info","message":"Product created: 流平剂 LC-312 (ID: 16)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:19"}
{"environment":"development","level":"info","message":"Product created: 分散剂WL-201 (ID: 17)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:19"}
{"environment":"development","level":"info","message":"Product created: 润湿分散剂 WL-204A (ID: 18)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:19"}
{"environment":"development","level":"info","message":"Product created: 降粘分散剂 WL-210 (ID: 19)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:19"}
{"environment":"development","level":"info","message":"Product created: 分散剂 WL-244 (ID: 20)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:19"}
{"environment":"development","level":"info","message":"Product created: 消泡剂 WL-300 (ID: 21)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:19"}
{"environment":"development","level":"info","message":"Product created: 消泡剂 WL-301 (ID: 22)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:19"}
{"environment":"development","level":"info","message":"Product created: 消泡剂 WL-319 (ID: 31)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:19"}
{"environment":"development","level":"info","message":"Product created: 油性消泡剂 WL-3066 (ID: 32)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:19"}
{"environment":"development","level":"info","message":"Product created: 防变剂 WL-5410 (ID: 33)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:20"}
{"environment":"development","level":"info","message":"Product created: 防沉触变剂 WL-5201 (ID: 34)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:20"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-469 (ID: 35)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:20"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-470 (ID: 36)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:20"}
{"environment":"development","level":"info","message":"Product created: 附着力促进剂 WL-480 (ID: 37)","service":"lc-mall-backend","timestamp":"2025-06-21 12:21:20"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:40"}
{"environment":"development","level":"info","message":"Product data structure initialized and Redis cleared","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:40"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 产品样品 50g (ID: 0)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:40"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 潜固化剂 WL-1031 (ID: 1)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:41"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 潜固化剂 WL-104 (ID: 3)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:41"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 潜固化剂 WL-101 (ID: 4)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:41"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 生物基树脂LC-450 (ID: 5)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:41"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 生物基树脂LC-320 (ID: 6)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:41"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 催化剂WL-11 (ID: 7)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:41"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 产品样品 50g (ID: 8)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:41"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 潜固化剂 WL-102 (ID: 9)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:41"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 生物基树脂LC-170 (ID: 10)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:42"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 生物基树脂LC-140 (ID: 11)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:42"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-469 (ID: 12)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:42"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-480 (ID: 13)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:42"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-470 (ID: 14)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:42"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 流平剂 LC-310 (ID: 15)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:42"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 流平剂 LC-312 (ID: 16)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:42"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 分散剂WL-201 (ID: 17)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:42"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 润湿分散剂 WL-204A (ID: 18)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:42"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 降粘分散剂 WL-210 (ID: 19)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 分散剂 WL-244 (ID: 20)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 消泡剂 WL-300 (ID: 21)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 消泡剂 WL-301 (ID: 22)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 消泡剂 WL-319 (ID: 31)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 油性消泡剂 WL-3066 (ID: 32)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 防变剂 WL-5410 (ID: 33)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 防沉触变剂 WL-5201 (ID: 34)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-469 (ID: 35)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-470 (ID: 36)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:43"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-480 (ID: 37)","service":"lc-mall-backend","timestamp":"2025-06-21 12:28:44"}
{"environment":"development","level":"error","message":"Redis smembers error: Cannot read properties of null (reading 'sMembers')","service":"lc-mall-backend","stack":"TypeError: Cannot read properties of null (reading 'sMembers')\n    at RedisService.smembers (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\redisService.js:582:32)\n    at ProductService.loadImportedData (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:714:45)\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:15:10)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:777:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)","timestamp":"2025-06-21 12:29:05"}
{"environment":"development","level":"warn","message":"Failed to load imported data from Redis, using mock data:","service":"lc-mall-backend","timestamp":"2025-06-21 12:29:06"}
{"environment":"development","level":"error","message":"Redis smembers error: Cannot read properties of null (reading 'sMembers')","service":"lc-mall-backend","stack":"TypeError: Cannot read properties of null (reading 'sMembers')\n    at RedisService.smembers (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\redisService.js:582:32)\n    at ProductService.loadImportedData (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:714:45)\n    at ProductService.refreshImportedData (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:767:16)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\test-server.js:31:43\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-06-21 12:29:39"}
{"environment":"development","level":"warn","message":"Failed to load imported data from Redis, using mock data:","service":"lc-mall-backend","timestamp":"2025-06-21 12:29:39"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:31:36"}
{"environment":"development","level":"info","message":"Redis not connected, attempting to connect...","service":"lc-mall-backend","timestamp":"2025-06-21 12:32:46"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:32:46"}
{"environment":"development","level":"info","message":"Loaded 29 imported products from Redis","service":"lc-mall-backend","timestamp":"2025-06-21 12:32:46"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:08"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:08"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:08"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:08"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:08"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:08"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:08"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:08"}
{"environment":"production","level":"info","message":"Analytics record saved: 53afea71-96f3-43c7-abc1-0d13387f6d1c","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:18"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:38:18 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:18"}
{"environment":"production","level":"info","message":"Analytics record saved: 28666143-a388-4044-91ee-43d036b7431d","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:29"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:38:29 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:29"}
{"environment":"production","level":"info","message":"Analytics record saved: 75940ff2-d4ac-47c2-8f58-1cb381b88a82","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:30"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:38:30 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:30"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5r0odvjcbjyhbr4oh","responseSize":35947,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:38:30"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:38:30 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:30"}
{"environment":"production","level":"info","message":"Loaded 29 imported products from Redis","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:30"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5r0oe3mf85gnet4ns","responseSize":29802,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:38:30"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:38:30 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:38:30"}
{"environment":"production","level":"info","message":"Analytics record saved: 63d712d7-73d4-47a1-89b4-f71838153609","service":"lc-mall-backend","timestamp":"2025-06-21 12:39:26"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:39:26 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:39:26"}
{"environment":"production","level":"info","message":"Analytics record saved: 1f9f7b8a-8ad4-48e7-ac88-06813a42340b","service":"lc-mall-backend","timestamp":"2025-06-21 12:39:42"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:39:42 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:39:42"}
{"environment":"production","level":"info","message":"Analytics record saved: 41e873d5-209c-4f6f-a902-96bea8413b65","service":"lc-mall-backend","timestamp":"2025-06-21 12:41:59"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:41:59 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:41:59"}
{"environment":"production","level":"info","message":"Analytics record saved: eab32a73-c852-41c2-badd-0dc0b948d134","service":"lc-mall-backend","timestamp":"2025-06-21 12:42:18"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:42:18 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:42:18"}
{"environment":"production","level":"info","message":"Analytics record saved: 080f553d-d770-45d4-baad-0e252d85020d","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:02"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:02 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:02"}
{"environment":"production","level":"info","message":"Analytics record saved: 00125c30-4c55-4df1-9935-ac3874d43814","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:03"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:03 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:03"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5r6mbm5imq6mcp389","responseSize":9651,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:43:07"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc5r6mbn3v5eq6ofech","responseSize":9651,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:43:07"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:07 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:07"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:07 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:07"}
{"environment":"production","level":"info","message":"Analytics record saved: c5a740c9-0b13-443d-9216-6afcdbe7d02a","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:07"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:07 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:07"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:07 +0000] \"GET /images/products/WL-5201.jpg HTTP/1.1\" 404 90 \"http://localhost:3000/admin/products/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:07"}
{"environment":"production","level":"info","message":"Analytics record saved: 07c1f4bd-e031-4d39-af86-8b28f53c334b","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:12"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:12 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:12"}
{"environment":"production","level":"debug","message":"API Response 产品详情获取成功","requestId":"mc5r6q2xz5znot7gbc","responseSize":1189,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:43:12"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:12 +0000] \"GET /api/products/37 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:12"}
{"environment":"production","level":"debug","message":"API Response 产品详情获取成功","requestId":"mc5r6q5wb6xuxci0ibo","responseSize":1190,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:43:12"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:12 +0000] \"GET /api/products/37 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:12"}
{"environment":"production","level":"info","message":"Analytics record saved: 9db02fcd-e801-4f78-81d6-20290841d614","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:32"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:32 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:32"}
{"environment":"production","level":"info","message":"Analytics record saved: d18f5cca-97d1-4350-85c8-ac721745525c","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:38"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:38 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:38"}
{"apiKey":"lc_admin...","environment":"production","level":"warn","message":"Invalid admin API key attempt","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:38"}
{"environment":"production","error":"Invalid admin API key","ip":"127.0.0.1","level":"warn","message":"API Error Response 无效的管理员API密钥","requestId":"mc5r7a2zr5ly39rvnb","service":"lc-mall-backend","statusCode":401,"timestamp":"2025-06-21 12:43:38","userAgent":"node"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:38 +0000] \"GET /api/analytics/admin/stats?startDate=2025-06-14&endDate=2025-06-21 HTTP/1.1\" 401 129 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:38"}
{"apiKey":"lc_admin...","environment":"production","level":"warn","message":"Invalid admin API key attempt","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:38"}
{"environment":"production","error":"Invalid admin API key","ip":"127.0.0.1","level":"warn","message":"API Error Response 无效的管理员API密钥","requestId":"mc5r7a7iwr2fr8rv1k","service":"lc-mall-backend","statusCode":401,"timestamp":"2025-06-21 12:43:38","userAgent":"node"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:43:38 +0000] \"GET /api/analytics/admin/records?startDate=2025-06-14&endDate=2025-06-21&limit=50&offset=0 HTTP/1.1\" 401 129 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:43:38"}
{"environment":"production","level":"info","message":"Analytics record saved: 10d765a8-02c9-4c72-98e2-e4eb35e96e37","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:01"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:44:01 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:01"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:35"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:35"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:35"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:35"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:35"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:35"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:35"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:36"}
{"code":"NOT_FOUND","details":{"id":"0","resource":"Product"},"environment":"production","isOperational":true,"level":"error","message":"Unhandled error: Product with id '0' not found","name":"NotFoundError","service":"lc-mall-backend","stack":"NotFoundError: Product with id '0' not found\n    at ErrorFactory.notFound (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\utils\\errors.js:157:12)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:46:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","statusCode":404,"timestamp":"2025-06-21 12:44:45"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:44:45 +0000] \"GET /api/products/0 HTTP/1.1\" 500 51 \"-\" \"-\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:44:45"}
{"code":"NOT_FOUND","details":{"id":"0","resource":"Product"},"environment":"production","isOperational":true,"level":"error","message":"Unhandled error: Product with id '0' not found","name":"NotFoundError","service":"lc-mall-backend","stack":"NotFoundError: Product with id '0' not found\n    at ErrorFactory.notFound (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\utils\\errors.js:157:12)\n    at D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:46:26\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","statusCode":404,"timestamp":"2025-06-21 12:45:04"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:45:04 +0000] \"GET /api/products/0 HTTP/1.1\" 500 51 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:45:04"}
{"environment":"production","level":"debug","message":"API Response 产品详情获取成功","requestId":"mc5ra2prlco0l63v3d","responseSize":814,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-21 12:45:48"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [21/Jun/2025:04:45:48 +0000] \"GET /api/products/1 HTTP/1.1\" 200 1016 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202\"","service":"lc-mall-backend","timestamp":"2025-06-21 12:45:48"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:14"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:14"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:14"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:14"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:14"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:14"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:14"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:14"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:31"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:31"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:31"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:31"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:31"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:31"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:31"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:31"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product data structure initialized and Redis cleared","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 产品样品 50g (ID: 0)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 潜固化剂 WL-1031 (ID: 1)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 潜固化剂 WL-104 (ID: 3)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 潜固化剂 WL-101 (ID: 4)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 生物基树脂LC-450 (ID: 5)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 生物基树脂LC-320 (ID: 6)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 催化剂WL-11 (ID: 7)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 产品样品 50g (ID: 8)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 潜固化剂 WL-102 (ID: 9)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:46"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 生物基树脂LC-170 (ID: 10)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 生物基树脂LC-140 (ID: 11)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-469 (ID: 12)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-480 (ID: 13)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-470 (ID: 14)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 流平剂 LC-310 (ID: 15)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 流平剂 LC-312 (ID: 16)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 分散剂WL-201 (ID: 17)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 润湿分散剂 WL-204A (ID: 18)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 降粘分散剂 WL-210 (ID: 19)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:47"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 分散剂 WL-244 (ID: 20)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:48"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 消泡剂 WL-300 (ID: 21)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:48"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 消泡剂 WL-301 (ID: 22)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:48"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 消泡剂 WL-319 (ID: 31)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:48"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 油性消泡剂 WL-3066 (ID: 32)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:48"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 防变剂 WL-5410 (ID: 33)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:48"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 防沉触变剂 WL-5201 (ID: 34)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:48"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-469 (ID: 35)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:48"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-470 (ID: 36)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:48"}
{"environment":"development","level":"info","message":"Product created and saved to Redis: 附着力促进剂 WL-480 (ID: 37)","service":"lc-mall-backend","timestamp":"2025-06-21 12:46:49"}
{"environment":"development","level":"info","message":"Redis not connected, attempting to connect...","service":"lc-mall-backend","timestamp":"2025-06-21 12:47:56"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:47:56"}
{"environment":"development","level":"info","message":"Loaded 29 imported products from Redis","service":"lc-mall-backend","timestamp":"2025-06-21 12:47:56"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:48:34"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:48:34"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:48:34"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-21 12:48:34"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-21 12:48:34"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-21 12:48:34"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-21 12:48:34"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-21 12:48:34"}
{"environment":"development","level":"info","message":"Loaded 29 imported products from Redis","service":"lc-mall-backend","timestamp":"2025-06-21 12:49:08"}
{"environment":"development","level":"info","message":"Redis not connected, attempting to connect...","service":"lc-mall-backend","timestamp":"2025-06-21 12:50:41"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-21 12:50:41"}
{"environment":"development","level":"info","message":"Loaded 29 imported products from Redis","service":"lc-mall-backend","timestamp":"2025-06-21 12:50:41"}

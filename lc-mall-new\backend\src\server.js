const path = require('path');
const envFile =
  process.env.NODE_ENV === 'production'
    ? path.resolve(__dirname, '../../.env.production')
    : path.resolve(__dirname, '../../.env.development');

require('dotenv').config({ path: envFile });

const app = require('./app');
const logger = require('./utils/logger');
const redisService = require('./services/redisService');
const schedulerService = require('./services/schedulerService');

const PORT = process.env.PORT || 5000;
const HOST = process.env.HOST || '0.0.0.0'; // 监听所有网络接口

// Initialize Redis connection
const startServer = async () => {
  try {
    // 尝试连接Redis，但不阻塞服务器启动
    try {
      await redisService.connect();
      logger.info('Redis connected successfully');
    } catch (redisError) {
      logger.warn('Redis connection failed, continuing without Redis:', redisError.message);
    }

    // Initialize scheduler service
    try {
      schedulerService.init();
      logger.info('Scheduler service initialized');
    } catch (schedulerError) {
      logger.error('Scheduler initialization failed, continuing without scheduler:', schedulerError);
    }
    
    const server = app.listen(PORT, HOST, () => {
      logger.info(`Server running on http://${HOST}:${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
      console.log(`🚀 Server is running on http://${HOST}:${PORT}`);
    });

    // Graceful shutdown
    const gracefulShutdown = async (signal) => {
      logger.info(`${signal} received, shutting down gracefully`);
      
      server.close(async (err) => {
        if (err) {
          logger.error('Error during server shutdown:', err);
        }

        try {
          // Stop scheduler service
          schedulerService.destroy();
          logger.info('Scheduler service stopped');
          
          await redisService.disconnect();
          logger.info('Redis connection closed');
        } catch (error) {
          logger.error('Error during shutdown:', error);
        }
        
        process.exit(err ? 1 : 0);
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    return server;
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = { app, startServer };

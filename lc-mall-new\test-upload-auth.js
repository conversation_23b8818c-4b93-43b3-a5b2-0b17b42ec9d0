// 测试图片上传API认证
const testUploadAuth = async () => {
  try {
    console.log('Testing upload API authentication...');
    
    // 测试前端上传API路由的GET请求
    const response = await fetch('http://gdlongchi.cn/api/admin/upload/image', {
      method: 'GET',
      headers: {
        'x-admin-api-key': 'lc_admin_dev_key_2025'
      }
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const result = await response.text();
    console.log('Response body:', result);
    
    if (response.ok) {
      console.log('✅ Upload API authentication test PASSED');
    } else {
      console.log('❌ Upload API authentication test FAILED');
    }
  } catch (error) {
    console.error('❌ Test error:', error);
  }
};

testUploadAuth();

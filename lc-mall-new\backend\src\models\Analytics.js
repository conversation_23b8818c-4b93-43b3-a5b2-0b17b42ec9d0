/**
 * 用户访问记录模型
 * Analytics Model - 用户访问记录相关数据结构和业务逻辑
 */

const redisService = require('../services/redisService');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class Analytics {
  constructor(data = {}) {
    this.id = data.id || uuidv4();
    this.sessionId = data.sessionId || '';
    this.userId = data.userId || null; // 如果用户已登录
    this.ip = data.ip || '';
    this.userAgent = data.userAgent || '';
    this.url = data.url || '';
    this.pathname = data.pathname || '';
    this.referer = data.referer || '';
    this.timestamp = data.timestamp || new Date().toISOString();
    this.duration = data.duration || 0; // 页面停留时间（秒）
    this.deviceType = data.deviceType || 'unknown'; // desktop, mobile, tablet
    this.browserName = data.browserName || 'unknown';
    this.browserVersion = data.browserVersion || 'unknown';
    this.osName = data.osName || 'unknown';
    this.osVersion = data.osVersion || 'unknown';
    this.screenResolution = data.screenResolution || '';
    this.language = data.language || '';
    this.timezone = data.timezone || '';
    this.country = data.country || '';
    this.city = data.city || '';
    this.isBot = data.isBot || false;
    this.event = data.event || 'pageview'; // pageview, click, scroll, etc.
    this.eventData = data.eventData || {}; // 额外的事件数据
  }

  /**
   * 保存访问记录
   */
  async save() {
    try {
      const key = `analytics:${this.id}`;
      const data = this.toJSON();
      
      // 保存到Redis
      await redisService.setex(key, 30 * 24 * 60 * 60, JSON.stringify(data)); // 保存30天
      
      // 添加到日期索引
      const dateKey = `analytics:date:${this.timestamp.split('T')[0]}`;
      await redisService.sadd(dateKey, this.id);
      await redisService.expire(dateKey, 30 * 24 * 60 * 60); // 30天过期
      
      // 添加到IP索引（用于统计UV）
      const ipKey = `analytics:ip:${this.ip}:${this.timestamp.split('T')[0]}`;
      await redisService.setex(ipKey, 24 * 60 * 60, '1'); // 1天过期
      
      // 添加到会话索引
      if (this.sessionId) {
        const sessionKey = `analytics:session:${this.sessionId}`;
        await redisService.sadd(sessionKey, this.id);
        await redisService.expire(sessionKey, 24 * 60 * 60); // 1天过期
      }
      
      // 添加到URL索引
      const urlKey = `analytics:url:${this.pathname}:${this.timestamp.split('T')[0]}`;
      await redisService.sadd(urlKey, this.id);
      await redisService.expire(urlKey, 30 * 24 * 60 * 60); // 30天过期
      
      logger.info(`Analytics record saved: ${this.id}`);
      return this;
    } catch (error) {
      logger.error('Error saving analytics record:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取访问记录
   */  
  static async findById(id) {
    try {
      const key = `analytics:${id}`;
      const data = await redisService.get(key);
      
      if (!data) {
        return null;
      }
      
      // No need to parse as redisService.get already returns parsed data
      return new Analytics(data);
    } catch (error) {
      logger.error('Error finding analytics record by ID:', error);
      throw error;
    }
  }

  /**
   * 获取指定日期范围的访问记录
   */
  static async findByDateRange(startDate, endDate, options = {}) {
    try {
      const {
        limit = 100,
        offset = 0,
        url = null,
        ip = null,
        sessionId = null
      } = options;

      const records = [];
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      // 遍历日期范围
      for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
        const dateStr = date.toISOString().split('T')[0];
        let dateKey = `analytics:date:${dateStr}`;
        
        // 如果指定了URL过滤
        if (url) {
          dateKey = `analytics:url:${url}:${dateStr}`;
        }
        
        const ids = await redisService.smembers(dateKey);
        
        for (const id of ids) {
          if (records.length >= limit + offset) break;
          
          const record = await Analytics.findById(id);
          if (record) {
            // 应用过滤条件
            if (ip && record.ip !== ip) continue;
            if (sessionId && record.sessionId !== sessionId) continue;
            
            records.push(record);
          }
        }
        
        if (records.length >= limit + offset) break;
      }
      
      // 应用分页
      return records.slice(offset, offset + limit);
    } catch (error) {
      logger.error('Error finding analytics records by date range:', error);
      throw error;
    }
  }

  /**
   * 获取访问统计数据
   */
  static async getStats(startDate, endDate) {
    try {
      const stats = {
        totalViews: 0,
        uniqueVisitors: 0,
        uniqueSessions: 0,
        avgDuration: 0,
        popularPages: {},
        deviceTypes: {},
        browsers: {},
        countries: {},
        hourlyViews: {},
        dailyViews: {}
      };

      const start = new Date(startDate);
      const end = new Date(endDate);
      const uniqueIPs = new Set();
      const uniqueSessions = new Set();
      let totalDuration = 0;
      let durationCount = 0;

      // 遍历日期范围
      for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
        const dateStr = date.toISOString().split('T')[0];
        const dateKey = `analytics:date:${dateStr}`;
        
        const ids = await redisService.smembers(dateKey);
        stats.dailyViews[dateStr] = ids.length;
        stats.totalViews += ids.length;
        
        for (const id of ids) {
          const record = await Analytics.findById(id);
          if (record) {
            // 统计UV
            uniqueIPs.add(record.ip);
            
            // 统计会话
            if (record.sessionId) {
              uniqueSessions.add(record.sessionId);
            }
            
            // 统计停留时间
            if (record.duration > 0) {
              totalDuration += record.duration;
              durationCount++;
            }
            
            // 统计热门页面
            stats.popularPages[record.pathname] = (stats.popularPages[record.pathname] || 0) + 1;
            
            // 统计设备类型
            stats.deviceTypes[record.deviceType] = (stats.deviceTypes[record.deviceType] || 0) + 1;
            
            // 统计浏览器
            stats.browsers[record.browserName] = (stats.browsers[record.browserName] || 0) + 1;
            
            // 统计国家
            if (record.country) {
              stats.countries[record.country] = (stats.countries[record.country] || 0) + 1;
            }
            
            // 统计每小时访问量
            const hour = new Date(record.timestamp).getHours();
            stats.hourlyViews[hour] = (stats.hourlyViews[hour] || 0) + 1;
          }
        }
      }

      stats.uniqueVisitors = uniqueIPs.size;
      stats.uniqueSessions = uniqueSessions.size;
      stats.avgDuration = durationCount > 0 ? Math.round(totalDuration / durationCount) : 0;

      return stats;
    } catch (error) {
      logger.error('Error getting analytics stats:', error);
      throw error;
    }
  }

  /**
   * 获取实时统计数据（最近24小时）
   */
  static async getRealTimeStats() {
    try {
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      return await Analytics.getStats(yesterday.toISOString(), now.toISOString());
    } catch (error) {
      logger.error('Error getting real-time stats:', error);
      throw error;
    }
  }

  /**
   * 删除过期的访问记录
   */
  static async cleanupExpired(daysToKeep = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      let deletedCount = 0;
      
      // 获取需要删除的日期
      for (let i = daysToKeep; i < daysToKeep + 7; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        const dateKey = `analytics:date:${dateStr}`;
        
        const ids = await redisService.smembers(dateKey);
        
        for (const id of ids) {
          const key = `analytics:${id}`;
          const deleted = await redisService.del(key);
          if (deleted) deletedCount++;
        }
        
        // 删除日期索引
        await redisService.del(dateKey);
      }
      
      logger.info(`Cleaned up ${deletedCount} expired analytics records`);
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up expired analytics records:', error);
      throw error;
    }
  }

  /**
   * 转换为JSON对象
   */
  toJSON() {
    return {
      id: this.id,
      sessionId: this.sessionId,
      userId: this.userId,
      ip: this.ip,
      userAgent: this.userAgent,
      url: this.url,
      pathname: this.pathname,
      referer: this.referer,
      timestamp: this.timestamp,
      duration: this.duration,
      deviceType: this.deviceType,
      browserName: this.browserName,
      browserVersion: this.browserVersion,
      osName: this.osName,
      osVersion: this.osVersion,
      screenResolution: this.screenResolution,
      language: this.language,
      timezone: this.timezone,
      country: this.country,
      city: this.city,
      isBot: this.isBot,
      event: this.event,
      eventData: this.eventData
    };
  }
}

module.exports = Analytics;

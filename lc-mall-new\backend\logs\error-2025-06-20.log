{"environment":"development","level":"error","message":"Error during shutdown: The client is closed","service":"lc-mall-backend","stack":"Error: The client is closed\n    at RedisSocket.disconnect (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:63:19)\n    at Commander.disconnect (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\@redis\\client\\dist\\lib\\client\\index.js:353:64)\n    at RedisService.disconnect (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\redisService.js:56:25)\n    at Server.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js:45:30)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2416:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","timestamp":"2025-06-20 21:05:43"}
{"environment":"development","level":"error","message":"Error during shutdown: The client is closed","service":"lc-mall-backend","stack":"Error: The client is closed\n    at RedisSocket.disconnect (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:63:19)\n    at Commander.disconnect (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\@redis\\client\\dist\\lib\\client\\index.js:353:64)\n    at RedisService.disconnect (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\redisService.js:56:25)\n    at Server.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js:45:30)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2416:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","timestamp":"2025-06-20 21:07:46"}
{"environment":"development","level":"error","message":"Error during shutdown: The client is closed","service":"lc-mall-backend","stack":"Error: The client is closed\n    at RedisSocket.disconnect (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:63:19)\n    at Commander.disconnect (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\@redis\\client\\dist\\lib\\client\\index.js:353:64)\n    at RedisService.disconnect (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\redisService.js:56:25)\n    at Server.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js:45:30)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2416:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","timestamp":"2025-06-20 21:31:52"}
{"code":"ERR_SERVER_NOT_RUNNING","environment":"development","level":"error","message":"Error during server shutdown: Server is not running.","service":"lc-mall-backend","stack":"Error [ERR_SERVER_NOT_RUNNING]: Server is not running.\n    at Server.close (node:net:2356:12)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2416:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","timestamp":"2025-06-20 21:51:04"}
{"environment":"development","level":"error","message":"Error finding analytics record by ID: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error getting analytics stats: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error getting analytics stats: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error finding analytics record by ID: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error finding analytics records by date range: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error getting analytics records: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error finding analytics record by ID: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error getting analytics stats: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error getting analytics stats: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error finding analytics record by ID: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error finding analytics records by date range: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error getting analytics records: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:37"}

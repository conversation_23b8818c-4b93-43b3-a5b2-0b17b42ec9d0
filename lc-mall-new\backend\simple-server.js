/**
 * 简化的服务器启动脚本
 * 绕过复杂的初始化流程，直接启动基本服务
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const logger = require('./src/utils/logger');

// 创建Express应用
const app = express();

// 基本中间件
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 限制每个IP 15分钟内最多1000个请求
  message: '请求过于频繁，请稍后再试'
});
app.use(limiter);

// 解析JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 基本路由
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 产品路由
const productRoutes = require('./src/routes/products');
app.use('/api/products', productRoutes);

// 分析路由
const analyticsRoutes = require('./src/routes/analytics');
app.use('/api/analytics', analyticsRoutes);

// 视频路由
const videoRoutes = require('./src/routes/videos');
app.use('/api/videos', videoRoutes);

// 错误处理
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', { error: err.message, stack: err.stack });
  res.status(500).json({
    success: false,
    message: err.message || '服务器内部错误',
    timestamp: new Date().toISOString()
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
const PORT = process.env.PORT || 5000;
const HOST = process.env.HOST || 'localhost';

const server = app.listen(PORT, HOST, () => {
  console.log(`🚀 Simple server running on http://${HOST}:${PORT}`);
  logger.info(`Simple server running on http://${HOST}:${PORT}`);
});

// 优雅关闭
const gracefulShutdown = (signal) => {
  console.log(`\n收到 ${signal} 信号，正在关闭服务器...`);
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

module.exports = app;

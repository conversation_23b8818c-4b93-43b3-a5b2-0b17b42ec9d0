/**
 * 测试产品数据格式
 */

const http = require('http');

function testProductData() {
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/products/0', // 获取ID为0的产品详细信息
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('🔍 产品详情测试结果:');
        console.log('状态码:', res.statusCode);
        console.log('响应成功:', response.success);
        
        if (response.data) {
          const product = response.data;
          console.log('\n📦 产品基本信息:');
          console.log('ID:', product.id);
          console.log('名称:', product.name);
          console.log('分类:', product.category);
          console.log('价格:', product.price, '(类型:', typeof product.price, ')');
          console.log('单位:', product.unit);
          console.log('库存数量:', product.stockQuantity, '(类型:', typeof product.stockQuantity, ')');
          console.log('最小订购量:', product.minOrderQuantity, '(类型:', typeof product.minOrderQuantity, ')');
          
          console.log('\n🖼️ 图片信息:');
          console.log('图片数组:', product.images, '(类型:', typeof product.images, ')');
          console.log('图片数量:', Array.isArray(product.images) ? product.images.length : 'N/A');
          
          console.log('\n🏷️ 标签信息:');
          console.log('是否推荐:', product.isFeatured, '(类型:', typeof product.isFeatured, ')');
          console.log('是否热门:', product.isHot, '(类型:', typeof product.isHot, ')');
          console.log('是否新品:', product.isNew, '(类型:', typeof product.isNew, ')');
          
          console.log('\n📋 应用场景:');
          console.log('应用列表:', product.applications, '(类型:', typeof product.applications, ')');
          console.log('应用数量:', Array.isArray(product.applications) ? product.applications.length : 'N/A');
          
          console.log('\n🔧 技术参数:');
          console.log('技术数据:', typeof product.technicalData, product.technicalData);
          
          console.log('\n📝 产品特性:');
          console.log('特性列表:', product.features, '(类型:', typeof product.features, ')');
          console.log('特性数量:', Array.isArray(product.features) ? product.features.length : 'N/A');
        }
        
      } catch (error) {
        console.error('解析响应失败:', error);
        console.log('原始响应:', data.substring(0, 1000));
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求失败:', error);
  });

  req.end();
}

console.log('🚀 开始测试产品数据格式...');
testProductData();

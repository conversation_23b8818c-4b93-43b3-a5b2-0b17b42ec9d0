#!/bin/bash
# 检查和修复后端图片上传权限

echo "=========================================="
echo "  后端图片上传权限检查和修复            "
echo "=========================================="

# 进入后端目录
cd backend || { echo "❌ 错误: 无法进入backend目录"; exit 1; }

# 检查并创建必要的目录
echo "检查图片上传目录..."

directories=(
    "public"
    "public/images"
    "public/images/products"
    "public/images/news"
    "public/images/uploads"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        echo "创建目录: $dir"
        mkdir -p "$dir"
    else
        echo "✅ 目录存在: $dir"
    fi
    
    # 设置权限
    chmod 755 "$dir"
    echo "✅ 权限设置: $dir (755)"
done

# 检查目录所有者
echo ""
echo "检查目录所有者..."
ls -la public/
ls -la public/images/

# 测试写入权限
echo ""
echo "测试写入权限..."
test_file="public/images/test_write.txt"
if echo "test" > "$test_file" 2>/dev/null; then
    echo "✅ 写入权限正常"
    rm -f "$test_file"
else
    echo "❌ 写入权限失败"
    echo "尝试修复权限..."
    sudo chown -R $(whoami):$(whoami) public/
    sudo chmod -R 755 public/
fi

# 检查后端环境变量
echo ""
echo "检查后端API密钥配置..."
if [ -f "../.env.production" ]; then
    echo "生产环境API密钥:"
    grep "ADMIN_API_KEY=" ../.env.production
else
    echo "⚠️  生产环境配置文件不存在"
fi

if [ -f "../.env.development" ]; then
    echo "开发环境API密钥:"
    grep "ADMIN_API_KEY=" ../.env.development
else
    echo "⚠️  开发环境配置文件不存在"
fi

# 检查后端服务状态
echo ""
echo "检查后端服务状态..."
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ 后端服务正常响应"
    
    # 测试上传API
    echo "测试后端上传API..."
    upload_response=$(curl -s -X GET "http://localhost:5000/api/admin/upload/image" \
                     -H "x-admin-api-key: lc_admin_2025_secure_key_prod")
    
    if echo "$upload_response" | grep -q "success\|working\|Upload"; then
        echo "✅ 后端上传API响应正常"
    else
        echo "⚠️  后端上传API响应异常"
        echo "响应: $upload_response"
    fi
else
    echo "❌ 后端服务无响应"
    echo "请检查后端服务是否正在运行"
fi

echo ""
echo "=========================================="
echo "✅ 后端权限检查完成"
echo "=========================================="
echo ""
echo "如果上传仍有问题，请尝试:"
echo "1. 重启后端服务"
echo "2. 检查磁盘空间: df -h"
echo "3. 检查上传文件大小限制"
echo "4. 查看后端日志: tail -f logs/backend.log"

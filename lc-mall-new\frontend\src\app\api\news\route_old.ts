// frontend/src/app/api/news/route.ts
import { NextResponse } from 'next/server';
import { NewsArticle, PaginatedNewsResponse, newsCategories } from '@/shared/types/News';
import { mockNewsDb } from '@/shared/mockdata/news'; // Import shared mock data
import { v4 as uuidv4 } from 'uuid';

// --- Redis Client Setup (Placeholder - implement your actual Redis client) ---
// import { createClient } from 'redis'; 
// const redisClient = createClient({ url: process.env.REDIS_URL || 'redis://localhost:6379' });
// redisClient.on('error', (err) => console.error('Redis Client Error', err));
// async function connectRedis() {
//   if (!redisClient.isOpen) {
//     await redisClient.connect();
//   }
// }
// connectRedis(); // Connect on module load

// --- Mock Data (Remove this when Redis is implemented) ---
let mockNewsDb: NewsArticle[] = Array.from({ length: 25 }, (_, i) => ({
  id: uuidv4(),
  slug: `sample-news-${i + 1}`,
  title: `示例新闻标题 ${i + 1}`,
  summary: `这是新闻 ${i + 1} 的简短摘要...`,
  content: `这是新闻 #${i + 1} 的**详细内容**。它支持 Markdown 格式。\n\n## 子标题\n\n- 列表项1\n- 列表项2`,
  coverImage: `/images/stock/news-placeholder-${(i % 5) + 1}.jpg`, // Placeholder images
  author: '龙驰AI助手',
  publishedAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(), // Decreasing dates
  createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
  updatedAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
  category: newsCategories[i % newsCategories.length],
  status: 'published',
  views: Math.floor(Math.random() * 1000),
  isFeatured: i < 3,
  aiGenerated: true,
}));
// --- End Mock Data ---


export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const categoryId = searchParams.get('category'); // e.g., 'industry-dynamics'
  // const sortBy = searchParams.get('sortBy') || 'publishedAt'; // Example: 'publishedAt', 'views'
  // const sortOrder = searchParams.get('sortOrder') || 'desc'; // 'asc' or 'desc'

  try {
    // TODO: Replace with actual Redis data fetching and filtering logic
    // await connectRedis(); 

    let filteredArticles = mockNewsDb.filter(article => article.status === 'published');

    if (categoryId && categoryId !== 'all') {
      filteredArticles = filteredArticles.filter(article => article.category.id === categoryId);
    }
    
    // Sort by publishedAt descending by default
    filteredArticles.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());

    const totalArticles = filteredArticles.length;
    const totalPages = Math.ceil(totalArticles / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const articlesForPage = filteredArticles.slice(startIndex, endIndex);

    const response: PaginatedNewsResponse = {
      articles: articlesForPage,
      currentPage: page,
      totalPages: totalPages,
      totalArticles: totalArticles,
    };
    return NextResponse.json(response);

  } catch (error) {
    console.error('API Error fetching news:', error);
    return NextResponse.json({ message: 'Error fetching news', error: (error as Error).message }, { status: 500 });
  }
}

export async function POST(request: Request) {
  // Admin functionality - Placeholder
  // TODO: Implement authentication and authorization for this endpoint
  try {
    const newArticleData = await request.json() as Omit<NewsArticle, 'id' | 'createdAt' | 'updatedAt' | 'slug'>;
    
    // TODO: Validate newArticleData
    
    const newArticle: NewsArticle = {
      ...newArticleData,
      id: uuidv4(),
      slug: newArticleData.title.toLowerCase().replace(/\s+/g, '-').slice(0, 50), // Simple slug generation
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // Ensure category is an object if your type expects it
      category: typeof newArticleData.category === 'string' 
                ? newsCategories.find(c => c.id === newArticleData.category) || newsCategories[0] 
                : newArticleData.category,
    };

    // TODO: Save to Redis
    // await connectRedis();
    // await redisClient.hSet(`news:${newArticle.id}`, newArticle as any);
    // await redisClient.zAdd('news:all:published_at', { score: new Date(newArticle.publishedAt).getTime(), value: newArticle.id });
    // if (newArticle.category) {
    //   await redisClient.zAdd(`news:cat:${newArticle.category.id}:published_at`, { score: new Date(newArticle.publishedAt).getTime(), value: newArticle.id });
    //   await redisClient.sAdd('news:categories', newArticle.category.id);
    // }
    
    mockNewsDb.unshift(newArticle); // Add to mock DB for now

    return NextResponse.json({ message: 'News article created successfully', article: newArticle }, { status: 201 });

  } catch (error) {
    console.error('API Error creating news:', error);
    return NextResponse.json({ message: 'Error creating news article', error: (error as Error).message }, { status: 500 });
  }
}

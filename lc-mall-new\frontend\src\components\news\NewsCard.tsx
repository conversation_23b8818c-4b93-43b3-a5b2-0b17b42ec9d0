// frontend/src/components/news/NewsCard.tsx
import React from 'react';
import Link from 'next/link';
import { NewsArticle } from '@/shared/types/News';
import { format } from 'date-fns';

interface NewsCardProps {
  article: NewsArticle;
}

export default function NewsCard({ article }: NewsCardProps) {
  const publishDate = new Date(article.publishedAt);
  const now = new Date();
  const daysDiff = Math.floor((now.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24));
    return (
    <Link href={`/news/${article.slug || article.id}`} legacyBehavior>
      <a className="block px-6 py-4 hover:bg-blue-50 transition-all duration-200 group relative overflow-hidden">
        {/* Hover indicator */}
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300"></div>
        
        <article className="flex items-center justify-between gap-4 md:gap-6">
          {/* Left section: Title and Category */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-1">
              <h2 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-300 truncate">
                {article.title}
              </h2>
              {article.isFeatured && (
                <span className="inline-flex items-center px-2 py-1 bg-red-100 text-red-700 text-xs font-medium rounded shrink-0">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                  </svg>
                  置顶
                </span>
              )}
              {daysDiff === 0 && (
                <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded shrink-0">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  今日
                </span>
              )}
            </div>
            
            {/* Summary - hidden on mobile, visible on larger screens */}
            {article.summary && (
              <p className="hidden md:block text-gray-600 text-sm leading-relaxed line-clamp-1 mt-1">
                {article.summary}
              </p>
            )}
            
            {/* Metadata row */}
            <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
              {article.category && (
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded">
                  {article.category.name || article.category.id}
                </span>
              )}
              <span className="flex items-center">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
                {article.author || '龙驰新材料'}
              </span>
              {article.views && (
                <span className="hidden sm:flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                  </svg>
                  {article.views.toLocaleString()}
                </span>
              )}
            </div>
          </div>
          
          {/* Right section: Date */}
          <div className="text-right shrink-0">
            <time dateTime={article.publishedAt} className="text-sm font-medium text-gray-600 block">
              {format(publishDate, 'yyyy-MM-dd')}
            </time>
            <span className="text-xs text-gray-400 mt-1 block">
              {format(publishDate, 'HH:mm')}
            </span>
          </div>
        </article>
      </a>
    </Link>
  );
}

/**
 * Analytics Track API Route
 * 前端API路由 - 转发访问追踪请求到后端
 */

import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, '') || 'http://localhost:5000/api';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 获取客户端IP地址
    const clientIP = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     '127.0.0.1';
                     
    // 添加IP地址到请求数据
    const analyticsData = {
      ...body,
      ip: clientIP,
      userAgent: request.headers.get('user-agent') || ''
    };

    const response = await fetch(`${API_BASE_URL}/analytics/track`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(analyticsData)
    });

    if (!response.ok) {
      throw new Error(`<PERSON><PERSON> responded with ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('Analytics track API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Analytics tracking failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * 产品服务 - 导入脚本专用版本
 * 提供与导入脚本兼容的接口，支持Redis持久化存储
 */

const logger = require('../utils/logger');
const redisService = require('./redisService');

class ProductServiceForImport {
  constructor() {
    this.products = new Map(); // Map作为二级缓存，提高读取性能
    this.categories = new Set(); // 存储分类缓存
    this.nextId = 1;
    this.cacheLoaded = false; // 标记缓存是否已加载

    // Redis键名前缀，避免与其他数据冲突
    this.REDIS_PREFIX = {
      PRODUCT: 'imported:product:',
      PRODUCT_LIST: 'imported:products:all',
      CATEGORIES: 'imported:categories:all',
      NEXT_ID: 'imported:next_id',
      STATS: 'imported:stats'
    };
  }

  /**
   * 从Redis加载缓存数据
   */
  async loadCacheFromRedis() {
    if (this.cacheLoaded) return;

    try {
      // 加载所有产品ID
      const productIds = await redisService.smembers(this.REDIS_PREFIX.PRODUCT_LIST);

      // 加载每个产品的详细信息
      for (const productId of productIds) {
        const productData = await redisService.hgetAll(`${this.REDIS_PREFIX.PRODUCT}${productId}`);
        if (productData && Object.keys(productData).length > 0) {
          // 解析JSON字段
          const product = {
            ...productData,
            price: parseFloat(productData.price) || 0,
            stockQuantity: parseInt(productData.stockQuantity) || 0,
            minOrderQuantity: parseInt(productData.minOrderQuantity) || 1,
            isFeatured: productData.isFeatured === 'true',
            isHot: productData.isHot === 'true',
            isNew: productData.isNew === 'true',
            images: productData.images ? JSON.parse(productData.images) : [],
            technicalData: productData.technicalData ? JSON.parse(productData.technicalData) : {},
            applications: productData.applications ? JSON.parse(productData.applications) : [],
            features: productData.features ? JSON.parse(productData.features) : [],
            certifications: productData.certifications ? JSON.parse(productData.certifications) : []
          };

          this.products.set(productId, product);

          // 添加分类到缓存
          if (product.category) {
            this.categories.add(product.category);
          }
        }
      }

      // 加载下一个ID
      const nextId = await redisService.get(this.REDIS_PREFIX.NEXT_ID);
      if (nextId) {
        this.nextId = parseInt(nextId);
      }

      this.cacheLoaded = true;
      logger.info(`Loaded ${this.products.size} products from Redis cache`);
    } catch (error) {
      logger.warn('Failed to load cache from Redis:', error.message);
      this.cacheLoaded = false;
    }
  }

  /**
   * 初始化产品数据结构
   */
  async initializeProductData() {
    try {
      // 清空Redis中的导入数据
      const productIds = await redisService.smembers(this.REDIS_PREFIX.PRODUCT_LIST);
      for (const productId of productIds) {
        await redisService.del(`${this.REDIS_PREFIX.PRODUCT}${productId}`);
      }
      await redisService.del(this.REDIS_PREFIX.PRODUCT_LIST);
      await redisService.del(this.REDIS_PREFIX.CATEGORIES);
      await redisService.del(this.REDIS_PREFIX.NEXT_ID);
      await redisService.del(this.REDIS_PREFIX.STATS);

      // 清空内存缓存
      this.products.clear();
      this.categories.clear();
      this.nextId = 1;
      this.cacheLoaded = true;

      // 设置初始的下一个ID
      await redisService.set(this.REDIS_PREFIX.NEXT_ID, '1');

      logger.info('Product data structure initialized and Redis cleared');
      return true;
    } catch (error) {
      logger.error('Failed to initialize product data:', error);
      throw error;
    }
  }

  /**
   * 创建产品
   * @param {Object} productData - 产品数据
   * @returns {Object} 创建的产品对象
   */
  async createProduct(productData) {
    try {
      // 确保缓存已加载
      await this.loadCacheFromRedis();

      // 使用传入的ID，如果没有则使用nextId
      const productId = productData.id !== undefined ? productData.id.toString() : this.nextId.toString();

      const product = {
        ...productData,
        id: productId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: productData.status || 'active'
      };

      // 保存到Redis
      const productKey = `${this.REDIS_PREFIX.PRODUCT}${product.id}`;
      const productForRedis = {
        ...product,
        // 将复杂对象序列化为JSON字符串
        images: JSON.stringify(product.images || []),
        technicalData: JSON.stringify(product.technicalData || {}),
        applications: JSON.stringify(product.applications || []),
        features: JSON.stringify(product.features || []),
        certifications: JSON.stringify(product.certifications || []),
        // 确保布尔值转换为字符串
        isFeatured: String(product.isFeatured || false),
        isHot: String(product.isHot || false),
        isNew: String(product.isNew || false)
      };

      await redisService.hsetAll(productKey, productForRedis);

      // 添加产品ID到产品列表
      await redisService.sadd(this.REDIS_PREFIX.PRODUCT_LIST, product.id);

      // 更新下一个ID（只有在使用自动生成ID时才更新）
      if (productData.id === undefined) {
        this.nextId++;
        await redisService.set(this.REDIS_PREFIX.NEXT_ID, this.nextId.toString());
      } else {
        // 如果使用了指定的ID，确保nextId大于当前ID
        const currentId = parseInt(product.id);
        if (!isNaN(currentId) && currentId >= this.nextId) {
          this.nextId = currentId + 1;
          await redisService.set(this.REDIS_PREFIX.NEXT_ID, this.nextId.toString());
        }
      }

      // 更新内存缓存
      this.products.set(product.id, product);

      // 添加分类到缓存和Redis
      if (product.category) {
        this.categories.add(product.category);
        await redisService.sadd(this.REDIS_PREFIX.CATEGORIES, product.category);
      }

      logger.info(`Product created and saved to Redis: ${product.name} (ID: ${product.id})`);
      return product;
    } catch (error) {
      logger.error('Failed to create product:', error);
      return null;
    }
  }

  /**
   * 获取产品列表
   * @param {Object} options - 查询选项
   * @returns {Object} 产品列表和分页信息
   */
  async getProducts(options = {}) {
    try {
      // 确保缓存已加载
      await this.loadCacheFromRedis();

      const { page = 1, limit = 20, category, featured, inStock } = options;
      let allProducts = Array.from(this.products.values());

      // 应用过滤器
      if (category) {
        allProducts = allProducts.filter(p => p.category === category);
      }
      if (featured !== undefined) {
        allProducts = allProducts.filter(p => p.isFeatured === featured);
      }
      if (inStock !== undefined) {
        allProducts = allProducts.filter(p => (p.stockQuantity > 0) === inStock);
      }

      const total = allProducts.length;
      const totalPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;
      const products = allProducts.slice(offset, offset + limit);

      return {
        products,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      logger.error('Failed to get products:', error);
      return {
        products: [],
        pagination: { total: 0 }
      };
    }
  }

  /**
   * 获取产品统计信息
   * @returns {Object} 统计信息
   */
  async getProductStats() {
    try {
      // 确保缓存已加载
      await this.loadCacheFromRedis();

      const allProducts = Array.from(this.products.values());
      const totalValue = allProducts.reduce((sum, p) => {
        const price = parseFloat(p.price) || 0;
        const stock = parseInt(p.stockQuantity) || 0;
        return sum + (price * stock);
      }, 0);

      const stats = {
        total: allProducts.length,
        active: allProducts.filter(p => p.status === 'active').length,
        inactive: allProducts.filter(p => p.status !== 'active').length,
        featured: allProducts.filter(p => p.isFeatured).length,
        hot: allProducts.filter(p => p.isHot).length,
        new: allProducts.filter(p => p.isNew).length,
        inStock: allProducts.filter(p => (p.stockQuantity || 0) > 0).length,
        outOfStock: allProducts.filter(p => (p.stockQuantity || 0) === 0).length,
        categoriesCount: this.categories.size,
        totalValue: totalValue
      };

      // 缓存统计信息到Redis
      await redisService.hsetAll(this.REDIS_PREFIX.STATS, {
        ...stats,
        lastUpdated: new Date().toISOString()
      });

      return stats;
    } catch (error) {
      logger.error('Failed to get product stats:', error);
      return {
        total: 0,
        totalValue: 0
      };
    }
  }

  /**
   * 获取产品分类列表
   * @returns {Array} 分类名称数组
   */
  async getCategories() {
    try {
      // 确保缓存已加载
      await this.loadCacheFromRedis();

      return Array.from(this.categories);
    } catch (error) {
      logger.error('Failed to get categories:', error);
      return [];
    }
  }

  /**
   * 根据ID获取产品
   * @param {string} productId - 产品ID
   * @returns {Object|null} 产品对象
   */
  async getProductById(productId) {
    try {
      // 确保缓存已加载
      await this.loadCacheFromRedis();

      // 先从缓存获取
      let product = this.products.get(productId);

      // 如果缓存中没有，尝试从Redis直接获取
      if (!product) {
        const productData = await redisService.hgetAll(`${this.REDIS_PREFIX.PRODUCT}${productId}`);
        if (productData && Object.keys(productData).length > 0) {
          product = {
            ...productData,
            price: parseFloat(productData.price) || 0,
            stockQuantity: parseInt(productData.stockQuantity) || 0,
            minOrderQuantity: parseInt(productData.minOrderQuantity) || 1,
            isFeatured: productData.isFeatured === 'true',
            isHot: productData.isHot === 'true',
            isNew: productData.isNew === 'true',
            images: productData.images ? JSON.parse(productData.images) : [],
            technicalData: productData.technicalData ? JSON.parse(productData.technicalData) : {},
            applications: productData.applications ? JSON.parse(productData.applications) : [],
            features: productData.features ? JSON.parse(productData.features) : [],
            certifications: productData.certifications ? JSON.parse(productData.certifications) : []
          };

          // 更新缓存
          this.products.set(productId, product);
        }
      }

      return product || null;
    } catch (error) {
      logger.error('Failed to get product by ID:', error);
      return null;
    }
  }

  /**
   * 更新产品
   * @param {string} productId - 产品ID
   * @param {Object} updateData - 更新数据
   * @returns {Object|null} 更新后的产品对象
   */
  async updateProduct(productId, updateData) {
    try {
      const existingProduct = this.products.get(productId);
      if (!existingProduct) {
        return null;
      }

      const updatedProduct = {
        ...existingProduct,
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      this.products.set(productId, updatedProduct);

      // 更新分类
      if (updatedProduct.category) {
        this.categories.add(updatedProduct.category);
      }

      logger.info(`Product updated: ${updatedProduct.name} (ID: ${productId})`);
      return updatedProduct;
    } catch (error) {
      logger.error('Failed to update product:', error);
      return null;
    }
  }

  /**
   * 删除产品
   * @param {string} productId - 产品ID
   * @returns {boolean} 删除是否成功
   */
  async deleteProduct(productId) {
    try {
      const deleted = this.products.delete(productId);
      if (deleted) {
        logger.info(`Product deleted: ID ${productId}`);
      }
      return deleted;
    } catch (error) {
      logger.error('Failed to delete product:', error);
      return false;
    }
  }

  /**
   * 清空所有产品数据
   */
  async clearAllProducts() {
    try {
      this.products.clear();
      this.categories.clear();
      this.nextId = 1;
      logger.info('All products cleared');
      return true;
    } catch (error) {
      logger.error('Failed to clear products:', error);
      return false;
    }
  }

  /**
   * 获取产品数量
   * @returns {number} 产品总数
   */
  getProductCount() {
    return this.products.size;
  }

  /**
   * 获取分类数量
   * @returns {number} 分类总数
   */
  getCategoryCount() {
    return this.categories.size;
  }
}

// 导出单例实例
module.exports = new ProductServiceForImport();

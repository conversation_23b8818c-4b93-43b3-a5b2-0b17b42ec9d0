/**
 * 产品服务 - 导入脚本专用版本
 * 提供与导入脚本兼容的接口
 */

const logger = require('../utils/logger');
const redisService = require('./redisService');

class ProductServiceForImport {
  constructor() {
    this.products = new Map(); // 使用Map存储产品，模拟Redis
    this.categories = new Set(); // 存储分类
    this.nextId = 1;
  }

  /**
   * 初始化产品数据结构
   */
  async initializeProductData() {
    try {
      // 清空现有数据
      this.products.clear();
      this.categories.clear();
      this.nextId = 1;
      
      logger.info('Product data structure initialized');
      return true;
    } catch (error) {
      logger.error('Failed to initialize product data:', error);
      throw error;
    }
  }

  /**
   * 创建产品
   * @param {Object} productData - 产品数据
   * @returns {Object} 创建的产品对象
   */
  async createProduct(productData) {
    try {
      const product = {
        id: this.nextId.toString(),
        ...productData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: productData.status || 'active'
      };

      this.products.set(product.id, product);
      this.nextId++;

      // 添加分类
      if (product.category) {
        this.categories.add(product.category);
      }

      logger.info(`Product created: ${product.name} (ID: ${product.id})`);
      return product;
    } catch (error) {
      logger.error('Failed to create product:', error);
      return null;
    }
  }

  /**
   * 获取产品列表
   * @param {Object} options - 查询选项
   * @returns {Object} 产品列表和分页信息
   */
  async getProducts(options = {}) {
    try {
      const { page = 1, limit = 20 } = options;
      const allProducts = Array.from(this.products.values());
      
      const total = allProducts.length;
      const totalPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;
      const products = allProducts.slice(offset, offset + limit);

      return {
        products,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      logger.error('Failed to get products:', error);
      return {
        products: [],
        pagination: { total: 0 }
      };
    }
  }

  /**
   * 获取产品统计信息
   * @returns {Object} 统计信息
   */
  async getProductStats() {
    try {
      const allProducts = Array.from(this.products.values());
      const totalValue = allProducts.reduce((sum, p) => {
        const price = parseFloat(p.price) || 0;
        const stock = parseInt(p.stockQuantity) || 0;
        return sum + (price * stock);
      }, 0);

      return {
        total: allProducts.length,
        active: allProducts.filter(p => p.status === 'active').length,
        inactive: allProducts.filter(p => p.status !== 'active').length,
        featured: allProducts.filter(p => p.isFeatured).length,
        hot: allProducts.filter(p => p.isHot).length,
        new: allProducts.filter(p => p.isNew).length,
        inStock: allProducts.filter(p => (p.stockQuantity || 0) > 0).length,
        outOfStock: allProducts.filter(p => (p.stockQuantity || 0) === 0).length,
        categoriesCount: this.categories.size,
        totalValue: totalValue
      };
    } catch (error) {
      logger.error('Failed to get product stats:', error);
      return {
        total: 0,
        totalValue: 0
      };
    }
  }

  /**
   * 获取产品分类列表
   * @returns {Array} 分类名称数组
   */
  async getCategories() {
    try {
      return Array.from(this.categories);
    } catch (error) {
      logger.error('Failed to get categories:', error);
      return [];
    }
  }

  /**
   * 根据ID获取产品
   * @param {string} productId - 产品ID
   * @returns {Object|null} 产品对象
   */
  async getProductById(productId) {
    try {
      const product = this.products.get(productId);
      return product || null;
    } catch (error) {
      logger.error('Failed to get product by ID:', error);
      return null;
    }
  }

  /**
   * 更新产品
   * @param {string} productId - 产品ID
   * @param {Object} updateData - 更新数据
   * @returns {Object|null} 更新后的产品对象
   */
  async updateProduct(productId, updateData) {
    try {
      const existingProduct = this.products.get(productId);
      if (!existingProduct) {
        return null;
      }

      const updatedProduct = {
        ...existingProduct,
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      this.products.set(productId, updatedProduct);

      // 更新分类
      if (updatedProduct.category) {
        this.categories.add(updatedProduct.category);
      }

      logger.info(`Product updated: ${updatedProduct.name} (ID: ${productId})`);
      return updatedProduct;
    } catch (error) {
      logger.error('Failed to update product:', error);
      return null;
    }
  }

  /**
   * 删除产品
   * @param {string} productId - 产品ID
   * @returns {boolean} 删除是否成功
   */
  async deleteProduct(productId) {
    try {
      const deleted = this.products.delete(productId);
      if (deleted) {
        logger.info(`Product deleted: ID ${productId}`);
      }
      return deleted;
    } catch (error) {
      logger.error('Failed to delete product:', error);
      return false;
    }
  }

  /**
   * 清空所有产品数据
   */
  async clearAllProducts() {
    try {
      this.products.clear();
      this.categories.clear();
      this.nextId = 1;
      logger.info('All products cleared');
      return true;
    } catch (error) {
      logger.error('Failed to clear products:', error);
      return false;
    }
  }

  /**
   * 获取产品数量
   * @returns {number} 产品总数
   */
  getProductCount() {
    return this.products.size;
  }

  /**
   * 获取分类数量
   * @returns {number} 分类总数
   */
  getCategoryCount() {
    return this.categories.size;
  }
}

// 导出单例实例
module.exports = new ProductServiceForImport();

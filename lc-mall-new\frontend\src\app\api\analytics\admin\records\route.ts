/**
 * Analytics Admin Records API Route
 * 前端API路由 - 转发管理员访问记录请求到后端
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAdminHeaders } from '@/utils/adminAuth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, '') || 'http://localhost:5000/api';

export async function GET(request: NextRequest) {
  try {
    // 转发查询参数
    const searchParams = request.nextUrl.searchParams;
    const queryString = searchParams.toString();
    const url = `${API_BASE_URL}/analytics/admin/records${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAdminHeaders()
    });

    if (!response.ok) {
      throw new Error(`Backend responded with ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('Analytics admin records API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch analytics records',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

#!/bin/bash
# 修复nginx反向代理API路由问题

echo "=========================================="
echo "  修复nginx反向代理API路由问题           "
echo "=========================================="

echo "🔍 问题分析："
echo "• http://gdlongchi.cn:3000 ✅ 视频正常加载 (直接访问前端)"
echo "• http://gdlongchi.cn ❌ API endpoint not found (nginx代理)"
echo ""
echo "🎯 问题原因：nginx配置中API路由规则有问题"
echo ""

echo "1. 备份当前nginx配置..."
sudo cp /etc/nginx/sites-available/gdlongchi.cn /etc/nginx/sites-available/gdlongchi.cn.backup.$(date +%Y%m%d_%H%M%S)
echo "✅ 配置已备份"

echo ""
echo "2. 查看当前配置问题..."
echo "当前nginx配置摘要："
grep -E "(location|proxy_pass)" /etc/nginx/sites-available/gdlongchi.cn | head -10

echo ""
echo "3. 分析API路由问题..."

# 测试各种API路径
echo "测试API路径路由："
echo "• 前端API路由 (应该到3000端口):"
echo "  /api/videos → 前端Next.js API Routes"
echo "  /api/admin/stats → 前端Next.js API Routes"

echo "• 后端API路由 (应该到5000端口):" 
echo "  /api/products → 后端Express.js API"
echo "  /api/health → 后端Express.js API"

echo ""
echo "4. 创建正确的nginx配置..."

# 创建新的正确配置
sudo tee /etc/nginx/sites-available/gdlongchi.cn > /dev/null << 'EOF'
server {
    listen 80;
    listen [::]:80;
    server_name gdlongchi.cn www.gdlongchi.cn;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 日志配置
    access_log /var/log/nginx/gdlongchi.cn.access.log;
    error_log /var/log/nginx/gdlongchi.cn.error.log;
    
    # 特定前端API路由 (Next.js API Routes) - 优先级最高
    location ~ ^/api/(videos|admin|test) {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # 后端API路由 (Express.js API)
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # Next.js 特殊路径
    location /_next/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 静态文件 (后端服务的图片等)
    location /images/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 主页面和其他路由 (Next.js前端)
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
}
EOF

echo "✅ 新的nginx配置已创建"

echo ""
echo "5. 测试新配置..."
if sudo nginx -t; then
    echo "✅ nginx配置语法正确"
    
    echo ""
    echo "6. 重载nginx配置..."
    if sudo systemctl reload nginx; then
        echo "✅ nginx配置已重载"
        
        echo ""
        echo "7. 等待配置生效..."
        sleep 3
        
        echo ""
        echo "8. 测试修复结果..."
        
        echo "测试前端API路由："
        echo -n "• /api/videos: "
        if curl -s "http://gdlongchi.cn/api/videos" | grep -q "success"; then
            echo "✅ 正常"
        else
            echo "❌ 仍有问题"
            echo "  响应: $(curl -s "http://gdlongchi.cn/api/videos" | head -1)"
        fi
        
        echo -n "• /api/admin/stats: "
        if curl -s "http://gdlongchi.cn/api/admin/stats" | grep -q "success\|data"; then
            echo "✅ 正常"
        else
            echo "❌ 仍有问题"
            echo "  响应: $(curl -s "http://gdlongchi.cn/api/admin/stats" | head -1)"
        fi
        
        echo ""
        echo "测试后端API路由："
        echo -n "• /api/products: "
        if curl -s "http://gdlongchi.cn/api/products" | grep -q "success"; then
            echo "✅ 正常"
        else
            echo "❌ 仍有问题"
        fi
        
        echo -n "• /api/health: "
        if curl -s "http://gdlongchi.cn/api/health" | grep -q "healthy\|OK"; then
            echo "✅ 正常"
        else
            echo "❌ 仍有问题"
        fi
        
        echo ""
        echo "测试前端页面："
        echo -n "• 主页访问: "
        if curl -s "http://gdlongchi.cn" | grep -q "<title>"; then
            echo "✅ 正常"
        else
            echo "❌ 仍有问题"
        fi
        
    else
        echo "❌ nginx重载失败"
        sudo systemctl status nginx
    fi
else
    echo "❌ nginx配置语法错误"
    echo "恢复备份配置..."
    sudo cp /etc/nginx/sites-available/gdlongchi.cn.backup.* /etc/nginx/sites-available/gdlongchi.cn
fi

echo ""
echo "=========================================="
echo "🎯 修复结果"
echo "=========================================="
echo ""
echo "📋 新配置的路由规则："
echo "• /api/videos → 前端 (127.0.0.1:3000)"
echo "• /api/admin/* → 前端 (127.0.0.1:3000)"  
echo "• /api/* → 后端 (127.0.0.1:5000)"
echo "• /_next/* → 前端 (127.0.0.1:3000)"
echo "• /images/* → 后端 (127.0.0.1:5000)"
echo "• / → 前端 (127.0.0.1:3000)"
echo ""
echo "🧪 验证修复："
echo "1. 浏览器访问: http://gdlongchi.cn"
echo "2. 检查视频是否加载正常"
echo "3. 打开浏览器开发者工具，查看Network标签"
echo "4. 确认/api/videos请求状态为200"
echo ""
echo "📊 如果仍有问题："
echo "• 查看nginx错误日志: sudo tail -f /var/log/nginx/gdlongchi.cn.error.log"
echo "• 查看前端日志: tail -f logs/frontend.log"
echo "• 手动测试API: curl -v http://gdlongchi.cn/api/videos"
echo "=========================================="

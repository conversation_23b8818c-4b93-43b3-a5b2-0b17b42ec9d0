#!/bin/bash
# 综合问题修复指导脚本

echo "=========================================="
echo "  龙驰新材料商城 - 综合问题修复指导     "
echo "=========================================="

echo "基于之前的诊断结果，服务基本正常运行，但可能存在以下问题："
echo ""
echo "🔍 已发现的问题："
echo "1. ⚠️  前端standalone配置警告"
echo "2. ❓ nginx反向代理可能未配置"
echo "3. ❓ 域名访问可能有问题"
echo ""

echo "📋 修复步骤建议："
echo ""
echo "=== 第一步：网络诊断 ==="
echo "运行网络诊断脚本，检查nginx和域名访问状态："
echo "  ./network-diagnose.sh"
echo ""

echo "=== 第二步：nginx配置检查 ==="
echo "如果nginx未运行或配置有问题："
echo "  ./nginx-setup.sh"
echo ""

echo "=== 第三步：修复前端警告 ==="
echo "修复Next.js standalone配置警告："
echo "  ./fix-frontend-warning.sh"
echo ""

echo "=== 第四步：服务重启 ==="
echo "如果需要完全重启服务："
echo "  ./quick-restart.sh"
echo ""

echo "=== 第五步：验证修复 ==="
echo "再次运行诊断确认问题已解决："
echo "  ./diagnose-api-issue.sh"
echo ""

echo "=========================================="
echo "🚀 快速修复 (一键执行所有步骤)"
echo "=========================================="
echo "执行完整修复流程? (y/n): "
read -r auto_fix

if [ "$auto_fix" = "y" ]; then
    echo ""
    echo "🔧 开始自动修复流程..."
    echo ""
    
    echo "1️⃣ 网络诊断..."
    ./network-diagnose.sh
    echo ""
    
    echo "2️⃣ nginx配置检查..."
    echo "y" | ./nginx-setup.sh
    echo ""
    
    echo "3️⃣ 修复前端警告..."
    echo "1" | ./fix-frontend-warning.sh
    echo ""
    
    echo "4️⃣ 等待服务稳定..."
    sleep 10
    echo ""
    
    echo "5️⃣ 最终验证..."
    ./diagnose-api-issue.sh
    echo ""
    
    echo "=========================================="
    echo "🎉 自动修复流程完成！"
    echo "=========================================="
    echo ""
    echo "📊 验证访问："
    echo "• 本地访问前端: http://localhost:3000"
    echo "• 本地访问后端: http://localhost:5000/health"
    echo "• 域名访问(nginx): http://gdlongchi.cn"
    echo "• API测试: http://gdlongchi.cn/api/products"
    echo ""
    echo "📝 查看日志："
    echo "• 前端日志: tail -f logs/frontend.log"
    echo "• 后端日志: tail -f logs/backend.log"
    echo "• nginx日志: sudo tail -f /var/log/nginx/gdlongchi.cn.access.log"
    echo ""
    
else
    echo ""
    echo "请按照上述步骤手动执行修复。"
    echo ""
    echo "💡 提示："
    echo "• 每个脚本都是交互式的，会引导你完成配置"
    echo "• 建议按顺序执行，每步完成后验证结果"
    echo "• 如遇问题，可查看各个日志文件排查"
    echo ""
fi

echo "=========================================="
echo "📞 需要帮助？"
echo "=========================================="
echo "• 查看服务状态: ./check-services.sh"
echo "• 重启所有服务: ./quick-restart.sh"
echo "• 停止所有服务: ./stop.sh"
echo "• 诊断API问题: ./diagnose-api-issue.sh"
echo "• 诊断网络问题: ./network-diagnose.sh"
echo "=========================================="

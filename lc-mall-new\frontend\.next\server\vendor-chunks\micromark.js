"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark";
exports.ids = ["vendor-chunks/micromark"];
exports.modules = {

/***/ "(rsc)/../node_modules/micromark/dev/lib/constructs.js":
/*!*******************************************************!*\
  !*** ../node_modules/micromark/dev/lib/constructs.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attentionMarkers: () => (/* binding */ attentionMarkers),\n/* harmony export */   contentInitial: () => (/* binding */ contentInitial),\n/* harmony export */   disable: () => (/* binding */ disable),\n/* harmony export */   document: () => (/* binding */ document),\n/* harmony export */   flow: () => (/* binding */ flow),\n/* harmony export */   flowInitial: () => (/* binding */ flowInitial),\n/* harmony export */   insideSpan: () => (/* binding */ insideSpan),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/list.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/block-quote.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/definition.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/code-indented.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/heading-atx.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/setext-underline.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/html-flow.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/code-fenced.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/character-reference.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/character-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/line-ending.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/label-start-image.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/attention.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/autolink.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/html-text.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/label-start-link.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/code-text.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./initialize/text.js */ \"(rsc)/../node_modules/micromark/dev/lib/initialize/text.js\");\n/**\n * @import {Extension} from 'micromark-util-types'\n */ \n\n\n/** @satisfies {Extension['document']} */ const document = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit1]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit2]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit3]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit4]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit5]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit6]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit7]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit8]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit9]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.greaterThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.blockQuote\n};\n/** @satisfies {Extension['contentInitial']} */ const contentInitial = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__.definition\n};\n/** @satisfies {Extension['flowInitial']} */ const flowInitial = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented\n};\n/** @satisfies {Extension['flow']} */ const flow = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.numberSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__.headingAtx,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__.htmlFlow,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.equalsTo]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced\n};\n/** @satisfies {Extension['string']} */ const string = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n};\n/** @satisfies {Extension['text']} */ const text = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__.labelStartImage,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__.autolink,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__.htmlText\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__.labelStartLink,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__.hardBreakEscape,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__.labelEnd,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__.codeText\n};\n/** @satisfies {Extension['insideSpan']} */ const insideSpan = {\n    null: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n        _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__.resolver\n    ]\n};\n/** @satisfies {Extension['attentionMarkers']} */ const attentionMarkers = {\n    null: [\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore\n    ]\n};\n/** @satisfies {Extension['disable']} */ const disable = {\n    null: []\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kZXYvbGliL2NvbnN0cnVjdHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBOztDQUVDLEdBdUJpQztBQUNTO0FBQ2lCO0FBRTVELHVDQUF1QyxHQUNoQyxNQUFNdUIsV0FBVztJQUN0QixDQUFDSCx3REFBS0EsQ0FBQ0ksUUFBUSxDQUFDLEVBQUVQLDJEQUFJQTtJQUN0QixDQUFDRyx3REFBS0EsQ0FBQ0ssUUFBUSxDQUFDLEVBQUVSLDJEQUFJQTtJQUN0QixDQUFDRyx3REFBS0EsQ0FBQ00sSUFBSSxDQUFDLEVBQUVULDJEQUFJQTtJQUNsQixDQUFDRyx3REFBS0EsQ0FBQ08sTUFBTSxDQUFDLEVBQUVWLDJEQUFJQTtJQUNwQixDQUFDRyx3REFBS0EsQ0FBQ1EsTUFBTSxDQUFDLEVBQUVYLDJEQUFJQTtJQUNwQixDQUFDRyx3REFBS0EsQ0FBQ1MsTUFBTSxDQUFDLEVBQUVaLDJEQUFJQTtJQUNwQixDQUFDRyx3REFBS0EsQ0FBQ1UsTUFBTSxDQUFDLEVBQUViLDJEQUFJQTtJQUNwQixDQUFDRyx3REFBS0EsQ0FBQ1csTUFBTSxDQUFDLEVBQUVkLDJEQUFJQTtJQUNwQixDQUFDRyx3REFBS0EsQ0FBQ1ksTUFBTSxDQUFDLEVBQUVmLDJEQUFJQTtJQUNwQixDQUFDRyx3REFBS0EsQ0FBQ2EsTUFBTSxDQUFDLEVBQUVoQiwyREFBSUE7SUFDcEIsQ0FBQ0csd0RBQUtBLENBQUNjLE1BQU0sQ0FBQyxFQUFFakIsMkRBQUlBO0lBQ3BCLENBQUNHLHdEQUFLQSxDQUFDZSxNQUFNLENBQUMsRUFBRWxCLDJEQUFJQTtJQUNwQixDQUFDRyx3REFBS0EsQ0FBQ2dCLE1BQU0sQ0FBQyxFQUFFbkIsMkRBQUlBO0lBQ3BCLENBQUNHLHdEQUFLQSxDQUFDaUIsV0FBVyxDQUFDLEVBQUVuQyxpRUFBVUE7QUFDakMsRUFBQztBQUVELDZDQUE2QyxHQUN0QyxNQUFNb0MsaUJBQWlCO0lBQzVCLENBQUNsQix3REFBS0EsQ0FBQ21CLGlCQUFpQixDQUFDLEVBQUUvQixpRUFBVUE7QUFDdkMsRUFBQztBQUVELDBDQUEwQyxHQUNuQyxNQUFNZ0MsY0FBYztJQUN6QixDQUFDcEIsd0RBQUtBLENBQUNxQixhQUFhLENBQUMsRUFBRW5DLG1FQUFZQTtJQUNuQyxDQUFDYyx3REFBS0EsQ0FBQ3NCLFlBQVksQ0FBQyxFQUFFcEMsbUVBQVlBO0lBQ2xDLENBQUNjLHdEQUFLQSxDQUFDdUIsS0FBSyxDQUFDLEVBQUVyQyxtRUFBWUE7QUFDN0IsRUFBQztBQUVELG1DQUFtQyxHQUM1QixNQUFNc0MsT0FBTztJQUNsQixDQUFDeEIsd0RBQUtBLENBQUN5QixVQUFVLENBQUMsRUFBRW5DLGlFQUFVQTtJQUM5QixDQUFDVSx3REFBS0EsQ0FBQ0ksUUFBUSxDQUFDLEVBQUVMLG9FQUFhQTtJQUMvQixDQUFDQyx3REFBS0EsQ0FBQ00sSUFBSSxDQUFDLEVBQUU7UUFBQ1Isc0VBQWVBO1FBQUVDLG9FQUFhQTtLQUFDO0lBQzlDLENBQUNDLHdEQUFLQSxDQUFDMEIsUUFBUSxDQUFDLEVBQUVuQywrREFBUUE7SUFDMUIsQ0FBQ1Msd0RBQUtBLENBQUMyQixRQUFRLENBQUMsRUFBRTdCLHNFQUFlQTtJQUNqQyxDQUFDRSx3REFBS0EsQ0FBQzRCLFVBQVUsQ0FBQyxFQUFFN0Isb0VBQWFBO0lBQ2pDLENBQUNDLHdEQUFLQSxDQUFDNkIsV0FBVyxDQUFDLEVBQUU1QyxpRUFBVUE7SUFDL0IsQ0FBQ2Usd0RBQUtBLENBQUM4QixLQUFLLENBQUMsRUFBRTdDLGlFQUFVQTtBQUMzQixFQUFDO0FBRUQscUNBQXFDLEdBQzlCLE1BQU04QyxTQUFTO0lBQ3BCLENBQUMvQix3REFBS0EsQ0FBQ2dDLFNBQVMsQ0FBQyxFQUFFaEQsMEVBQWtCQTtJQUNyQyxDQUFDZ0Isd0RBQUtBLENBQUNpQyxTQUFTLENBQUMsRUFBRWxELHVFQUFlQTtBQUNwQyxFQUFDO0FBRUQsbUNBQW1DLEdBQzVCLE1BQU1tRCxPQUFPO0lBQ2xCLENBQUNsQyx3REFBS0EsQ0FBQ21DLGNBQWMsQ0FBQyxFQUFFdkMsa0VBQVVBO0lBQ2xDLENBQUNJLHdEQUFLQSxDQUFDb0MsUUFBUSxDQUFDLEVBQUV4QyxrRUFBVUE7SUFDNUIsQ0FBQ0ksd0RBQUtBLENBQUNxQyxzQkFBc0IsQ0FBQyxFQUFFekMsa0VBQVVBO0lBQzFDLENBQUNJLHdEQUFLQSxDQUFDc0MsZUFBZSxDQUFDLEVBQUU1Qyx1RUFBZUE7SUFDeEMsQ0FBQ00sd0RBQUtBLENBQUNnQyxTQUFTLENBQUMsRUFBRWhELDBFQUFrQkE7SUFDckMsQ0FBQ2dCLHdEQUFLQSxDQUFDSSxRQUFRLENBQUMsRUFBRXhCLGlFQUFTQTtJQUMzQixDQUFDb0Isd0RBQUtBLENBQUMwQixRQUFRLENBQUMsRUFBRTtRQUFDN0MsZ0VBQVFBO1FBQUVXLGdFQUFRQTtLQUFDO0lBQ3RDLENBQUNRLHdEQUFLQSxDQUFDbUIsaUJBQWlCLENBQUMsRUFBRXhCLHNFQUFjQTtJQUN6QyxDQUFDSyx3REFBS0EsQ0FBQ2lDLFNBQVMsQ0FBQyxFQUFFO1FBQUM1Qyx1RUFBZUE7UUFBRU4sdUVBQWVBO0tBQUM7SUFDckQsQ0FBQ2lCLHdEQUFLQSxDQUFDdUMsa0JBQWtCLENBQUMsRUFBRTlDLGdFQUFRQTtJQUNwQyxDQUFDTyx3REFBS0EsQ0FBQzRCLFVBQVUsQ0FBQyxFQUFFaEQsaUVBQVNBO0lBQzdCLENBQUNvQix3REFBS0EsQ0FBQzZCLFdBQVcsQ0FBQyxFQUFFMUMsZ0VBQVFBO0FBQy9CLEVBQUM7QUFFRCx5Q0FBeUMsR0FDbEMsTUFBTXFELGFBQWE7SUFBQ0MsTUFBTTtRQUFDN0QsaUVBQVNBO1FBQUVzQiwwREFBV0E7S0FBQztBQUFBLEVBQUM7QUFFMUQsK0NBQStDLEdBQ3hDLE1BQU13QyxtQkFBbUI7SUFBQ0QsTUFBTTtRQUFDekMsd0RBQUtBLENBQUNJLFFBQVE7UUFBRUosd0RBQUtBLENBQUM0QixVQUFVO0tBQUM7QUFBQSxFQUFDO0FBRTFFLHNDQUFzQyxHQUMvQixNQUFNZSxVQUFVO0lBQUNGLE1BQU0sRUFBRTtBQUFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYy1tYWxsLWZyb250ZW5kLy4uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGV2L2xpYi9jb25zdHJ1Y3RzLmpzPzNiNzUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtFeHRlbnNpb259IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbmltcG9ydCB7XG4gIGF0dGVudGlvbixcbiAgYXV0b2xpbmssXG4gIGJsb2NrUXVvdGUsXG4gIGNoYXJhY3RlckVzY2FwZSxcbiAgY2hhcmFjdGVyUmVmZXJlbmNlLFxuICBjb2RlRmVuY2VkLFxuICBjb2RlSW5kZW50ZWQsXG4gIGNvZGVUZXh0LFxuICBkZWZpbml0aW9uLFxuICBoYXJkQnJlYWtFc2NhcGUsXG4gIGhlYWRpbmdBdHgsXG4gIGh0bWxGbG93LFxuICBodG1sVGV4dCxcbiAgbGFiZWxFbmQsXG4gIGxhYmVsU3RhcnRJbWFnZSxcbiAgbGFiZWxTdGFydExpbmssXG4gIGxpbmVFbmRpbmcsXG4gIGxpc3QsXG4gIHNldGV4dFVuZGVybGluZSxcbiAgdGhlbWF0aWNCcmVha1xufSBmcm9tICdtaWNyb21hcmstY29yZS1jb21tb25tYXJrJ1xuaW1wb3J0IHtjb2Rlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sJ1xuaW1wb3J0IHtyZXNvbHZlciBhcyByZXNvbHZlVGV4dH0gZnJvbSAnLi9pbml0aWFsaXplL3RleHQuanMnXG5cbi8qKiBAc2F0aXNmaWVzIHtFeHRlbnNpb25bJ2RvY3VtZW50J119ICovXG5leHBvcnQgY29uc3QgZG9jdW1lbnQgPSB7XG4gIFtjb2Rlcy5hc3Rlcmlza106IGxpc3QsXG4gIFtjb2Rlcy5wbHVzU2lnbl06IGxpc3QsXG4gIFtjb2Rlcy5kYXNoXTogbGlzdCxcbiAgW2NvZGVzLmRpZ2l0MF06IGxpc3QsXG4gIFtjb2Rlcy5kaWdpdDFdOiBsaXN0LFxuICBbY29kZXMuZGlnaXQyXTogbGlzdCxcbiAgW2NvZGVzLmRpZ2l0M106IGxpc3QsXG4gIFtjb2Rlcy5kaWdpdDRdOiBsaXN0LFxuICBbY29kZXMuZGlnaXQ1XTogbGlzdCxcbiAgW2NvZGVzLmRpZ2l0Nl06IGxpc3QsXG4gIFtjb2Rlcy5kaWdpdDddOiBsaXN0LFxuICBbY29kZXMuZGlnaXQ4XTogbGlzdCxcbiAgW2NvZGVzLmRpZ2l0OV06IGxpc3QsXG4gIFtjb2Rlcy5ncmVhdGVyVGhhbl06IGJsb2NrUXVvdGVcbn1cblxuLyoqIEBzYXRpc2ZpZXMge0V4dGVuc2lvblsnY29udGVudEluaXRpYWwnXX0gKi9cbmV4cG9ydCBjb25zdCBjb250ZW50SW5pdGlhbCA9IHtcbiAgW2NvZGVzLmxlZnRTcXVhcmVCcmFja2V0XTogZGVmaW5pdGlvblxufVxuXG4vKiogQHNhdGlzZmllcyB7RXh0ZW5zaW9uWydmbG93SW5pdGlhbCddfSAqL1xuZXhwb3J0IGNvbnN0IGZsb3dJbml0aWFsID0ge1xuICBbY29kZXMuaG9yaXpvbnRhbFRhYl06IGNvZGVJbmRlbnRlZCxcbiAgW2NvZGVzLnZpcnR1YWxTcGFjZV06IGNvZGVJbmRlbnRlZCxcbiAgW2NvZGVzLnNwYWNlXTogY29kZUluZGVudGVkXG59XG5cbi8qKiBAc2F0aXNmaWVzIHtFeHRlbnNpb25bJ2Zsb3cnXX0gKi9cbmV4cG9ydCBjb25zdCBmbG93ID0ge1xuICBbY29kZXMubnVtYmVyU2lnbl06IGhlYWRpbmdBdHgsXG4gIFtjb2Rlcy5hc3Rlcmlza106IHRoZW1hdGljQnJlYWssXG4gIFtjb2Rlcy5kYXNoXTogW3NldGV4dFVuZGVybGluZSwgdGhlbWF0aWNCcmVha10sXG4gIFtjb2Rlcy5sZXNzVGhhbl06IGh0bWxGbG93LFxuICBbY29kZXMuZXF1YWxzVG9dOiBzZXRleHRVbmRlcmxpbmUsXG4gIFtjb2Rlcy51bmRlcnNjb3JlXTogdGhlbWF0aWNCcmVhayxcbiAgW2NvZGVzLmdyYXZlQWNjZW50XTogY29kZUZlbmNlZCxcbiAgW2NvZGVzLnRpbGRlXTogY29kZUZlbmNlZFxufVxuXG4vKiogQHNhdGlzZmllcyB7RXh0ZW5zaW9uWydzdHJpbmcnXX0gKi9cbmV4cG9ydCBjb25zdCBzdHJpbmcgPSB7XG4gIFtjb2Rlcy5hbXBlcnNhbmRdOiBjaGFyYWN0ZXJSZWZlcmVuY2UsXG4gIFtjb2Rlcy5iYWNrc2xhc2hdOiBjaGFyYWN0ZXJFc2NhcGVcbn1cblxuLyoqIEBzYXRpc2ZpZXMge0V4dGVuc2lvblsndGV4dCddfSAqL1xuZXhwb3J0IGNvbnN0IHRleHQgPSB7XG4gIFtjb2Rlcy5jYXJyaWFnZVJldHVybl06IGxpbmVFbmRpbmcsXG4gIFtjb2Rlcy5saW5lRmVlZF06IGxpbmVFbmRpbmcsXG4gIFtjb2Rlcy5jYXJyaWFnZVJldHVybkxpbmVGZWVkXTogbGluZUVuZGluZyxcbiAgW2NvZGVzLmV4Y2xhbWF0aW9uTWFya106IGxhYmVsU3RhcnRJbWFnZSxcbiAgW2NvZGVzLmFtcGVyc2FuZF06IGNoYXJhY3RlclJlZmVyZW5jZSxcbiAgW2NvZGVzLmFzdGVyaXNrXTogYXR0ZW50aW9uLFxuICBbY29kZXMubGVzc1RoYW5dOiBbYXV0b2xpbmssIGh0bWxUZXh0XSxcbiAgW2NvZGVzLmxlZnRTcXVhcmVCcmFja2V0XTogbGFiZWxTdGFydExpbmssXG4gIFtjb2Rlcy5iYWNrc2xhc2hdOiBbaGFyZEJyZWFrRXNjYXBlLCBjaGFyYWN0ZXJFc2NhcGVdLFxuICBbY29kZXMucmlnaHRTcXVhcmVCcmFja2V0XTogbGFiZWxFbmQsXG4gIFtjb2Rlcy51bmRlcnNjb3JlXTogYXR0ZW50aW9uLFxuICBbY29kZXMuZ3JhdmVBY2NlbnRdOiBjb2RlVGV4dFxufVxuXG4vKiogQHNhdGlzZmllcyB7RXh0ZW5zaW9uWydpbnNpZGVTcGFuJ119ICovXG5leHBvcnQgY29uc3QgaW5zaWRlU3BhbiA9IHtudWxsOiBbYXR0ZW50aW9uLCByZXNvbHZlVGV4dF19XG5cbi8qKiBAc2F0aXNmaWVzIHtFeHRlbnNpb25bJ2F0dGVudGlvbk1hcmtlcnMnXX0gKi9cbmV4cG9ydCBjb25zdCBhdHRlbnRpb25NYXJrZXJzID0ge251bGw6IFtjb2Rlcy5hc3RlcmlzaywgY29kZXMudW5kZXJzY29yZV19XG5cbi8qKiBAc2F0aXNmaWVzIHtFeHRlbnNpb25bJ2Rpc2FibGUnXX0gKi9cbmV4cG9ydCBjb25zdCBkaXNhYmxlID0ge251bGw6IFtdfVxuIl0sIm5hbWVzIjpbImF0dGVudGlvbiIsImF1dG9saW5rIiwiYmxvY2tRdW90ZSIsImNoYXJhY3RlckVzY2FwZSIsImNoYXJhY3RlclJlZmVyZW5jZSIsImNvZGVGZW5jZWQiLCJjb2RlSW5kZW50ZWQiLCJjb2RlVGV4dCIsImRlZmluaXRpb24iLCJoYXJkQnJlYWtFc2NhcGUiLCJoZWFkaW5nQXR4IiwiaHRtbEZsb3ciLCJodG1sVGV4dCIsImxhYmVsRW5kIiwibGFiZWxTdGFydEltYWdlIiwibGFiZWxTdGFydExpbmsiLCJsaW5lRW5kaW5nIiwibGlzdCIsInNldGV4dFVuZGVybGluZSIsInRoZW1hdGljQnJlYWsiLCJjb2RlcyIsInJlc29sdmVyIiwicmVzb2x2ZVRleHQiLCJkb2N1bWVudCIsImFzdGVyaXNrIiwicGx1c1NpZ24iLCJkYXNoIiwiZGlnaXQwIiwiZGlnaXQxIiwiZGlnaXQyIiwiZGlnaXQzIiwiZGlnaXQ0IiwiZGlnaXQ1IiwiZGlnaXQ2IiwiZGlnaXQ3IiwiZGlnaXQ4IiwiZGlnaXQ5IiwiZ3JlYXRlclRoYW4iLCJjb250ZW50SW5pdGlhbCIsImxlZnRTcXVhcmVCcmFja2V0IiwiZmxvd0luaXRpYWwiLCJob3Jpem9udGFsVGFiIiwidmlydHVhbFNwYWNlIiwic3BhY2UiLCJmbG93IiwibnVtYmVyU2lnbiIsImxlc3NUaGFuIiwiZXF1YWxzVG8iLCJ1bmRlcnNjb3JlIiwiZ3JhdmVBY2NlbnQiLCJ0aWxkZSIsInN0cmluZyIsImFtcGVyc2FuZCIsImJhY2tzbGFzaCIsInRleHQiLCJjYXJyaWFnZVJldHVybiIsImxpbmVGZWVkIiwiY2FycmlhZ2VSZXR1cm5MaW5lRmVlZCIsImV4Y2xhbWF0aW9uTWFyayIsInJpZ2h0U3F1YXJlQnJhY2tldCIsImluc2lkZVNwYW4iLCJudWxsIiwiYXR0ZW50aW9uTWFya2VycyIsImRpc2FibGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark/dev/lib/constructs.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark/dev/lib/create-tokenizer.js":
/*!*************************************************************!*\
  !*** ../node_modules/micromark/dev/lib/create-tokenizer.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTokenizer: () => (/* binding */ createTokenizer)\n/* harmony export */ });\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! debug */ \"(rsc)/../node_modules/debug/src/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(rsc)/../node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(rsc)/../node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/values.js\");\n/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */ /**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */ \n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_0__(\"micromark\");\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */ function createTokenizer(parser, initialize, from) {\n    /** @type {Point} */ let point = {\n        _bufferIndex: -1,\n        _index: 0,\n        line: from && from.line || 1,\n        column: from && from.column || 1,\n        offset: from && from.offset || 0\n    };\n    /** @type {Record<string, number>} */ const columnStart = {};\n    /** @type {Array<Construct>} */ const resolveAllConstructs = [];\n    /** @type {Array<Chunk>} */ let chunks = [];\n    /** @type {Array<Token>} */ let stack = [];\n    /** @type {boolean | undefined} */ let consumed = true;\n    /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */ const effects = {\n        attempt: constructFactory(onsuccessfulconstruct),\n        check: constructFactory(onsuccessfulcheck),\n        consume,\n        enter,\n        exit,\n        interrupt: constructFactory(onsuccessfulcheck, {\n            interrupt: true\n        })\n    };\n    /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */ const context = {\n        code: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n        containerState: {},\n        defineSkip,\n        events: [],\n        now,\n        parser,\n        previous: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n        sliceSerialize,\n        sliceStream,\n        write\n    };\n    /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */ let state = initialize.tokenize.call(context, effects);\n    /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */ let expectedCode;\n    if (initialize.resolveAll) {\n        resolveAllConstructs.push(initialize);\n    }\n    return context;\n    /** @type {TokenizeContext['write']} */ function write(slice) {\n        chunks = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(chunks, slice);\n        main();\n        // Exit if we’re not done, resolve might change stuff.\n        if (chunks[chunks.length - 1] !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            return [];\n        }\n        addResult(initialize, 0);\n        // Otherwise, resolve, and exit.\n        context.events = (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(resolveAllConstructs, context.events, context);\n        return context.events;\n    }\n    //\n    // Tools.\n    //\n    /** @type {TokenizeContext['sliceSerialize']} */ function sliceSerialize(token, expandTabs) {\n        return serializeChunks(sliceStream(token), expandTabs);\n    }\n    /** @type {TokenizeContext['sliceStream']} */ function sliceStream(token) {\n        return sliceChunks(chunks, token);\n    }\n    /** @type {TokenizeContext['now']} */ function now() {\n        // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n        const { _bufferIndex, _index, line, column, offset } = point;\n        return {\n            _bufferIndex,\n            _index,\n            line,\n            column,\n            offset\n        };\n    }\n    /** @type {TokenizeContext['defineSkip']} */ function defineSkip(value) {\n        columnStart[value.line] = value.column;\n        accountForPotentialSkip();\n        debug(\"position: define skip: `%j`\", point);\n    }\n    //\n    // State management.\n    //\n    /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */ function main() {\n        /** @type {number} */ let chunkIndex;\n        while(point._index < chunks.length){\n            const chunk = chunks[point._index];\n            // If we’re in a buffer chunk, loop through it.\n            if (typeof chunk === \"string\") {\n                chunkIndex = point._index;\n                if (point._bufferIndex < 0) {\n                    point._bufferIndex = 0;\n                }\n                while(point._index === chunkIndex && point._bufferIndex < chunk.length){\n                    go(chunk.charCodeAt(point._bufferIndex));\n                }\n            } else {\n                go(chunk);\n            }\n        }\n    }\n    /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */ function go(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === true, \"expected character to be consumed\");\n        consumed = undefined;\n        debug(\"main: passing `%s` to %s\", code, state && state.name);\n        expectedCode = code;\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof state === \"function\", \"expected state\");\n        state = state(code);\n    }\n    /** @type {Effects['consume']} */ function consume(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected given code to equal expected code\");\n        debug(\"consume: `%s`\", code);\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === undefined, \"expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === null ? context.events.length === 0 || context.events[context.events.length - 1][0] === \"exit\" : context.events[context.events.length - 1][0] === \"enter\", \"expected last token to be open\");\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n            point.line++;\n            point.column = 1;\n            point.offset += code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed ? 2 : 1;\n            accountForPotentialSkip();\n            debug(\"position: after eol: `%j`\", point);\n        } else if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace) {\n            point.column++;\n            point.offset++;\n        }\n        // Not in a string chunk.\n        if (point._bufferIndex < 0) {\n            point._index++;\n        } else {\n            point._bufferIndex++;\n            // At end of string chunk.\n            if (point._bufferIndex === // Points w/ non-negative `_bufferIndex` reference\n            // strings.\n            /** @type {string} */ chunks[point._index].length) {\n                point._bufferIndex = -1;\n                point._index++;\n            }\n        }\n        // Expose the previous character.\n        context.previous = code;\n        // Mark as consumed.\n        consumed = true;\n    }\n    /** @type {Effects['enter']} */ function enter(type, fields) {\n        /** @type {Token} */ // @ts-expect-error Patch instead of assign required fields to help GC.\n        const token = fields || {};\n        token.type = type;\n        token.start = now();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === \"string\", \"expected string type\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, \"expected non-empty string\");\n        debug(\"enter: `%s`\", type);\n        context.events.push([\n            \"enter\",\n            token,\n            context\n        ]);\n        stack.push(token);\n        return token;\n    }\n    /** @type {Effects['exit']} */ function exit(type) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === \"string\", \"expected string type\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, \"expected non-empty string\");\n        const token = stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(token, \"cannot close w/o open tokens\");\n        token.end = now();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type === token.type, \"expected exit token to match current token\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(!(token.start._index === token.end._index && token.start._bufferIndex === token.end._bufferIndex), \"expected non-empty token (`\" + type + \"`)\");\n        debug(\"exit: `%s`\", token.type);\n        context.events.push([\n            \"exit\",\n            token,\n            context\n        ]);\n        return token;\n    }\n    /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */ function onsuccessfulconstruct(construct, info) {\n        addResult(construct, info.from);\n    }\n    /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */ function onsuccessfulcheck(_, info) {\n        info.restore();\n    }\n    /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */ function constructFactory(onreturn, fields) {\n        return hook;\n        /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */ function hook(constructs, returnState, bogusState) {\n            /** @type {ReadonlyArray<Construct>} */ let listOfConstructs;\n            /** @type {number} */ let constructIndex;\n            /** @type {Construct} */ let currentConstruct;\n            /** @type {Info} */ let info;\n            return Array.isArray(constructs) ? /* c8 ignore next 1 */ handleListOfConstructs(constructs) : \"tokenize\" in constructs ? handleListOfConstructs([\n                /** @type {Construct} */ constructs\n            ]) : handleMapOfConstructs(constructs);\n            /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */ function handleMapOfConstructs(map) {\n                return start;\n                /** @type {State} */ function start(code) {\n                    const left = code !== null && map[code];\n                    const all = code !== null && map.null;\n                    const list = [\n                        // To do: add more extension tests.\n                        /* c8 ignore next 2 */ ...Array.isArray(left) ? left : left ? [\n                            left\n                        ] : [],\n                        ...Array.isArray(all) ? all : all ? [\n                            all\n                        ] : []\n                    ];\n                    return handleListOfConstructs(list)(code);\n                }\n            }\n            /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */ function handleListOfConstructs(list) {\n                listOfConstructs = list;\n                constructIndex = 0;\n                if (list.length === 0) {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(bogusState, \"expected `bogusState` to be given\");\n                    return bogusState;\n                }\n                return handleConstruct(list[constructIndex]);\n            }\n            /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */ function handleConstruct(construct) {\n                return start;\n                /** @type {State} */ function start(code) {\n                    // To do: not needed to store if there is no bogus state, probably?\n                    // Currently doesn’t work because `inspect` in document does a check\n                    // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n                    // by not storing.\n                    info = store();\n                    currentConstruct = construct;\n                    if (!construct.partial) {\n                        context.currentConstruct = construct;\n                    }\n                    // Always populated by defaults.\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(context.parser.constructs.disable.null, \"expected `disable.null` to be populated\");\n                    if (construct.name && context.parser.constructs.disable.null.includes(construct.name)) {\n                        return nok(code);\n                    }\n                    return construct.tokenize.call(// If we do have fields, create an object w/ `context` as its\n                    // prototype.\n                    // This allows a “live binding”, which is needed for `interrupt`.\n                    fields ? Object.assign(Object.create(context), fields) : context, effects, ok, nok)(code);\n                }\n            }\n            /** @type {State} */ function ok(code) {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected code\");\n                consumed = true;\n                onreturn(currentConstruct, info);\n                return returnState;\n            }\n            /** @type {State} */ function nok(code) {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected code\");\n                consumed = true;\n                info.restore();\n                if (++constructIndex < listOfConstructs.length) {\n                    return handleConstruct(listOfConstructs[constructIndex]);\n                }\n                return bogusState;\n            }\n        }\n    }\n    /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */ function addResult(construct, from) {\n        if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n            resolveAllConstructs.push(construct);\n        }\n        if (construct.resolve) {\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(context.events, from, context.events.length - from, construct.resolve(context.events.slice(from), context));\n        }\n        if (construct.resolveTo) {\n            context.events = construct.resolveTo(context.events, context);\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(construct.partial || context.events.length === 0 || context.events[context.events.length - 1][0] === \"exit\", \"expected last token to end\");\n    }\n    /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */ function store() {\n        const startPoint = now();\n        const startPrevious = context.previous;\n        const startCurrentConstruct = context.currentConstruct;\n        const startEventsIndex = context.events.length;\n        const startStack = Array.from(stack);\n        return {\n            from: startEventsIndex,\n            restore\n        };\n        /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */ function restore() {\n            point = startPoint;\n            context.previous = startPrevious;\n            context.currentConstruct = startCurrentConstruct;\n            context.events.length = startEventsIndex;\n            stack = startStack;\n            accountForPotentialSkip();\n            debug(\"position: restore: `%j`\", point);\n        }\n    }\n    /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */ function accountForPotentialSkip() {\n        if (point.line in columnStart && point.column < 2) {\n            point.column = columnStart[point.line];\n            point.offset += columnStart[point.line] - 1;\n        }\n    }\n}\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */ function sliceChunks(chunks, token) {\n    const startIndex = token.start._index;\n    const startBufferIndex = token.start._bufferIndex;\n    const endIndex = token.end._index;\n    const endBufferIndex = token.end._bufferIndex;\n    /** @type {Array<Chunk>} */ let view;\n    if (startIndex === endIndex) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(endBufferIndex > -1, \"expected non-negative end buffer index\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex > -1, \"expected non-negative start buffer index\");\n        // @ts-expect-error `_bufferIndex` is used on string chunks.\n        view = [\n            chunks[startIndex].slice(startBufferIndex, endBufferIndex)\n        ];\n    } else {\n        view = chunks.slice(startIndex, endIndex);\n        if (startBufferIndex > -1) {\n            const head = view[0];\n            if (typeof head === \"string\") {\n                view[0] = head.slice(startBufferIndex);\n            /* c8 ignore next 4 -- used to be used, no longer */ } else {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex === 0, \"expected `startBufferIndex` to be `0`\");\n                view.shift();\n            }\n        }\n        if (endBufferIndex > 0) {\n            // @ts-expect-error `_bufferIndex` is used on string chunks.\n            view.push(chunks[endIndex].slice(0, endBufferIndex));\n        }\n    }\n    return view;\n}\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */ function serializeChunks(chunks, expandTabs) {\n    let index = -1;\n    /** @type {Array<string>} */ const result = [];\n    /** @type {boolean | undefined} */ let atTab;\n    while(++index < chunks.length){\n        const chunk = chunks[index];\n        /** @type {string} */ let value;\n        if (typeof chunk === \"string\") {\n            value = chunk;\n        } else switch(chunk){\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturn:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lineFeed:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab:\n                {\n                    value = expandTabs ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.ht;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace:\n                {\n                    if (!expandTabs && atTab) continue;\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space;\n                    break;\n                }\n            default:\n                {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof chunk === \"number\", \"expected number\");\n                    // Currently only replacement character.\n                    value = String.fromCharCode(chunk);\n                }\n        }\n        atTab = chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab;\n        result.push(value);\n    }\n    return result.join(\"\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark/dev/lib/create-tokenizer.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark/dev/lib/initialize/content.js":
/*!***************************************************************!*\
  !*** ../node_modules/micromark/dev/lib/initialize/content.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(rsc)/../node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */ \n\n\n\n/** @type {InitialConstruct} */ const content = {\n    tokenize: initializeContent\n};\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */ function initializeContent(effects) {\n    const contentStart = effects.attempt(this.parser.constructs.contentInitial, afterContentStartConstruct, paragraphInitial);\n    /** @type {Token} */ let previous;\n    return contentStart;\n    /** @type {State} */ function afterContentStartConstruct(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, contentStart, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix);\n    }\n    /** @type {State} */ function paragraphInitial(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code), \"expected anything other than a line ending or EOF\");\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph);\n        return lineStart(code);\n    }\n    /** @type {State} */ function lineStart(code) {\n        const token = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText, {\n            contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText,\n            previous\n        });\n        if (previous) {\n            previous.next = token;\n        }\n        previous = token;\n        return data(code);\n    }\n    /** @type {State} */ function data(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText);\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph);\n            effects.consume(code);\n            return;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            effects.consume(code);\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText);\n            return lineStart;\n        }\n        // Data.\n        effects.consume(code);\n        return data;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark/dev/lib/initialize/content.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark/dev/lib/initialize/document.js":
/*!****************************************************************!*\
  !*** ../node_modules/micromark/dev/lib/initialize/document.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   document: () => (/* binding */ document)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(rsc)/../node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(rsc)/../node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */ /**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */ \n\n\n\n\n/** @type {InitialConstruct} */ const document = {\n    tokenize: initializeDocument\n};\n/** @type {Construct} */ const containerConstruct = {\n    tokenize: tokenizeContainer\n};\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */ function initializeDocument(effects) {\n    const self = this;\n    /** @type {Array<StackItem>} */ const stack = [];\n    let continued = 0;\n    /** @type {TokenizeContext | undefined} */ let childFlow;\n    /** @type {Token | undefined} */ let childToken;\n    /** @type {number} */ let lineStartOffset;\n    return start;\n    /** @type {State} */ function start(code) {\n        // First we iterate through the open blocks, starting with the root\n        // document, and descending through last children down to the last open\n        // block.\n        // Each block imposes a condition that the line must satisfy if the block is\n        // to remain open.\n        // For example, a block quote requires a `>` character.\n        // A paragraph requires a non-blank line.\n        // In this phase we may match all or just some of the open blocks.\n        // But we cannot close unmatched blocks yet, because we may have a lazy\n        // continuation line.\n        if (continued < stack.length) {\n            const item = stack[continued];\n            self.containerState = item[1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(item[0].continuation, \"expected `continuation` to be defined on container construct\");\n            return effects.attempt(item[0].continuation, documentContinue, checkNewContainers)(code);\n        }\n        // Done.\n        return checkNewContainers(code);\n    }\n    /** @type {State} */ function documentContinue(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined after continuation\");\n        continued++;\n        // Note: this field is called `_closeFlow` but it also closes containers.\n        // Perhaps a good idea to rename it but it’s already used in the wild by\n        // extensions.\n        if (self.containerState._closeFlow) {\n            self.containerState._closeFlow = undefined;\n            if (childFlow) {\n                closeFlow();\n            }\n            // Note: this algorithm for moving events around is similar to the\n            // algorithm when dealing with lazy lines in `writeToChild`.\n            const indexBeforeExits = self.events.length;\n            let indexBeforeFlow = indexBeforeExits;\n            /** @type {Point | undefined} */ let point;\n            // Find the flow chunk.\n            while(indexBeforeFlow--){\n                if (self.events[indexBeforeFlow][0] === \"exit\" && self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow) {\n                    point = self.events[indexBeforeFlow][1].end;\n                    break;\n                }\n            }\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, \"could not find previous flow chunk\");\n            exitContainers(continued);\n            // Fix positions.\n            let index = indexBeforeExits;\n            while(index < self.events.length){\n                self.events[index][1].end = {\n                    ...point\n                };\n                index++;\n            }\n            // Inject the exits earlier (they’re still also at the end).\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n            // Discard the duplicate exits.\n            self.events.length = index;\n            return checkNewContainers(code);\n        }\n        return start(code);\n    }\n    /** @type {State} */ function checkNewContainers(code) {\n        // Next, after consuming the continuation markers for existing blocks, we\n        // look for new block starts (e.g. `>` for a block quote).\n        // If we encounter a new block start, we close any blocks unmatched in\n        // step 1 before creating the new block as a child of the last matched\n        // block.\n        if (continued === stack.length) {\n            // No need to `check` whether there’s a container, of `exitContainers`\n            // would be moot.\n            // We can instead immediately `attempt` to parse one.\n            if (!childFlow) {\n                return documentContinued(code);\n            }\n            // If we have concrete content, such as block HTML or fenced code,\n            // we can’t have containers “pierce” into them, so we can immediately\n            // start.\n            if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n                return flowStart(code);\n            }\n            // If we do have flow, it could still be a blank line,\n            // but we’d be interrupting it w/ a new container if there’s a current\n            // construct.\n            // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n            // needed in micromark-extension-gfm-table@1.0.6).\n            self.interrupt = Boolean(childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack);\n        }\n        // Check if there is a new container.\n        self.containerState = {};\n        return effects.check(containerConstruct, thereIsANewContainer, thereIsNoNewContainer)(code);\n    }\n    /** @type {State} */ function thereIsANewContainer(code) {\n        if (childFlow) closeFlow();\n        exitContainers(continued);\n        return documentContinued(code);\n    }\n    /** @type {State} */ function thereIsNoNewContainer(code) {\n        self.parser.lazy[self.now().line] = continued !== stack.length;\n        lineStartOffset = self.now().offset;\n        return flowStart(code);\n    }\n    /** @type {State} */ function documentContinued(code) {\n        // Try new containers.\n        self.containerState = {};\n        return effects.attempt(containerConstruct, containerContinue, flowStart)(code);\n    }\n    /** @type {State} */ function containerContinue(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.currentConstruct, \"expected `currentConstruct` to be defined on tokenizer\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined on tokenizer\");\n        continued++;\n        stack.push([\n            self.currentConstruct,\n            self.containerState\n        ]);\n        // Try another.\n        return documentContinued(code);\n    }\n    /** @type {State} */ function flowStart(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n            if (childFlow) closeFlow();\n            exitContainers(0);\n            effects.consume(code);\n            return;\n        }\n        childFlow = childFlow || self.parser.flow(self.now());\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow, {\n            _tokenizer: childFlow,\n            contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.contentTypeFlow,\n            previous: childToken\n        });\n        return flowContinue(code);\n    }\n    /** @type {State} */ function flowContinue(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n            writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow), true);\n            exitContainers(0);\n            effects.consume(code);\n            return;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n            effects.consume(code);\n            writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow));\n            // Get ready for the next line.\n            continued = 0;\n            self.interrupt = undefined;\n            return start;\n        }\n        effects.consume(code);\n        return flowContinue;\n    }\n    /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */ function writeToChild(token, endOfFile) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, \"expected `childFlow` to be defined when continuing\");\n        const stream = self.sliceStream(token);\n        if (endOfFile) stream.push(null);\n        token.previous = childToken;\n        if (childToken) childToken.next = token;\n        childToken = token;\n        childFlow.defineSkip(token.start);\n        childFlow.write(stream);\n        // Alright, so we just added a lazy line:\n        //\n        // ```markdown\n        // > a\n        // b.\n        //\n        // Or:\n        //\n        // > ~~~c\n        // d\n        //\n        // Or:\n        //\n        // > | e |\n        // f\n        // ```\n        //\n        // The construct in the second example (fenced code) does not accept lazy\n        // lines, so it marked itself as done at the end of its first line, and\n        // then the content construct parses `d`.\n        // Most constructs in markdown match on the first line: if the first line\n        // forms a construct, a non-lazy line can’t “unmake” it.\n        //\n        // The construct in the third example is potentially a GFM table, and\n        // those are *weird*.\n        // It *could* be a table, from the first line, if the following line\n        // matches a condition.\n        // In this case, that second line is lazy, which “unmakes” the first line\n        // and turns the whole into one content block.\n        //\n        // We’ve now parsed the non-lazy and the lazy line, and can figure out\n        // whether the lazy line started a new flow block.\n        // If it did, we exit the current containers between the two flow blocks.\n        if (self.parser.lazy[token.start.line]) {\n            let index = childFlow.events.length;\n            while(index--){\n                if (// The token starts before the line ending…\n                childFlow.events[index][1].start.offset < lineStartOffset && // …and either is not ended yet…\n                (!childFlow.events[index][1].end || // …or ends after it.\n                childFlow.events[index][1].end.offset > lineStartOffset)) {\n                    // Exit: there’s still something open, which means it’s a lazy line\n                    // part of something.\n                    return;\n                }\n            }\n            // Note: this algorithm for moving events around is similar to the\n            // algorithm when closing flow in `documentContinue`.\n            const indexBeforeExits = self.events.length;\n            let indexBeforeFlow = indexBeforeExits;\n            /** @type {boolean | undefined} */ let seen;\n            /** @type {Point | undefined} */ let point;\n            // Find the previous chunk (the one before the lazy line).\n            while(indexBeforeFlow--){\n                if (self.events[indexBeforeFlow][0] === \"exit\" && self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow) {\n                    if (seen) {\n                        point = self.events[indexBeforeFlow][1].end;\n                        break;\n                    }\n                    seen = true;\n                }\n            }\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, \"could not find previous flow chunk\");\n            exitContainers(continued);\n            // Fix positions.\n            index = indexBeforeExits;\n            while(index < self.events.length){\n                self.events[index][1].end = {\n                    ...point\n                };\n                index++;\n            }\n            // Inject the exits earlier (they’re still also at the end).\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n            // Discard the duplicate exits.\n            self.events.length = index;\n        }\n    }\n    /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */ function exitContainers(size) {\n        let index = stack.length;\n        // Exit open containers.\n        while(index-- > size){\n            const entry = stack[index];\n            self.containerState = entry[1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(entry[0].exit, \"expected `exit` to be defined on container construct\");\n            entry[0].exit.call(self, effects);\n        }\n        stack.length = size;\n    }\n    function closeFlow() {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined when closing flow\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, \"expected `childFlow` to be defined when closing it\");\n        childFlow.write([\n            micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof\n        ]);\n        childToken = undefined;\n        childFlow = undefined;\n        self.containerState._closeFlow = undefined;\n    }\n}\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */ function tokenizeContainer(effects, ok, nok) {\n    // Always populated by defaults.\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(this.parser.constructs.disable.null, \"expected `disable.null` to be populated\");\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, effects.attempt(this.parser.constructs.document, ok, nok), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix, this.parser.constructs.disable.null.includes(\"codeIndented\") ? undefined : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark/dev/lib/initialize/document.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark/dev/lib/initialize/flow.js":
/*!************************************************************!*\
  !*** ../node_modules/micromark/dev/lib/initialize/flow.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flow: () => (/* binding */ flow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/../node_modules/micromark-core-commonmark/dev/lib/content.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ \"(rsc)/../node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */ \n\n\n\n\n/** @type {InitialConstruct} */ const flow = {\n    tokenize: initializeFlow\n};\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */ function initializeFlow(effects) {\n    const self = this;\n    const initial = effects.attempt(// Try to parse a blank line.\n    micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__.blankLine, atBlankEnding, // Try to parse initial flow (essentially, only code).\n    effects.attempt(this.parser.constructs.flowInitial, afterConstruct, (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(effects, effects.attempt(this.parser.constructs.flow, afterConstruct, effects.attempt(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.content, afterConstruct)), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix)));\n    return initial;\n    /** @type {State} */ function atBlankEnding(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank);\n        self.currentConstruct = undefined;\n        return initial;\n    }\n    /** @type {State} */ function afterConstruct(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        self.currentConstruct = undefined;\n        return initial;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kZXYvbGliL2luaXRpYWxpemUvZmxvdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBOzs7Ozs7O0NBT0MsR0FFa0M7QUFDeUI7QUFDUjtBQUNPO0FBQ1Q7QUFFbEQsNkJBQTZCLEdBQ3RCLE1BQU1RLE9BQU87SUFBQ0MsVUFBVUM7QUFBYyxFQUFDO0FBRTlDOzs7OztDQUtDLEdBQ0QsU0FBU0EsZUFBZUMsT0FBTztJQUM3QixNQUFNQyxPQUFPLElBQUk7SUFDakIsTUFBTUMsVUFBVUYsUUFBUUcsT0FBTyxDQUM3Qiw2QkFBNkI7SUFDN0JaLGdFQUFTQSxFQUNUYSxlQUNBLHNEQUFzRDtJQUN0REosUUFBUUcsT0FBTyxDQUNiLElBQUksQ0FBQ0UsTUFBTSxDQUFDQyxVQUFVLENBQUNDLFdBQVcsRUFDbENDLGdCQUNBZixxRUFBWUEsQ0FDVk8sU0FDQUEsUUFBUUcsT0FBTyxDQUNiLElBQUksQ0FBQ0UsTUFBTSxDQUFDQyxVQUFVLENBQUNULElBQUksRUFDM0JXLGdCQUNBUixRQUFRRyxPQUFPLENBQUNYLDhEQUFPQSxFQUFFZ0Isa0JBRTNCWix3REFBS0EsQ0FBQ2EsVUFBVTtJQUt0QixPQUFPUDtJQUVQLGtCQUFrQixHQUNsQixTQUFTRSxjQUFjTSxJQUFJO1FBQ3pCcEIsMENBQU1BLENBQ0pvQixTQUFTZix3REFBS0EsQ0FBQ2dCLEdBQUcsSUFBSWpCLDRFQUFrQkEsQ0FBQ2dCLE9BQ3pDO1FBR0YsSUFBSUEsU0FBU2Ysd0RBQUtBLENBQUNnQixHQUFHLEVBQUU7WUFDdEJYLFFBQVFZLE9BQU8sQ0FBQ0Y7WUFDaEI7UUFDRjtRQUVBVixRQUFRYSxLQUFLLENBQUNqQix3REFBS0EsQ0FBQ2tCLGVBQWU7UUFDbkNkLFFBQVFZLE9BQU8sQ0FBQ0Y7UUFDaEJWLFFBQVFlLElBQUksQ0FBQ25CLHdEQUFLQSxDQUFDa0IsZUFBZTtRQUNsQ2IsS0FBS2UsZ0JBQWdCLEdBQUdDO1FBQ3hCLE9BQU9mO0lBQ1Q7SUFFQSxrQkFBa0IsR0FDbEIsU0FBU00sZUFBZUUsSUFBSTtRQUMxQnBCLDBDQUFNQSxDQUNKb0IsU0FBU2Ysd0RBQUtBLENBQUNnQixHQUFHLElBQUlqQiw0RUFBa0JBLENBQUNnQixPQUN6QztRQUdGLElBQUlBLFNBQVNmLHdEQUFLQSxDQUFDZ0IsR0FBRyxFQUFFO1lBQ3RCWCxRQUFRWSxPQUFPLENBQUNGO1lBQ2hCO1FBQ0Y7UUFFQVYsUUFBUWEsS0FBSyxDQUFDakIsd0RBQUtBLENBQUNzQixVQUFVO1FBQzlCbEIsUUFBUVksT0FBTyxDQUFDRjtRQUNoQlYsUUFBUWUsSUFBSSxDQUFDbkIsd0RBQUtBLENBQUNzQixVQUFVO1FBQzdCakIsS0FBS2UsZ0JBQWdCLEdBQUdDO1FBQ3hCLE9BQU9mO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kZXYvbGliL2luaXRpYWxpemUvZmxvdy5qcz9mYTdkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7XG4gKiAgIEluaXRpYWxDb25zdHJ1Y3QsXG4gKiAgIEluaXRpYWxpemVyLFxuICogICBTdGF0ZSxcbiAqICAgVG9rZW5pemVDb250ZXh0XG4gKiB9IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbmltcG9ydCB7b2sgYXMgYXNzZXJ0fSBmcm9tICdkZXZsb3AnXG5pbXBvcnQge2JsYW5rTGluZSwgY29udGVudH0gZnJvbSAnbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyaydcbmltcG9ydCB7ZmFjdG9yeVNwYWNlfSBmcm9tICdtaWNyb21hcmstZmFjdG9yeS1zcGFjZSdcbmltcG9ydCB7bWFya2Rvd25MaW5lRW5kaW5nfSBmcm9tICdtaWNyb21hcmstdXRpbC1jaGFyYWN0ZXInXG5pbXBvcnQge2NvZGVzLCB0eXBlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sJ1xuXG4vKiogQHR5cGUge0luaXRpYWxDb25zdHJ1Y3R9ICovXG5leHBvcnQgY29uc3QgZmxvdyA9IHt0b2tlbml6ZTogaW5pdGlhbGl6ZUZsb3d9XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqICAgU2VsZi5cbiAqIEB0eXBlIHtJbml0aWFsaXplcn1cbiAqICAgSW5pdGlhbGl6ZXIuXG4gKi9cbmZ1bmN0aW9uIGluaXRpYWxpemVGbG93KGVmZmVjdHMpIHtcbiAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgY29uc3QgaW5pdGlhbCA9IGVmZmVjdHMuYXR0ZW1wdChcbiAgICAvLyBUcnkgdG8gcGFyc2UgYSBibGFuayBsaW5lLlxuICAgIGJsYW5rTGluZSxcbiAgICBhdEJsYW5rRW5kaW5nLFxuICAgIC8vIFRyeSB0byBwYXJzZSBpbml0aWFsIGZsb3cgKGVzc2VudGlhbGx5LCBvbmx5IGNvZGUpLlxuICAgIGVmZmVjdHMuYXR0ZW1wdChcbiAgICAgIHRoaXMucGFyc2VyLmNvbnN0cnVjdHMuZmxvd0luaXRpYWwsXG4gICAgICBhZnRlckNvbnN0cnVjdCxcbiAgICAgIGZhY3RvcnlTcGFjZShcbiAgICAgICAgZWZmZWN0cyxcbiAgICAgICAgZWZmZWN0cy5hdHRlbXB0KFxuICAgICAgICAgIHRoaXMucGFyc2VyLmNvbnN0cnVjdHMuZmxvdyxcbiAgICAgICAgICBhZnRlckNvbnN0cnVjdCxcbiAgICAgICAgICBlZmZlY3RzLmF0dGVtcHQoY29udGVudCwgYWZ0ZXJDb25zdHJ1Y3QpXG4gICAgICAgICksXG4gICAgICAgIHR5cGVzLmxpbmVQcmVmaXhcbiAgICAgIClcbiAgICApXG4gIClcblxuICByZXR1cm4gaW5pdGlhbFxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGZ1bmN0aW9uIGF0QmxhbmtFbmRpbmcoY29kZSkge1xuICAgIGFzc2VydChcbiAgICAgIGNvZGUgPT09IGNvZGVzLmVvZiB8fCBtYXJrZG93bkxpbmVFbmRpbmcoY29kZSksXG4gICAgICAnZXhwZWN0ZWQgZW9sIG9yIGVvZidcbiAgICApXG5cbiAgICBpZiAoY29kZSA9PT0gY29kZXMuZW9mKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGVmZmVjdHMuZW50ZXIodHlwZXMubGluZUVuZGluZ0JsYW5rKVxuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIGVmZmVjdHMuZXhpdCh0eXBlcy5saW5lRW5kaW5nQmxhbmspXG4gICAgc2VsZi5jdXJyZW50Q29uc3RydWN0ID0gdW5kZWZpbmVkXG4gICAgcmV0dXJuIGluaXRpYWxcbiAgfVxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGZ1bmN0aW9uIGFmdGVyQ29uc3RydWN0KGNvZGUpIHtcbiAgICBhc3NlcnQoXG4gICAgICBjb2RlID09PSBjb2Rlcy5lb2YgfHwgbWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpLFxuICAgICAgJ2V4cGVjdGVkIGVvbCBvciBlb2YnXG4gICAgKVxuXG4gICAgaWYgKGNvZGUgPT09IGNvZGVzLmVvZikge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBlZmZlY3RzLmVudGVyKHR5cGVzLmxpbmVFbmRpbmcpXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgZWZmZWN0cy5leGl0KHR5cGVzLmxpbmVFbmRpbmcpXG4gICAgc2VsZi5jdXJyZW50Q29uc3RydWN0ID0gdW5kZWZpbmVkXG4gICAgcmV0dXJuIGluaXRpYWxcbiAgfVxufVxuIl0sIm5hbWVzIjpbIm9rIiwiYXNzZXJ0IiwiYmxhbmtMaW5lIiwiY29udGVudCIsImZhY3RvcnlTcGFjZSIsIm1hcmtkb3duTGluZUVuZGluZyIsImNvZGVzIiwidHlwZXMiLCJmbG93IiwidG9rZW5pemUiLCJpbml0aWFsaXplRmxvdyIsImVmZmVjdHMiLCJzZWxmIiwiaW5pdGlhbCIsImF0dGVtcHQiLCJhdEJsYW5rRW5kaW5nIiwicGFyc2VyIiwiY29uc3RydWN0cyIsImZsb3dJbml0aWFsIiwiYWZ0ZXJDb25zdHJ1Y3QiLCJsaW5lUHJlZml4IiwiY29kZSIsImVvZiIsImNvbnN1bWUiLCJlbnRlciIsImxpbmVFbmRpbmdCbGFuayIsImV4aXQiLCJjdXJyZW50Q29uc3RydWN0IiwidW5kZWZpbmVkIiwibGluZUVuZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark/dev/lib/initialize/flow.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark/dev/lib/initialize/text.js":
/*!************************************************************!*\
  !*** ../node_modules/micromark/dev/lib/initialize/text.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolver: () => (/* binding */ resolver),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */ \n\nconst resolver = {\n    resolveAll: createResolver()\n};\nconst string = initializeFactory(\"string\");\nconst text = initializeFactory(\"text\");\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */ function initializeFactory(field) {\n    return {\n        resolveAll: createResolver(field === \"text\" ? resolveAllLineSuffixes : undefined),\n        tokenize: initializeText\n    };\n    /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */ function initializeText(effects) {\n        const self = this;\n        const constructs = this.parser.constructs[field];\n        const text = effects.attempt(constructs, start, notText);\n        return start;\n        /** @type {State} */ function start(code) {\n            return atBreak(code) ? text(code) : notText(code);\n        }\n        /** @type {State} */ function notText(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n                effects.consume(code);\n                return;\n            }\n            effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data);\n            effects.consume(code);\n            return data;\n        }\n        /** @type {State} */ function data(code) {\n            if (atBreak(code)) {\n                effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data);\n                return text(code);\n            }\n            // Data.\n            effects.consume(code);\n            return data;\n        }\n        /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */ function atBreak(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n                return true;\n            }\n            const list = constructs[code];\n            let index = -1;\n            if (list) {\n                // Always populated by defaults.\n                (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(Array.isArray(list), \"expected `disable.null` to be populated\");\n                while(++index < list.length){\n                    const item = list[index];\n                    if (!item.previous || item.previous.call(self, self.previous)) {\n                        return true;\n                    }\n                }\n            }\n            return false;\n        }\n    }\n}\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */ function createResolver(extraResolver) {\n    return resolveAllText;\n    /** @type {Resolver} */ function resolveAllText(events, context) {\n        let index = -1;\n        /** @type {number | undefined} */ let enter;\n        // A rather boring computation (to merge adjacent `data` events) which\n        // improves mm performance by 29%.\n        while(++index <= events.length){\n            if (enter === undefined) {\n                if (events[index] && events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n                    enter = index;\n                    index++;\n                }\n            } else if (!events[index] || events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n                // Don’t do anything if there is one data token.\n                if (index !== enter + 2) {\n                    events[enter][1].end = events[index - 1][1].end;\n                    events.splice(enter + 2, index - enter - 2);\n                    index = enter + 2;\n                }\n                enter = undefined;\n            }\n        }\n        return extraResolver ? extraResolver(events, context) : events;\n    }\n}\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */ function resolveAllLineSuffixes(events, context) {\n    let eventIndex = 0 // Skip first.\n    ;\n    while(++eventIndex <= events.length){\n        if ((eventIndex === events.length || events[eventIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding) && events[eventIndex - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n            const data = events[eventIndex - 1][1];\n            const chunks = context.sliceStream(data);\n            let index = chunks.length;\n            let bufferIndex = -1;\n            let size = 0;\n            /** @type {boolean | undefined} */ let tabs;\n            while(index--){\n                const chunk = chunks[index];\n                if (typeof chunk === \"string\") {\n                    bufferIndex = chunk.length;\n                    while(chunk.charCodeAt(bufferIndex - 1) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space){\n                        size++;\n                        bufferIndex--;\n                    }\n                    if (bufferIndex) break;\n                    bufferIndex = -1;\n                } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab) {\n                    tabs = true;\n                    size++;\n                } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace) {\n                // Empty\n                } else {\n                    // Replacement character, exit.\n                    index++;\n                    break;\n                }\n            }\n            // Allow final trailing whitespace.\n            if (context._contentTypeTextTrailing && eventIndex === events.length) {\n                size = 0;\n            }\n            if (size) {\n                const token = {\n                    type: eventIndex === events.length || tabs || size < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.hardBreakPrefixSizeMin ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineSuffix : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.hardBreakTrailing,\n                    start: {\n                        _bufferIndex: index ? bufferIndex : data.start._bufferIndex + bufferIndex,\n                        _index: data.start._index + index,\n                        line: data.end.line,\n                        column: data.end.column - size,\n                        offset: data.end.offset - size\n                    },\n                    end: {\n                        ...data.end\n                    }\n                };\n                data.end = {\n                    ...token.start\n                };\n                if (data.start.offset === data.end.offset) {\n                    Object.assign(data, token);\n                } else {\n                    events.splice(eventIndex, 0, [\n                        \"enter\",\n                        token,\n                        context\n                    ], [\n                        \"exit\",\n                        token,\n                        context\n                    ]);\n                    eventIndex += 2;\n                }\n            }\n            eventIndex++;\n        }\n    }\n    return events;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark/dev/lib/initialize/text.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark/dev/lib/parse.js":
/*!**************************************************!*\
  !*** ../node_modules/micromark/dev/lib/parse.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-combine-extensions */ \"(rsc)/../node_modules/micromark-util-combine-extensions/index.js\");\n/* harmony import */ var _initialize_content_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initialize/content.js */ \"(rsc)/../node_modules/micromark/dev/lib/initialize/content.js\");\n/* harmony import */ var _initialize_document_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initialize/document.js */ \"(rsc)/../node_modules/micromark/dev/lib/initialize/document.js\");\n/* harmony import */ var _initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./initialize/flow.js */ \"(rsc)/../node_modules/micromark/dev/lib/initialize/flow.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./initialize/text.js */ \"(rsc)/../node_modules/micromark/dev/lib/initialize/text.js\");\n/* harmony import */ var _constructs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constructs.js */ \"(rsc)/../node_modules/micromark/dev/lib/constructs.js\");\n/* harmony import */ var _create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./create-tokenizer.js */ \"(rsc)/../node_modules/micromark/dev/lib/create-tokenizer.js\");\n/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */ \n\n\n\n\n\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */ function parse(options) {\n    const settings = options || {};\n    const constructs = /** @type {FullNormalizedExtension} */ (0,micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__.combineExtensions)([\n        _constructs_js__WEBPACK_IMPORTED_MODULE_1__,\n        ...settings.extensions || []\n    ]);\n    /** @type {ParseContext} */ const parser = {\n        constructs,\n        content: create(_initialize_content_js__WEBPACK_IMPORTED_MODULE_2__.content),\n        defined: [],\n        document: create(_initialize_document_js__WEBPACK_IMPORTED_MODULE_3__.document),\n        flow: create(_initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__.flow),\n        lazy: {},\n        string: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.string),\n        text: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.text)\n    };\n    return parser;\n    /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */ function create(initial) {\n        return creator;\n        /** @type {Create} */ function creator(from) {\n            return (0,_create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__.createTokenizer)(parser, initial, from);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark/dev/lib/parse.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark/dev/lib/postprocess.js":
/*!********************************************************!*\
  !*** ../node_modules/micromark/dev/lib/postprocess.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   postprocess: () => (/* binding */ postprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-subtokenize */ \"(rsc)/../node_modules/micromark-util-subtokenize/dev/index.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */ \n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */ function postprocess(events) {\n    while(!(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__.subtokenize)(events)){\n    // Empty\n    }\n    return events;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kZXYvbGliL3Bvc3Rwcm9jZXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7O0NBRUMsR0FFcUQ7QUFFdEQ7Ozs7O0NBS0MsR0FDTSxTQUFTQyxZQUFZQyxNQUFNO0lBQ2hDLE1BQU8sQ0FBQ0YsdUVBQVdBLENBQUNFLFFBQVM7SUFDM0IsUUFBUTtJQUNWO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kZXYvbGliL3Bvc3Rwcm9jZXNzLmpzPzBiNDMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtFdmVudH0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuaW1wb3J0IHtzdWJ0b2tlbml6ZX0gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3VidG9rZW5pemUnXG5cbi8qKlxuICogQHBhcmFtIHtBcnJheTxFdmVudD59IGV2ZW50c1xuICogICBFdmVudHMuXG4gKiBAcmV0dXJucyB7QXJyYXk8RXZlbnQ+fVxuICogICBFdmVudHMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwb3N0cHJvY2VzcyhldmVudHMpIHtcbiAgd2hpbGUgKCFzdWJ0b2tlbml6ZShldmVudHMpKSB7XG4gICAgLy8gRW1wdHlcbiAgfVxuXG4gIHJldHVybiBldmVudHNcbn1cbiJdLCJuYW1lcyI6WyJzdWJ0b2tlbml6ZSIsInBvc3Rwcm9jZXNzIiwiZXZlbnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark/dev/lib/postprocess.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark/dev/lib/preprocess.js":
/*!*******************************************************!*\
  !*** ../node_modules/micromark/dev/lib/preprocess.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   preprocess: () => (/* binding */ preprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */ /**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */ \nconst search = /[\\0\\t\\n\\r]/g;\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */ function preprocess() {\n    let column = 1;\n    let buffer = \"\";\n    /** @type {boolean | undefined} */ let start = true;\n    /** @type {boolean | undefined} */ let atCarriageReturn;\n    return preprocessor;\n    /** @type {Preprocessor} */ // eslint-disable-next-line complexity\n    function preprocessor(value, encoding, end) {\n        /** @type {Array<Chunk>} */ const chunks = [];\n        /** @type {RegExpMatchArray | null} */ let match;\n        /** @type {number} */ let next;\n        /** @type {number} */ let startPosition;\n        /** @type {number} */ let endPosition;\n        /** @type {Code} */ let code;\n        value = buffer + (typeof value === \"string\" ? value.toString() : new TextDecoder(encoding || undefined).decode(value));\n        startPosition = 0;\n        buffer = \"\";\n        if (start) {\n            // To do: `markdown-rs` actually parses BOMs (byte order mark).\n            if (value.charCodeAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.byteOrderMarker) {\n                startPosition++;\n            }\n            start = undefined;\n        }\n        while(startPosition < value.length){\n            search.lastIndex = startPosition;\n            match = search.exec(value);\n            endPosition = match && match.index !== undefined ? match.index : value.length;\n            code = value.charCodeAt(endPosition);\n            if (!match) {\n                buffer = value.slice(startPosition);\n                break;\n            }\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf && startPosition === endPosition && atCarriageReturn) {\n                chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed);\n                atCarriageReturn = undefined;\n            } else {\n                if (atCarriageReturn) {\n                    chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn);\n                    atCarriageReturn = undefined;\n                }\n                if (startPosition < endPosition) {\n                    chunks.push(value.slice(startPosition, endPosition));\n                    column += endPosition - startPosition;\n                }\n                switch(code){\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.nul:\n                        {\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.replacementCharacter);\n                            column++;\n                            break;\n                        }\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ht:\n                        {\n                            next = Math.ceil(column / micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize) * micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize;\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab);\n                            while(column++ < next)chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace);\n                            break;\n                        }\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf:\n                        {\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed);\n                            column = 1;\n                            break;\n                        }\n                    default:\n                        {\n                            atCarriageReturn = true;\n                            column = 1;\n                        }\n                }\n            }\n            startPosition = endPosition + 1;\n        }\n        if (end) {\n            if (atCarriageReturn) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn);\n            if (buffer) chunks.push(buffer);\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof);\n        }\n        return chunks;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark/dev/lib/preprocess.js\n");

/***/ })

};
;
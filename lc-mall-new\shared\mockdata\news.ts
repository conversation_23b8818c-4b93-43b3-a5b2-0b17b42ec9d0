// shared/mockdata/news.ts
import { NewsArticle, newsCategories } from '@/shared/types/News';
import { v4 as uuidv4 } from 'uuid';

// Shared mock news database - centralized data source
export let mockNewsDb: NewsArticle[] = Array.from({ length: 25 }, (_, i) => ({
  id: `mock-id-${i + 1}`, // Use consistent IDs
  slug: `sample-news-${i + 1}`,
  title: `示例新闻标题 ${i + 1}`,
  summary: `这是新闻 ${i + 1} 的简短摘要，介绍了龙驰新材料在聚氨酯领域的最新发展和技术突破...`,
  content: `这是新闻 #${i + 1} 的**详细内容**。它支持 Markdown 格式。\n\n## 子标题\n\n- 列表项1\n- 列表项2\n\n![示例图片](/images/stock/news-placeholder-1.jpg "图片标题")`,
  coverImage: `/images/stock/news-placeholder-${(i % 5) + 1}.jpg`,
  author: '龙驰AI助手',
  publishedAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
  createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
  updatedAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
  category: newsCategories[i % newsCategories.length],
  status: 'published',
  views: Math.floor(Math.random() * 1000),
  isFeatured: i < 3,
  aiGenerated: true,
}));

// Helper function to find article by ID or slug
export function findArticle(idOrSlug: string): NewsArticle | undefined {
  return mockNewsDb.find(article => article.id === idOrSlug || article.slug === idOrSlug);
}

// Helper function to update article views
export function incrementViews(idOrSlug: string): void {
  const article = findArticle(idOrSlug);
  if (article) {
    article.views = (article.views || 0) + 1;
  }
}

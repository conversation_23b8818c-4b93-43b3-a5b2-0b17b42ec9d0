#!/bin/bash
# 远程服务器诊断脚本
# 请将此脚本上传到云服务器并执行

echo "=========================================="
echo "    龙驰商城 - 远程服务器诊断工具"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_service() {
    local service_name=$1
    local port=$2
    local url=$3
    
    echo -e "${BLUE}[检查] $service_name${NC}"
    
    # 检查端口监听
    if netstat -tlnp 2>/dev/null | grep ":$port " > /dev/null; then
        echo -e "${GREEN}✓ 端口 $port 正在监听${NC}"
        
        # 检查HTTP响应
        if curl -s --connect-timeout 5 "$url" > /dev/null; then
            echo -e "${GREEN}✓ HTTP响应正常${NC}"
        else
            echo -e "${RED}✗ HTTP响应异常${NC}"
            echo "  尝试访问: $url"
        fi
    else
        echo -e "${RED}✗ 端口 $port 未监听${NC}"
    fi
    echo ""
}

# 1. 系统信息
echo -e "${BLUE}=== 系统信息 ===${NC}"
echo "操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "当前时间: $(date)"
echo "系统负载: $(uptime)"
echo "内存使用: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo ""

# 2. Node.js环境
echo -e "${BLUE}=== Node.js环境 ===${NC}"
if command -v node > /dev/null; then
    echo "Node.js版本: $(node -v)"
else
    echo -e "${RED}✗ Node.js未安装${NC}"
fi

if command -v npm > /dev/null; then
    echo "NPM版本: $(npm -v)"
else
    echo -e "${RED}✗ NPM未安装${NC}"
fi
echo ""

# 3. 检查进程
echo -e "${BLUE}=== 进程检查 ===${NC}"
echo "Node.js相关进程:"
ps aux | grep -E "(node|npm)" | grep -v grep | while read line; do
    echo "  $line"
done
echo ""

# 4. 检查端口
echo -e "${BLUE}=== 端口检查 ===${NC}"
echo "关键端口监听状态:"
for port in 3000 5000 6379 80; do
    if netstat -tlnp 2>/dev/null | grep ":$port " > /dev/null; then
        echo -e "${GREEN}✓ 端口 $port 正在监听${NC}"
        netstat -tlnp 2>/dev/null | grep ":$port " | head -1
    else
        echo -e "${RED}✗ 端口 $port 未监听${NC}"
    fi
done
echo ""

# 5. 检查服务响应
echo -e "${BLUE}=== 服务响应检查 ===${NC}"
check_service "后端服务" "5000" "http://localhost:5000/health"
check_service "前端服务" "3000" "http://localhost:3000"

# 6. 检查日志文件
echo -e "${BLUE}=== 日志文件检查 ===${NC}"
if [ -d "logs" ]; then
    echo "日志目录存在:"
    ls -la logs/ | grep -E "\.(log|pid)$" | while read line; do
        echo "  $line"
    done
    
    echo ""
    echo "最近的错误日志:"
    if [ -f "logs/backend.log" ]; then
        echo -e "${YELLOW}后端错误:${NC}"
        tail -10 logs/backend.log | grep -i error || echo "  无错误日志"
    fi
    
    if [ -f "logs/frontend.log" ]; then
        echo -e "${YELLOW}前端错误:${NC}"
        tail -10 logs/frontend.log | grep -i error || echo "  无错误日志"
    fi
else
    echo -e "${RED}✗ logs目录不存在${NC}"
fi
echo ""

# 7. 检查配置文件
echo -e "${BLUE}=== 配置文件检查 ===${NC}"
if [ -f ".env.production" ]; then
    echo -e "${GREEN}✓ .env.production 存在${NC}"
    echo "关键配置项:"
    grep -E "(NODE_ENV|PORT|NEXT_PUBLIC_API_URL)" .env.production | sed 's/^/  /'
else
    echo -e "${RED}✗ .env.production 不存在${NC}"
fi
echo ""

# 8. Nginx检查
echo -e "${BLUE}=== Nginx检查 ===${NC}"
if command -v nginx > /dev/null; then
    echo -e "${GREEN}✓ Nginx已安装${NC}"
    echo "Nginx版本: $(nginx -v 2>&1)"
    
    if systemctl is-active nginx > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Nginx服务运行中${NC}"
    else
        echo -e "${RED}✗ Nginx服务未运行${NC}"
    fi
    
    # 检查配置
    if nginx -t > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Nginx配置语法正确${NC}"
    else
        echo -e "${RED}✗ Nginx配置有误${NC}"
        nginx -t
    fi
else
    echo -e "${RED}✗ Nginx未安装${NC}"
fi
echo ""

# 9. Redis检查
echo -e "${BLUE}=== Redis检查 ===${NC}"
if command -v redis-cli > /dev/null; then
    echo -e "${GREEN}✓ Redis客户端已安装${NC}"
    
    if redis-cli ping 2>/dev/null | grep PONG > /dev/null; then
        echo -e "${GREEN}✓ Redis服务响应正常${NC}"
    else
        echo -e "${RED}✗ Redis服务无响应${NC}"
    fi
else
    echo -e "${RED}✗ Redis客户端未安装${NC}"
fi
echo ""

# 10. API测试
echo -e "${BLUE}=== API接口测试 ===${NC}"
echo "测试API接口..."

# 后端API测试
echo -e "${YELLOW}后端API测试:${NC}"
for api in "/health" "/api/products" "/api/news"; do
    url="http://localhost:5000$api"
    if curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo -e "${GREEN}✓ $api${NC}"
    else
        echo -e "${RED}✗ $api${NC}"
    fi
done

# 前端API测试
echo -e "${YELLOW}前端API测试:${NC}"
for api in "/" "/api/videos" "/api/admin/stats"; do
    url="http://localhost:3000$api"
    if curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo -e "${GREEN}✓ $api${NC}"
    else
        echo -e "${RED}✗ $api${NC}"
    fi
done
echo ""

# 11. 建议操作
echo -e "${BLUE}=== 诊断完成 - 建议操作 ===${NC}"
echo "根据以上检查结果，如果发现问题，可以尝试以下操作："
echo ""
echo "🔄 重启服务:"
echo "  ./stop.sh && ./deploy.sh"
echo ""
echo "🔧 查看详细日志:"
echo "  tail -f logs/backend.log"
echo "  tail -f logs/frontend.log"
echo ""
echo "🌐 测试网络连接:"
echo "  curl -I http://gdlongchi.cn"
echo "  curl -I http://gdlongchi.cn:3000"
echo "  curl -I http://gdlongchi.cn:5000"
echo ""
echo "📊 检查系统资源:"
echo "  df -h  # 磁盘空间"
echo "  free -h  # 内存使用"
echo "  top  # 进程监控"
echo ""

echo "=========================================="
echo "诊断完成！请将结果截图或复制给开发者。"
echo "=========================================="

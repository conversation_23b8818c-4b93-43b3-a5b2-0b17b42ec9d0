/**
 * 调试启动脚本
 * 用于诊断服务器启动问题
 */

console.log('🔍 开始调试服务器启动...');

try {
  // 1. 测试基本模块加载
  console.log('1️⃣ 测试基本模块加载...');
  const path = require('path');
  const express = require('express');
  console.log('✅ 基本模块加载成功');

  // 2. 测试环境变量加载
  console.log('2️⃣ 测试环境变量加载...');
  const envFile = process.env.NODE_ENV === 'production'
    ? path.resolve(__dirname, '../.env.production')
    : path.resolve(__dirname, '../.env');
  
  console.log('环境文件路径:', envFile);
  require('dotenv').config({ path: envFile });
  console.log('✅ 环境变量加载成功');
  console.log('PORT:', process.env.PORT);
  console.log('REDIS_HOST:', process.env.REDIS_HOST);

  // 3. 测试日志模块
  console.log('3️⃣ 测试日志模块...');
  const logger = require('./src/utils/logger');
  logger.info('日志模块测试');
  console.log('✅ 日志模块加载成功');

  // 4. 测试Redis服务
  console.log('4️⃣ 测试Redis服务...');
  const redisService = require('./src/services/redisService');
  console.log('✅ Redis服务模块加载成功');

  // 5. 测试产品服务
  console.log('5️⃣ 测试产品服务...');
  const ProductService = require('./src/services/productService');
  const productService = new ProductService();
  console.log('✅ 产品服务加载成功');

  // 6. 测试产品控制器
  console.log('6️⃣ 测试产品控制器...');
  const productController = require('./src/controllers/productController');
  console.log('✅ 产品控制器加载成功');

  // 7. 测试路由
  console.log('7️⃣ 测试路由...');
  const productRoutes = require('./src/routes/products');
  console.log('✅ 产品路由加载成功');

  // 8. 测试应用
  console.log('8️⃣ 测试应用加载...');
  const app = require('./src/app');
  console.log('✅ 应用加载成功');

  // 9. 启动简单服务器
  console.log('9️⃣ 启动简单服务器...');
  const PORT = process.env.PORT || 5000;
  const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ 服务器启动成功: http://localhost:${PORT}`);
    console.log('🎉 所有测试通过！');
  });

  // 错误处理
  server.on('error', (error) => {
    console.error('❌ 服务器启动错误:', error);
  });

  // 优雅关闭
  process.on('SIGINT', () => {
    console.log('\n🛑 收到关闭信号，正在关闭服务器...');
    server.close(() => {
      console.log('✅ 服务器已关闭');
      process.exit(0);
    });
  });

} catch (error) {
  console.error('❌ 启动过程中发生错误:', error);
  console.error('错误堆栈:', error.stack);
  process.exit(1);
}

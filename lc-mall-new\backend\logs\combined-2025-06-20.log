{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 08:27:04"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 08:27:04"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 08:27:04"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 08:27:04"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 08:27:04"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 08:27:04"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 08:27:04"}
{"environment":"development","level":"debug","message":"获取文章列表成功，共 8 篇文章","service":"lc-mall-backend","timestamp":"2025-06-20 08:29:28"}
{"environment":"development","level":"debug","message":"API Response 获取新闻列表成功","requestId":"mc42okjlvjti2atb18","responseSize":12777,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:29:28"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:29:28 +0000] \"GET /api/news?page=1&limit=10 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:29:28"}
{"environment":"development","level":"debug","message":"获取文章列表成功，共 8 篇文章","service":"lc-mall-backend","timestamp":"2025-06-20 08:29:28"}
{"environment":"development","level":"debug","message":"API Response 获取新闻列表成功","requestId":"mc42okjtvkogwd3uswk","responseSize":12778,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:29:28"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:29:28 +0000] \"GET /api/news?page=1&limit=10 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:29:28"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 08:29:44"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc42ox8jyx41ju02bo","responseSize":34728,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:29:44"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:29:44 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:29:44"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 08:29:44"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc42ox8s2r5syefffc8","responseSize":34729,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:29:44"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:29:44 +0000] \"GET /api/products?limit=50 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:29:44"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:29:44 +0000] \"GET /images/products/WL-5201.jpg HTTP/1.1\" 404 90 \"http://localhost:3000/products/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:29:44"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:05"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc42pd8cy9x9a3oqg6","responseSize":1378,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:05"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:05 +0000] \"GET /api/products?page=1&limit=1 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:05"}
{"environment":"development","level":"debug","message":"获取文章列表成功，共 8 篇文章","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:05"}
{"environment":"development","level":"debug","message":"API Response 获取新闻列表成功","requestId":"mc42pd8o6szc9c7jjai","responseSize":12779,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:05"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:05 +0000] \"GET /api/news?page=1&limit=999 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:05"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:08"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc42pfpreycgnca6int","responseSize":10651,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:08"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:08 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:08"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:08"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc42pfq9dv0897t7iav","responseSize":10651,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:08"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:08 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:08"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:08 +0000] \"GET /images/products/WL-5201.jpg HTTP/1.1\" 404 90 \"http://localhost:3000/admin/products/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:08"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:14 +0000] \"POST /api/admin/upload/image HTTP/1.1\" 200 223 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:14"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:14 +0000] \"GET /images/products/product-1750379414137-524098463.jpg HTTP/1.1\" 200 431408 \"http://localhost:3000/admin/products/create/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:14"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:15"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc42pl7wkv6qipkd48","responseSize":10650,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:15"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:15 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:15"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:15"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc42pl89hke89yf1x38","responseSize":10651,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:15"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:15 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:15"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:16 +0000] \"GET /images/products/WL-5201.jpg HTTP/1.1\" 404 90 \"http://localhost:3000/admin/products/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:16"}
{"environment":"development","level":"debug","message":"获取文章列表成功，共 8 篇文章","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:19"}
{"environment":"development","level":"debug","message":"API Response 获取新闻列表成功","requestId":"mc42pntlkiguxnmiew","responseSize":12777,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:19"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:19 +0000] \"GET /api/news?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:19"}
{"environment":"development","level":"debug","message":"获取文章列表成功，共 8 篇文章","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:19"}
{"environment":"development","level":"debug","message":"API Response 获取新闻列表成功","requestId":"mc42pntyedji7pn7k5p","responseSize":12778,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:19"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:19 +0000] \"GET /api/news?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:19"}
{"environment":"development","level":"debug","message":"API Response 获取主题配置成功","requestId":"mc42po13kwofw132m4","responseSize":1028,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:19"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:19 +0000] \"GET /api/news/topics HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:19"}
{"environment":"development","level":"info","message":"DeepSeek API调用成功","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:22"}
{"environment":"development","level":"debug","message":"API Response 获取AI状态成功","requestId":"mc42pqafkqv9e7awqj9","responseSize":183,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 08:30:22"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:00:30:22 +0000] \"GET /api/news/ai-status HTTP/1.1\" 200 203 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:22"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 08:30:41"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 16:04:06"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 16:04:26"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"sample-001","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"sample-002","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"0","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"1","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"3","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"4","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"5","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"6","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"7","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"8","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"9","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"10","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"11","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"12","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"13","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"14","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"15","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"16","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"17","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"18","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"19","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"20","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"21","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"22","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"31","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"32","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"33","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"34","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"35","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"36","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"37","service":"lc-mall-backend","timestamp":"2025-06-20 16:06:51"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"environment":"development","level":"info","message":"Initialized product list key as SET","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"environment":"development","level":"info","message":"Product created successfully","name":"TDI-80 聚氨酯原料","productId":"sample-001","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"environment":"development","level":"info","message":"Product created successfully","name":"MDI 聚氨酯原料","productId":"sample-002","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"environment":"development","level":"info","message":"Product created successfully","name":"DBTDL 催化剂","productId":"sample-003","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"count":3,"environment":"development","level":"info","message":"Sample products created successfully","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"environment":"development","level":"info","message":"Product created successfully","name":"产品样品 50g","productId":"0","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"environment":"development","level":"info","message":"Product created successfully","name":"潜固化剂 WL-1031","productId":"1","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"environment":"development","level":"info","message":"Product created successfully","name":"潜固化剂 WL-104","productId":"3","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:20"}
{"environment":"development","level":"info","message":"Product created successfully","name":"潜固化剂 WL-101","productId":"4","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"生物基树脂LC-450","productId":"5","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"生物基树脂LC-320","productId":"6","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"催化剂WL-11","productId":"7","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"产品样品 50g","productId":"8","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"潜固化剂 WL-102","productId":"9","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"生物基树脂LC-170","productId":"10","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"生物基树脂LC-140","productId":"11","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"附着力促进剂 WL-469","productId":"12","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"附着力促进剂 WL-480","productId":"13","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:21"}
{"environment":"development","level":"info","message":"Product created successfully","name":"附着力促进剂 WL-470","productId":"14","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:22"}
{"environment":"development","level":"info","message":"Product created successfully","name":"流平剂 LC-310","productId":"15","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:22"}
{"environment":"development","level":"info","message":"Product created successfully","name":"流平剂 LC-312","productId":"16","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:22"}
{"environment":"development","level":"info","message":"Product created successfully","name":"分散剂WL-201","productId":"17","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:22"}
{"environment":"development","level":"info","message":"Product created successfully","name":"润湿分散剂 WL-204A","productId":"18","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:22"}
{"environment":"development","level":"info","message":"Product created successfully","name":"降粘分散剂 WL-210","productId":"19","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:22"}
{"environment":"development","level":"info","message":"Product created successfully","name":"分散剂 WL-244","productId":"20","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:22"}
{"environment":"development","level":"info","message":"Product created successfully","name":"消泡剂 WL-300","productId":"21","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:22"}
{"environment":"development","level":"info","message":"Product created successfully","name":"消泡剂 WL-301","productId":"22","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:22"}
{"environment":"development","level":"info","message":"Product created successfully","name":"消泡剂 WL-319","productId":"31","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:23"}
{"environment":"development","level":"info","message":"Product created successfully","name":"油性消泡剂 WL-3066","productId":"32","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:23"}
{"environment":"development","level":"info","message":"Product created successfully","name":"防变剂 WL-5410","productId":"33","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:23"}
{"environment":"development","level":"info","message":"Product created successfully","name":"防沉触变剂 WL-5201","productId":"34","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:23"}
{"environment":"development","level":"info","message":"Product created successfully","name":"附着力促进剂 WL-469","productId":"35","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:23"}
{"environment":"development","level":"info","message":"Product created successfully","name":"附着力促进剂 WL-470","productId":"36","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:23"}
{"environment":"development","level":"info","message":"Product created successfully","name":"附着力促进剂 WL-480","productId":"37","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:23"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"sample-001","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"sample-002","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"sample-003","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"0","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"1","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"3","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"4","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"5","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"6","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"7","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"8","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"9","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"10","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"11","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"12","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"13","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"14","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"15","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"16","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"17","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"18","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"19","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"20","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"21","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"22","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"31","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"32","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"33","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"34","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"35","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"36","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"development","level":"info","message":"Product deleted successfully","productId":"37","service":"lc-mall-backend","timestamp":"2025-06-20 16:09:37"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 19:42:50"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 20:42:02"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 20:43:32"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 20:54:00"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 20:54:43"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 20:56:07"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 20:56:48"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 20:59:34"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:01:38"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:03:20"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:15"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:24"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:24"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:24"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:24"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:24"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:24"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:24"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:24"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:31"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:31"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:31"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:31"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:31"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:31"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:31"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:32"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:40"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:40"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:40"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:40"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:40"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:40"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:40"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:57"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:57"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:57"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:57"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:57"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:57"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:04:57"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:12"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:12"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:12"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:12"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:12"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:12"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:12"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:25"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:34"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:43"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:46"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:05:54"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:03"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:11"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:11"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:11"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:11"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:11"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:11"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:11"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:52"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:52"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:52"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:52"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:52"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:52"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:52"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:57"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:57"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:57"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:57"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:57"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:57"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:06:57"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:22"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:23"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:40"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:46"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:07:49"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:08:22"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:08:30"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:08:39"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:08:48"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:09:15"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:12:53"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:13:31"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:13:31"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:13:31"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:13:31"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:13:31"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:13:31"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:13:31"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:13:31"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:17:33"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:23:39"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:24:27"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:29:12"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:29:43"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:30:13"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:31:06"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:31:06"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:31:06"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:31:06"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:31:06"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:31:06"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:31:06"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:31:06"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:31:52"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:03"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:03"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:03"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:03"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:03"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:03"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:03"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:03"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:10"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:10"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:10"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:10"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:10"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:10"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:10"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:11"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:20"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:20"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:20"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:20"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:20"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:20"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:20"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:21"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:29"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:29"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:29"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:29"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:29"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:29"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:29"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:29"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:36"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:36"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:36"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:36"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:36"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:36"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:36"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:36"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:47"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:47"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:47"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:47"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:47"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:47"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:47"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:47"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:57"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:57"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:57"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:57"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:57"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:57"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:57"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:32:57"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:15"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:15"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:15"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:15"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:15"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:15"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:15"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:15"}
{"environment":"production","level":"info","message":"Initialized product list key as SET","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"info","message":"Product created successfully","name":"TDI-80 聚氨酯原料","productId":"sample-001","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"info","message":"Product created successfully","name":"MDI 聚氨酯原料","productId":"sample-002","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"info","message":"Product created successfully","name":"DBTDL 催化剂","productId":"sample-003","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:49"}
{"count":3,"environment":"production","level":"info","message":"Sample products created successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc4up90u4p2lyxpivtb","responseSize":1198,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [20/Jun/2025:13:33:49 +0000] \"GET /api/products?page=1&limit=1 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"debug","message":"获取文章列表成功，共 8 篇文章","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"debug","message":"API Response 获取新闻列表成功","requestId":"mc4up99eu8kkjd3eva","responseSize":12778,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"info","message":"127.0.0.1 - - [20/Jun/2025:13:33:49 +0000] \"GET /api/news?page=1&limit=999 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:49"}
{"environment":"production","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:33:58"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:34:01"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:34:01"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:34:01"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:34:01"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:34:01"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:34:01"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:34:01"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:34:02"}
{"environment":"production","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:37:55"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:37:59"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:37:59"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:37:59"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:37:59"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:37:59"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:37:59"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:37:59"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:38:00"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:11"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:11"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:11"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:11"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:11"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:11"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:11"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:12"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:40"}
{"environment":"production","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:44"}
{"environment":"production","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:48"}
{"environment":"production","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:48"}
{"environment":"production","level":"info","message":"每日文章生成定时任务已启动（每天上午8点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:48"}
{"environment":"production","level":"info","message":"Analytics数据清理定时任务已启动（每天凌晨2点执行）","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:48"}
{"environment":"production","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:48"}
{"environment":"production","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:48"}
{"environment":"production","level":"info","message":"Server running on http://0.0.0.0:5000 in production mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:48"}
{"environment":"production","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:40:48"}
{"environment":"production","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:11"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:26"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:26"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:26"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:26"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:26"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:26"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:26"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:26"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:31"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc4uz5jat8vayqec6i","responseSize":1197,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 21:41:31"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:41:31 +0000] \"GET /api/products?page=1&limit=1 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:31"}
{"environment":"development","level":"debug","message":"获取文章列表成功，共 8 篇文章","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:31"}
{"environment":"development","level":"debug","message":"API Response 获取新闻列表成功","requestId":"mc4uz5jrt4iqwyg6y0o","responseSize":12779,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 21:41:31"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:41:31 +0000] \"GET /api/news?page=1&limit=999 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:41:31"}
{"environment":"development","level":"info","message":"Analytics record saved: d4ce292b-67f6-4439-a9d8-65f2be168411","service":"lc-mall-backend","timestamp":"2025-06-20 21:45:16"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:45:16 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:45:16"}
{"environment":"development","error":"User not authenticated","ip":"::1","level":"warn","message":"API Error Response 未认证","requestId":"mc4v45tbqgj1aafalb","service":"lc-mall-backend","statusCode":401,"timestamp":"2025-06-20 21:45:25","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:45:25 +0000] \"GET /api/analytics/admin/stats HTTP/1.1\" 401 144 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:45:25"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:14"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:14"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:14"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:14"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:14"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:14"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:15"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:24"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:28"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:28"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:28"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:28"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:28"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:28"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:28"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:28"}
{"environment":"development","level":"info","message":"Analytics record saved: cb5ad86d-32e1-4a24-b2cb-323b28284e6c","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:57"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:48:57 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:57"}
{"environment":"development","level":"info","message":"Analytics record saved: b6235fc0-2c04-473f-9edd-de40560b338c","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:58"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:48:58 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:58"}
{"environment":"development","error":"User not authenticated","ip":"::1","level":"warn","message":"API Error Response 未认证","requestId":"mc4v8qfy8yrb5trd20f","service":"lc-mall-backend","statusCode":401,"timestamp":"2025-06-20 21:48:58","userAgent":"node"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:48:58 +0000] \"GET /api/analytics/admin/records?startDate=2025-06-13&endDate=2025-06-20&limit=50&offset=0 HTTP/1.1\" 401 145 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:58"}
{"environment":"development","error":"User not authenticated","ip":"::1","level":"warn","message":"API Error Response 未认证","requestId":"mc4v8qg6y0d7yuug5s","service":"lc-mall-backend","statusCode":401,"timestamp":"2025-06-20 21:48:58","userAgent":"node"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:48:58 +0000] \"GET /api/analytics/admin/stats?startDate=2025-06-13&endDate=2025-06-20 HTTP/1.1\" 401 144 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:48:58"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:50:24"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:50:24"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:50:24"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:50:24"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:50:24"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:50:24"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:50:25"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:50:25"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:04"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:07"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:07"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:07"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:07"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:07"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:07"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:07"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:08"}
{"environment":"development","level":"info","message":"Analytics record saved: f427b7ad-ff29-4b51-aea1-1e9c7b74bbe2","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:18"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:51:18 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:18"}
{"environment":"development","level":"info","message":"Analytics record saved: 7789b82b-7ecd-482d-864b-e2363fe36568","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:24"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:51:24 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:24"}
{"environment":"development","error":"User not authenticated","ip":"::1","level":"warn","message":"API Error Response 未认证","requestId":"mc4vbv4k57vh62d2wiv","service":"lc-mall-backend","statusCode":401,"timestamp":"2025-06-20 21:51:24","userAgent":"node"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:51:24 +0000] \"GET /api/analytics/admin/stats?startDate=2025-06-13&endDate=2025-06-20 HTTP/1.1\" 401 145 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:24"}
{"environment":"development","error":"User not authenticated","ip":"::1","level":"warn","message":"API Error Response 未认证","requestId":"mc4vbv6vkeo4vyrp21","service":"lc-mall-backend","statusCode":401,"timestamp":"2025-06-20 21:51:24","userAgent":"node"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:51:24 +0000] \"GET /api/analytics/admin/records?startDate=2025-06-13&endDate=2025-06-20&limit=50&offset=0 HTTP/1.1\" 401 144 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:51:24"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:03"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:03"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:03"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:03"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:03"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:03"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:03"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:04"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:10"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:10"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:10"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:11"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:11"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:11"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:11"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:11"}
{"environment":"development","level":"info","message":"Analytics record saved: b1b39e91-f9f2-4743-8065-f0053983208b","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:17 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"info","message":"Analytics record saved: c0f9e669-8b22-43d2-ba5d-d3db851f4ff3","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:17 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error finding analytics record by ID: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error getting analytics stats: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error getting analytics stats: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:17 +0000] \"GET /api/analytics/admin/stats?startDate=2025-06-13&endDate=2025-06-20 HTTP/1.1\" 500 102 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error finding analytics record by ID: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error finding analytics records by date range: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"error","message":"Error getting analytics records: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:17 +0000] \"GET /api/analytics/admin/records?startDate=2025-06-13&endDate=2025-06-20&limit=50&offset=0 HTTP/1.1\" 500 102 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:17"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:20"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:26"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:26"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:26"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:26"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:26"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:26"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:26"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:26"}
{"environment":"development","level":"info","message":"Analytics record saved: aaa3002b-5db5-4943-b974-19ea79c28d2d","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:35"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:35 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:35"}
{"environment":"development","level":"info","message":"Analytics record saved: 8be7832b-566e-4df6-bbb3-3a54112c5dc2","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:35"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:35 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:35"}
{"environment":"development","level":"error","message":"Error finding analytics record by ID: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error getting analytics stats: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error getting analytics stats: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:184:26)\n    at async getStats (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:155:21)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:37 +0000] \"GET /api/analytics/admin/stats?startDate=2025-06-13&endDate=2025-06-20 HTTP/1.1\" 500 102 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error finding analytics record by ID: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error finding analytics records by date range: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"error","message":"Error getting analytics records: \"[object Object]\" is not valid JSON","service":"lc-mall-backend","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at Analytics.findById (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:89:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Analytics.findByDateRange (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\Analytics.js:128:26)\n    at async getRecords (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\analyticsController.js:107:23)","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:37 +0000] \"GET /api/analytics/admin/records?startDate=2025-06-13&endDate=2025-06-20&limit=50&offset=0 HTTP/1.1\" 500 102 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"info","message":"Analytics record saved: 44989eba-ea80-4239-b839-75630da7de7d","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:37 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:37"}
{"environment":"development","level":"info","message":"Analytics record saved: 78453339-497b-45b8-b7d5-f5bc656ecd52","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:47"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:13:53:47 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 21:53:47"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:25"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:25"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:25"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:25"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:25"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:25"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:25"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:25"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:45"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:45"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:45"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:45"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:45"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:45"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:45"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:45"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:50"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:50"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:50"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:50"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:50"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:50"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:50"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:51"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 22:03:59"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:03"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:03"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:03"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:03"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:03"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:03"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:03"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:03"}
{"environment":"development","level":"info","message":"Analytics record saved: cea647c3-59fd-46c2-b311-a7f467b2a1cc","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:39"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:14:04:39 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:39"}
{"environment":"development","level":"info","message":"Analytics record saved: 0c2c32dd-554d-4267-b247-b453498aca62","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:40"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:14:04:40 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:40"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:14:04:40 +0000] \"GET /api/analytics/admin/records?startDate=2025-06-13&endDate=2025-06-20&limit=50&offset=0 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:40"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:14:04:40 +0000] \"GET /api/analytics/admin/stats?startDate=2025-06-13&endDate=2025-06-20 HTTP/1.1\" 200 412 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 22:04:40"}
{"environment":"development","level":"info","message":"Analytics record saved: ed3382b4-5e0b-47ab-b7cd-37db4fb35b63","service":"lc-mall-backend","timestamp":"2025-06-20 22:05:27"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:14:05:27 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 22:05:27"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 22:08:18"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-20 22:08:21"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:08:21"}
{"environment":"development","level":"info","message":"开发环境，每日文章生成定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:08:21"}
{"environment":"development","level":"info","message":"开发环境，Analytics数据清理定时任务未启动","service":"lc-mall-backend","timestamp":"2025-06-20 22:08:21"}
{"environment":"development","level":"info","message":"定时任务调度服务初始化成功","service":"lc-mall-backend","timestamp":"2025-06-20 22:08:21"}
{"environment":"development","level":"info","message":"Scheduler service initialized","service":"lc-mall-backend","timestamp":"2025-06-20 22:08:21"}
{"environment":"development","level":"info","message":"Server running on http://localhost:5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-20 22:08:21"}
{"environment":"development","level":"info","message":"Email service initialized successfully","service":"lc-mall-backend","timestamp":"2025-06-20 22:08:21"}
{"environment":"development","level":"info","message":"Analytics record saved: f5339f9d-af0d-4b47-8006-06b0360cbd48","service":"lc-mall-backend","timestamp":"2025-06-20 22:10:18"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:14:10:18 +0000] \"POST /api/analytics/track HTTP/1.1\" 201 103 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 22:10:18"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 22:10:18"}
{"environment":"development","level":"info","message":"Product data initialization completed","service":"lc-mall-backend","timestamp":"2025-06-20 22:10:18"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc4w0668t3p3fefjh6","responseSize":3235,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 22:10:18"}
{"environment":"development","level":"debug","message":"API Response 产品列表获取成功","requestId":"mc4w0669jzqvrcvd6g","responseSize":3235,"service":"lc-mall-backend","statusCode":200,"timestamp":"2025-06-20 22:10:18"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:14:10:18 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 22:10:18"}
{"environment":"development","level":"info","message":"::1 - - [20/Jun/2025:14:10:18 +0000] \"GET /api/products?page=1&limit=10 HTTP/1.1\" 200 - \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-20 22:10:18"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-20 22:11:05"}

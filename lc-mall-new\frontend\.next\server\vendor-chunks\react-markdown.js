"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown";
exports.ids = ["vendor-chunks/react-markdown"];
exports.modules = {

/***/ "(rsc)/../node_modules/react-markdown/lib/index.js":
/*!***************************************************!*\
  !*** ../node_modules/react-markdown/lib/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown),\n/* harmony export */   MarkdownAsync: () => (/* binding */ MarkdownAsync),\n/* harmony export */   MarkdownHooks: () => (/* binding */ MarkdownHooks),\n/* harmony export */   defaultUrlTransform: () => (/* binding */ defaultUrlTransform)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(rsc)/../node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var html_url_attributes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! html-url-attributes */ \"(rsc)/../node_modules/html-url-attributes/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-parse */ \"(rsc)/../node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-rehype */ \"(rsc)/../node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unified */ \"(rsc)/../node_modules/unified/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/../node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(rsc)/../node_modules/vfile/lib/index.js\");\n/**\n * @import {Element, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentType, JSX, ReactElement, ReactNode} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */ /**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */ /**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */ /**\n * @typedef {{\n *   [Key in keyof JSX.IntrinsicElements]?: ComponentType<JSX.IntrinsicElements[Key] & ExtraProps> | keyof JSX.IntrinsicElements\n * }} Components\n *   Map tag names to components.\n */ /**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */ /**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */ /**\n * @typedef HooksOptionsOnly\n *   Configuration specifically for {@linkcode MarkdownHooks}.\n * @property {ReactNode | null | undefined} [fallback]\n *   Content to render while the processor processing the markdown (optional).\n */ /**\n * @typedef {Options & HooksOptionsOnly} HooksOptions\n *   Configuration for {@linkcode MarkdownHooks};\n *   extends the regular {@linkcode Options} with a `fallback` prop.\n */ /**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */ \n\n\n\n\n\n\n\n\n\nconst changelog = \"https://github.com/remarkjs/react-markdown/blob/main/changelog.md\";\n/** @type {PluggableList} */ const emptyPlugins = [];\n/** @type {Readonly<RemarkRehypeOptions>} */ const emptyRemarkRehypeOptions = {\n    allowDangerousHtml: true\n};\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i;\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */ const deprecations = [\n    {\n        from: \"astPlugins\",\n        id: \"remove-buggy-html-in-markdown-parser\"\n    },\n    {\n        from: \"allowDangerousHtml\",\n        id: \"remove-buggy-html-in-markdown-parser\"\n    },\n    {\n        from: \"allowNode\",\n        id: \"replace-allownode-allowedtypes-and-disallowedtypes\",\n        to: \"allowElement\"\n    },\n    {\n        from: \"allowedTypes\",\n        id: \"replace-allownode-allowedtypes-and-disallowedtypes\",\n        to: \"allowedElements\"\n    },\n    {\n        from: \"className\",\n        id: \"remove-classname\"\n    },\n    {\n        from: \"disallowedTypes\",\n        id: \"replace-allownode-allowedtypes-and-disallowedtypes\",\n        to: \"disallowedElements\"\n    },\n    {\n        from: \"escapeHtml\",\n        id: \"remove-buggy-html-in-markdown-parser\"\n    },\n    {\n        from: \"includeElementIndex\",\n        id: \"#remove-includeelementindex\"\n    },\n    {\n        from: \"includeNodeIndex\",\n        id: \"change-includenodeindex-to-includeelementindex\"\n    },\n    {\n        from: \"linkTarget\",\n        id: \"remove-linktarget\"\n    },\n    {\n        from: \"plugins\",\n        id: \"change-plugins-to-remarkplugins\",\n        to: \"remarkPlugins\"\n    },\n    {\n        from: \"rawSourcePos\",\n        id: \"#remove-rawsourcepos\"\n    },\n    {\n        from: \"renderers\",\n        id: \"change-renderers-to-components\",\n        to: \"components\"\n    },\n    {\n        from: \"source\",\n        id: \"change-source-to-children\",\n        to: \"children\"\n    },\n    {\n        from: \"sourcePos\",\n        id: \"#remove-sourcepos\"\n    },\n    {\n        from: \"transformImageUri\",\n        id: \"#add-urltransform\",\n        to: \"urlTransform\"\n    },\n    {\n        from: \"transformLinkUri\",\n        id: \"#add-urltransform\",\n        to: \"urlTransform\"\n    }\n];\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */ function Markdown(options) {\n    const processor = createProcessor(options);\n    const file = createFile(options);\n    return post(processor.runSync(processor.parse(file), file), options);\n}\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */ async function MarkdownAsync(options) {\n    const processor = createProcessor(options);\n    const file = createFile(options);\n    const tree = await processor.run(processor.parse(file), file);\n    return post(tree, options);\n}\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<HooksOptions>} options\n *   Props.\n * @returns {ReactNode}\n *   React node.\n */ function MarkdownHooks(options) {\n    const processor = createProcessor(options);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(/** @type {Error | undefined} */ undefined);\n    const [tree, setTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(/** @type {Root | undefined} */ undefined);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        let cancelled = false;\n        const file = createFile(options);\n        processor.run(processor.parse(file), file, function(error, tree) {\n            if (!cancelled) {\n                setError(error);\n                setTree(tree);\n            }\n        });\n        /**\n       * @returns {undefined}\n       *   Nothing.\n       */ return function() {\n            cancelled = true;\n        };\n    }, [\n        options.children,\n        options.rehypePlugins,\n        options.remarkPlugins,\n        options.remarkRehypeOptions\n    ]);\n    if (error) throw error;\n    return tree ? post(tree, options) : options.fallback;\n}\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */ function createProcessor(options) {\n    const rehypePlugins = options.rehypePlugins || emptyPlugins;\n    const remarkPlugins = options.remarkPlugins || emptyPlugins;\n    const remarkRehypeOptions = options.remarkRehypeOptions ? {\n        ...options.remarkRehypeOptions,\n        ...emptyRemarkRehypeOptions\n    } : emptyRemarkRehypeOptions;\n    const processor = (0,unified__WEBPACK_IMPORTED_MODULE_2__.unified)().use(remark_parse__WEBPACK_IMPORTED_MODULE_3__[\"default\"]).use(remarkPlugins).use(remark_rehype__WEBPACK_IMPORTED_MODULE_4__[\"default\"], remarkRehypeOptions).use(rehypePlugins);\n    return processor;\n}\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */ function createFile(options) {\n    const children = options.children || \"\";\n    const file = new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile();\n    if (typeof children === \"string\") {\n        file.value = children;\n    } else {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\"Unexpected value `\" + children + \"` for `children` prop, expected `string`\");\n    }\n    return file;\n}\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */ function post(tree, options) {\n    const allowedElements = options.allowedElements;\n    const allowElement = options.allowElement;\n    const components = options.components;\n    const disallowedElements = options.disallowedElements;\n    const skipHtml = options.skipHtml;\n    const unwrapDisallowed = options.unwrapDisallowed;\n    const urlTransform = options.urlTransform || defaultUrlTransform;\n    for (const deprecation of deprecations){\n        if (Object.hasOwn(options, deprecation.from)) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\"Unexpected `\" + deprecation.from + \"` prop, \" + (deprecation.to ? \"use `\" + deprecation.to + \"` instead\" : \"remove it\") + \" (see <\" + changelog + \"#\" + deprecation.id + \"> for more info)\");\n        }\n    }\n    if (allowedElements && disallowedElements) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\"Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other\");\n    }\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_7__.visit)(tree, transform);\n    return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.toJsxRuntime)(tree, {\n        Fragment: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n        components,\n        ignoreInvalidStyle: true,\n        jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx,\n        jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs,\n        passKeys: true,\n        passNode: true\n    });\n    /** @type {BuildVisitor<Root>} */ function transform(node, index, parent) {\n        if (node.type === \"raw\" && parent && typeof index === \"number\") {\n            if (skipHtml) {\n                parent.children.splice(index, 1);\n            } else {\n                parent.children[index] = {\n                    type: \"text\",\n                    value: node.value\n                };\n            }\n            return index;\n        }\n        if (node.type === \"element\") {\n            /** @type {string} */ let key;\n            for(key in html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes){\n                if (Object.hasOwn(html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes, key) && Object.hasOwn(node.properties, key)) {\n                    const value = node.properties[key];\n                    const test = html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes[key];\n                    if (test === null || test.includes(node.tagName)) {\n                        node.properties[key] = urlTransform(String(value || \"\"), key, node);\n                    }\n                }\n            }\n        }\n        if (node.type === \"element\") {\n            let remove = allowedElements ? !allowedElements.includes(node.tagName) : disallowedElements ? disallowedElements.includes(node.tagName) : false;\n            if (!remove && allowElement && typeof index === \"number\") {\n                remove = !allowElement(node, index, parent);\n            }\n            if (remove && parent && typeof index === \"number\") {\n                if (unwrapDisallowed && node.children) {\n                    parent.children.splice(index, 1, ...node.children);\n                } else {\n                    parent.children.splice(index, 1);\n                }\n                return index;\n            }\n        }\n    }\n}\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */ function defaultUrlTransform(value) {\n    // Same as:\n    // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n    // But without the `encode` part.\n    const colon = value.indexOf(\":\");\n    const questionMark = value.indexOf(\"?\");\n    const numberSign = value.indexOf(\"#\");\n    const slash = value.indexOf(\"/\");\n    if (// If there is no protocol, it’s relative.\n    colon === -1 || // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    slash !== -1 && colon > slash || questionMark !== -1 && colon > questionMark || numberSign !== -1 && colon > numberSign || // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))) {\n        return value;\n    }\n    return \"\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/react-markdown/lib/index.js\n");

/***/ })

};
;
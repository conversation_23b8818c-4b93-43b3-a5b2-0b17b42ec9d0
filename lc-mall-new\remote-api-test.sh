#!/bin/bash
# 远程服务器API测试脚本
# 专门用于测试API接口响应

echo "=========================================="
echo "    龙驰商城 - API接口测试工具"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试函数
test_api() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    echo -n "测试 $name ... "
    
    # 获取HTTP状态码和响应时间
    response=$(curl -s -w "%{http_code}:%{time_total}" --connect-timeout 10 "$url" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        status_code=$(echo "$response" | tail -c 10 | cut -d':' -f1)
        time_total=$(echo "$response" | tail -c 10 | cut -d':' -f2)
        
        if [ "$status_code" = "$expected_status" ]; then
            echo -e "${GREEN}✓ ($status_code, ${time_total}s)${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠ (状态码: $status_code, ${time_total}s)${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ (连接失败)${NC}"
        return 1
    fi
}

# 测试带响应内容的API
test_api_with_content() {
    local name=$1
    local url=$2
    local search_text=$3
    
    echo -n "测试 $name ... "
    
    response=$(curl -s --connect-timeout 10 "$url" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$response" ]; then
        if echo "$response" | grep -q "$search_text"; then
            echo -e "${GREEN}✓ (响应正常)${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠ (响应内容异常)${NC}"
            echo "  响应预览: $(echo "$response" | head -c 100)..."
            return 1
        fi
    else
        echo -e "${RED}✗ (无响应)${NC}"
        return 1
    fi
}

echo "开始API接口测试..."
echo ""

# 1. 基础连通性测试
echo -e "${BLUE}=== 基础连通性测试 ===${NC}"
test_api "本地前端服务" "http://localhost:3000" "200"
test_api "本地后端服务" "http://localhost:5000/health" "200"
test_api "域名访问(HTTP)" "http://gdlongchi.cn" "200"
test_api "域名前端直接访问" "http://gdlongchi.cn:3000" "200"
test_api "域名后端直接访问" "http://gdlongchi.cn:5000/health" "200"
echo ""

# 2. 后端API测试
echo -e "${BLUE}=== 后端API接口测试 ===${NC}"
test_api "健康检查" "http://localhost:5000/health" "200"
test_api "产品API" "http://localhost:5000/api/products" "200"
test_api "新闻API" "http://localhost:5000/api/news" "200"
test_api "用户API" "http://localhost:5000/api/users" "200"
test_api "订单API" "http://localhost:5000/api/orders" "200"
test_api "购物车API" "http://localhost:5000/api/cart" "200"
test_api "联系API" "http://localhost:5000/api/contact" "200"
test_api "上传API" "http://localhost:5000/api/admin/upload" "200"
echo ""

# 3. 前端API路由测试
echo -e "${BLUE}=== 前端API路由测试 ===${NC}"
test_api "前端首页" "http://localhost:3000/" "200"
test_api "视频API" "http://localhost:3000/api/videos" "200"
test_api "管理统计API" "http://localhost:3000/api/admin/stats" "200"
test_api "产品页面API" "http://localhost:3000/api/products" "200"
test_api "新闻页面API" "http://localhost:3000/api/news" "200"
echo ""

# 4. 跨域访问测试
echo -e "${BLUE}=== 跨域访问测试 ===${NC}"
test_api "域名→后端API(产品)" "http://gdlongchi.cn:5000/api/products" "200"
test_api "域名→后端API(新闻)" "http://gdlongchi.cn:5000/api/news" "200"
test_api "域名→前端API(视频)" "http://gdlongchi.cn:3000/api/videos" "200"
echo ""

# 5. 具体API响应内容测试
echo -e "${BLUE}=== API响应内容测试 ===${NC}"
test_api_with_content "后端健康检查" "http://localhost:5000/health" "status"
test_api_with_content "前端视频API" "http://localhost:3000/api/videos" "success"
test_api_with_content "管理统计API" "http://localhost:3000/api/admin/stats" "success\|data"
echo ""

# 6. 错误页面测试
echo -e "${BLUE}=== 错误处理测试 ===${NC}"
test_api "后端404页面" "http://localhost:5000/nonexistent" "404"
test_api "前端404页面" "http://localhost:3000/nonexistent" "404"
echo ""

# 7. 性能测试
echo -e "${BLUE}=== 简单性能测试 ===${NC}"
echo "进行10次请求的平均响应时间测试..."

# 后端性能测试
echo -n "后端API平均响应时间: "
total_time=0
for i in {1..10}; do
    time=$(curl -s -w "%{time_total}" --connect-timeout 5 http://localhost:5000/health -o /dev/null 2>/dev/null)
    if [ $? -eq 0 ]; then
        total_time=$(echo "$total_time + $time" | bc -l 2>/dev/null || echo "$total_time")
    fi
done
if command -v bc > /dev/null; then
    avg_time=$(echo "scale=3; $total_time / 10" | bc)
    echo "${avg_time}s"
else
    echo "无法计算(bc未安装)"
fi

# 前端性能测试
echo -n "前端页面平均响应时间: "
total_time=0
for i in {1..10}; do
    time=$(curl -s -w "%{time_total}" --connect-timeout 5 http://localhost:3000 -o /dev/null 2>/dev/null)
    if [ $? -eq 0 ]; then
        total_time=$(echo "$total_time + $time" | bc -l 2>/dev/null || echo "$total_time")
    fi
done
if command -v bc > /dev/null; then
    avg_time=$(echo "scale=3; $total_time / 10" | bc)
    echo "${avg_time}s"
else
    echo "无法计算(bc未安装)"
fi
echo ""

# 8. 生成测试报告
echo -e "${BLUE}=== 测试报告 ===${NC}"
echo "测试时间: $(date)"
echo "测试服务器: $(hostname)"
echo ""

# 检查服务运行状态
echo "服务运行状态:"
if pgrep -f "node.*server.js" > /dev/null; then
    echo -e "  后端服务: ${GREEN}运行中${NC}"
else
    echo -e "  后端服务: ${RED}未运行${NC}"
fi

if pgrep -f "next start" > /dev/null; then
    echo -e "  前端服务: ${GREEN}运行中${NC}"
else
    echo -e "  前端服务: ${RED}未运行${NC}"
fi

# 端口监听状态
echo ""
echo "端口监听状态:"
for port in 3000 5000; do
    if netstat -tlnp 2>/dev/null | grep ":$port " > /dev/null; then
        echo -e "  端口 $port: ${GREEN}监听中${NC}"
    else
        echo -e "  端口 $port: ${RED}未监听${NC}"
    fi
done

echo ""
echo "=========================================="
echo "API测试完成！"
echo ""
echo "如果发现问题，建议操作："
echo "1. 查看服务日志: tail -f logs/backend.log"
echo "2. 重启服务: ./remote-fix.sh"
echo "3. 检查系统状态: ./remote-diagnose.sh"
echo "=========================================="

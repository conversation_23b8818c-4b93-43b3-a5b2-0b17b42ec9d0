"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/stats/route";
exports.ids = ["app/api/admin/stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_LongChiMall_lc_mall_new_frontend_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/stats/route.ts */ \"(rsc)/./src/app/api/admin/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/stats/route\",\n        pathname: \"/api/admin/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\api\\\\admin\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_LongChiMall_lc_mall_new_frontend_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/stats/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/admin/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_adminAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/adminAuth */ \"(rsc)/./src/utils/adminAuth.ts\");\n\n\nconst API_BASE = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n// Get dashboard statistics\nasync function GET(request) {\n    try {\n        const productsResponse = await fetch(`${API_BASE}/products?page=1&limit=1`, {\n            method: \"GET\",\n            headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_1__.getAdminHeaders)()\n        });\n        // 获取所有新闻文章（用于计算总访问量）\n        const newsResponse = await fetch(`${API_BASE}/news?page=1&limit=999`, {\n            method: \"GET\",\n            headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_1__.getAdminHeaders)()\n        });\n        let productCount = 0;\n        let newsCount = 0;\n        let totalViews = 0;\n        // 处理产品数据\n        if (productsResponse.ok) {\n            const productData = await productsResponse.json();\n            if (productData.success && productData.data && productData.data.pagination) {\n                productCount = productData.data.pagination.total || 0;\n            }\n        }\n        // 处理新闻数据\n        if (newsResponse.ok) {\n            const newsData = await newsResponse.json();\n            if (newsData.success && newsData.data) {\n                // 获取文章总数\n                if (newsData.data.pagination) {\n                    newsCount = newsData.data.pagination.total || 0;\n                } else if (newsData.data.articles) {\n                    newsCount = newsData.data.articles.length || 0;\n                }\n                // 计算总访问量（所有文章的浏览量总和）\n                if (newsData.data.articles && Array.isArray(newsData.data.articles)) {\n                    totalViews = newsData.data.articles.reduce((sum, article)=>{\n                        return sum + (article.views || 0);\n                    }, 0);\n                }\n            }\n        }\n        // 返回统计数据\n        const stats = {\n            productCount: productCount || 0,\n            newsCount: newsCount || 0,\n            totalViews: totalViews || 0,\n            systemStatus: \"normal\"\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(stats);\n    } catch (error) {\n        console.error(\"Error fetching dashboard stats:\", error);\n        // 返回默认统计数据\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            productCount: 0,\n            newsCount: 0,\n            totalViews: 0,\n            systemStatus: \"error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/adminAuth.ts":
/*!********************************!*\
  !*** ./src/utils/adminAuth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminHeaders: () => (/* binding */ getAdminHeaders)\n/* harmony export */ });\n/**\r\n * 管理员认证相关工具函数\r\n */ /**\r\n * 获取管理员API请求头\r\n * @returns 包含Content-Type和管理员API密钥的请求头对象\r\n */ function getAdminHeaders() {\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // 获取管理员API密钥\n    let adminApiKey;\n    if (true) {\n        adminApiKey = \"lc_admin_dev_key_2025\" || 0 || 0;\n        console.log(\"Server-side Admin API Key source:\",  true ? \"NEXT_PUBLIC_ADMIN_API_KEY\" : 0);\n    } else {}\n    if (adminApiKey) {\n        headers[\"x-admin-api-key\"] = adminApiKey;\n    }\n    return headers;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/adminAuth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/stats/route";
exports.ids = ["app/api/admin/stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_LongChiMall_lc_mall_new_frontend_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/stats/route.ts */ \"(rsc)/./src/app/api/admin/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/stats/route\",\n        pathname: \"/api/admin/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\api\\\\admin\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_LongChiMall_lc_mall_new_frontend_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/stats/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/admin/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_adminAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/adminAuth */ \"(rsc)/./src/utils/adminAuth.ts\");\n\n\nconst API_BASE = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n// Get dashboard statistics\nasync function GET(request) {\n    try {\n        // 获取产品统计 - 使用更大的limit来确保获取准确的总数\n        const productsResponse = await fetch(`${API_BASE}/products?page=1&limit=100`, {\n            method: \"GET\",\n            headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_1__.getAdminHeaders)()\n        });\n        // 获取所有新闻文章（用于计算总访问量）\n        const newsResponse = await fetch(`${API_BASE}/news?page=1&limit=999`, {\n            method: \"GET\",\n            headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_1__.getAdminHeaders)()\n        });\n        let productCount = 0;\n        let newsCount = 0;\n        let totalViews = 0;\n        // 处理产品数据\n        if (productsResponse.ok) {\n            const productData = await productsResponse.json();\n            console.log(\"Product API response: success =\", productData.success, \", products count =\", productData.data?.products?.length || 0);\n            if (productData.success && productData.data) {\n                if (productData.data.pagination) {\n                    productCount = productData.data.pagination.total || 0;\n                } else if (productData.data.products) {\n                    // 如果没有分页信息，使用产品数组长度\n                    productCount = productData.data.products.length || 0;\n                }\n            }\n        } else {\n            console.error(\"Products API failed:\", productsResponse.status, productsResponse.statusText);\n        }\n        // 处理新闻数据\n        if (newsResponse.ok) {\n            const newsData = await newsResponse.json();\n            if (newsData.success && newsData.data) {\n                // 获取文章总数\n                if (newsData.data.pagination) {\n                    newsCount = newsData.data.pagination.total || 0;\n                } else if (newsData.data.articles) {\n                    newsCount = newsData.data.articles.length || 0;\n                }\n                // 计算总访问量（所有文章的浏览量总和）\n                if (newsData.data.articles && Array.isArray(newsData.data.articles)) {\n                    totalViews = newsData.data.articles.reduce((sum, article)=>{\n                        return sum + (article.views || 0);\n                    }, 0);\n                }\n            }\n        } else {\n            console.error(\"News API failed:\", newsResponse.status, newsResponse.statusText);\n        }\n        // 返回统计数据\n        const stats = {\n            productCount: productCount || 0,\n            newsCount: newsCount || 0,\n            totalViews: totalViews || 0,\n            systemStatus: productCount > 0 ? \"normal\" : \"warning\"\n        };\n        console.log(\"Final stats:\", stats);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(stats);\n    } catch (error) {\n        console.error(\"Error fetching dashboard stats:\", error);\n        // 返回默认统计数据\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            productCount: 0,\n            newsCount: 0,\n            totalViews: 0,\n            systemStatus: \"error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/adminAuth.ts":
/*!********************************!*\
  !*** ./src/utils/adminAuth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminHeaders: () => (/* binding */ getAdminHeaders)\n/* harmony export */ });\n/**\r\n * 管理员认证相关工具函数\r\n */ /**\r\n * 获取管理员API请求头\r\n * @returns 包含Content-Type和管理员API密钥的请求头对象\r\n */ function getAdminHeaders() {\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // 获取管理员API密钥\n    let adminApiKey;\n    if (true) {\n        adminApiKey = \"lc_admin_dev_key_2025\" || 0 || 0;\n        console.log(\"Server-side Admin API Key source:\",  true ? \"NEXT_PUBLIC_ADMIN_API_KEY\" : 0);\n    } else {}\n    if (adminApiKey) {\n        headers[\"x-admin-api-key\"] = adminApiKey;\n    }\n    return headers;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/adminAuth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
#!/usr/bin/env node

/**
 * 产品数据导入脚本
 * 将 products.json 中的数据导入到 Redis 数据库
 */

// 设置控制台编码为 UTF-8
process.stdout.setDefaultEncoding && process.stdout.setDefaultEncoding('utf8');
process.stderr.setDefaultEncoding && process.stderr.setDefaultEncoding('utf8');

const fs = require('fs');
const path = require('path');
const productService = require('../src/services/productService');
const redisService = require('../src/services/redisService');
const logger = require('../src/utils/logger');

// 导入共享的产品分类配置
const { categoryMapping, getCategoryValue } = require('../../shared/config/productCategories.cjs');

// 图片路径映射
const getImageUrl = (imagePath) => {
  // 如果没有图片路径，返回默认图片
  if (!imagePath) {
    return '/images/products/default.jpg';
  }
  
  // 如果已经是正确的路径格式，直接返回
  if (imagePath.startsWith('/images/products/')) {
    return imagePath;
  }
  
  // 如果是旧格式的路径，进行转换
  if (imagePath.includes('/lc-mall/images/products/')) {
    return imagePath.replace('/lc-mall/images/products/', '/images/products/');
  }
  
  // 提取文件名（处理各种路径格式）
  const filename = imagePath.split('/').pop();
  
  // 如果文件名存在，返回新路径
  if (filename) {
    return `/images/products/${filename}`;
  }
  
  // 最后的默认图片
  return '/images/products/default.jpg';
};

// 转换规格字符串为对象格式
const parseSpecifications = (specString) => {
  if (!specString || typeof specString !== 'string') {
    return {};
  }

  // 如果规格字符串包含冒号，尝试解析为键值对
  if (specString.includes('：') || specString.includes(':')) {
    const specs = {};
    // 分割多个规格项（如果有的话）
    const items = specString.split(/[,，;；]/);

    items.forEach(item => {
      const trimmedItem = item.trim();
      if (trimmedItem.includes('：')) {
        const [key, value] = trimmedItem.split('：', 2);
        specs[key.trim()] = value.trim();
      } else if (trimmedItem.includes(':')) {
        const [key, value] = trimmedItem.split(':', 2);
        specs[key.trim()] = value.trim();
      } else if (trimmedItem) {
        // 如果没有明确的键值分隔符，使用默认键
        specs['规格'] = trimmedItem;
      }
    });

    return specs;
  }

  // 如果没有冒号，作为默认规格值
  return { '包装规格': specString };
};

// 转换产品数据格式以匹配 productService 期望的格式
const transformProductData = (productData) => {
  const category = getCategoryValue(productData.category);

  return {
    id: productData.id.toString(), // 使用原始JSON中的ID
    name: productData.name,
    description: productData.description || '',
    category: category,
    subcategory: null,
    specifications: parseSpecifications(productData.specifications),
    price: parseFloat(productData.price) || 0,
    unit: productData.specifications && productData.specifications.includes('kg') ? 'kg' :
          productData.specifications && productData.specifications.includes('桶') ? '桶' : '件',
    minOrderQuantity: 1,
    stockQuantity: parseInt(productData.stock) || 0,
    images: [getImageUrl(productData.image)],
    technicalData: productData.details || {},
    applications: productData.manual?.usageList || [],
    features: productData.manual?.dosageList || [],
    certifications: [],
    status: 'active',
    isHot: productData.featured === true,
    isNew: false,
    isFeatured: productData.featured === true,
    seoTitle: productData.name,
    seoDescription: productData.description || '',
    seoKeywords: `${productData.name},${productData.category},聚氨酯,助剂`,
    slug: generateSlug(productData.name),
    createdBy: 'system'
  };
};

// 生成 URL slug
const generateSlug = (name) => {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

// 主导入函数
async function importProducts() {
  try {
    console.log('🚀 开始导入产品数据...');
    
    // 确保 Redis 连接已建立
    console.log('🔗 正在连接 Redis...');
    await redisService.connect();
    console.log('✅ Redis 连接成功');
    
    // 读取产品数据文件
    const productsJsonPath = path.join(__dirname, '../../products.json');
    if (!fs.existsSync(productsJsonPath)) {
      throw new Error(`产品数据文件不存在: ${productsJsonPath}`);
    }
    
    const productsData = JSON.parse(fs.readFileSync(productsJsonPath, 'utf8'));
    console.log(`📦 找到 ${productsData.length} 个产品待导入`);
    
    // 清理现有数据（可选）
    console.log('🧹 正在清理现有产品数据...');
    await clearExistingData();
    
    // 初始化产品数据结构
    console.log('🔧 正在初始化产品数据结构...');
    await productService.initializeProductData();
    
    // 导入产品数据
    let successCount = 0;
    let failCount = 0;
    
    for (let i = 0; i < productsData.length; i++) {
      const productData = productsData[i];
      try {
        console.log(`📝 正在导入产品 ${i + 1}/${productsData.length}: ${productData.name}`);
        
        const transformedData = transformProductData(productData);
        const createdProduct = await productService.createProduct(transformedData);
        
        if (createdProduct) {
          successCount++;
          console.log(`✅ 成功导入: ${productData.name} (ID: ${createdProduct.id})`);
        } else {
          failCount++;
          console.log(`❌ 导入失败: ${productData.name}`);
        }
        
        // 添加小延迟避免过快操作Redis
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        failCount++;
        console.error(`❌ 导入产品失败: ${productData.name}`, error.message);
      }
    }
    
    // 显示导入结果
    console.log('\n📊 导入完成统计:');
    console.log(`✅ 成功导入: ${successCount} 个产品`);
    console.log(`❌ 导入失败: ${failCount} 个产品`);
    console.log(`📦 总计处理: ${productsData.length} 个产品`);
    
    // 验证导入结果
    await verifyImportedData();
    
    console.log('🎉 产品数据导入完成！');
    
  } catch (error) {
    console.error('💥 导入过程中发生错误:', error);
    logger.error('Product import failed', { error: error.message });
    process.exit(1);
  }
}

// 清理现有数据
async function clearExistingData() {
  try {
    // 获取所有产品相关的 Redis 键
    const productKeys = await redisService.keys('product:*');
    const categoryKeys = await redisService.keys('products:category:*');
    const searchKeys = await redisService.keys('products:search:*');
    const listKeys = ['products:all', 'categories:all'];
    
    const allKeys = [...productKeys, ...categoryKeys, ...searchKeys, ...listKeys];
    
    if (allKeys.length > 0) {
      await redisService.del(...allKeys);
      console.log(`🧹 已清理 ${allKeys.length} 个 Redis 键`);
    } else {
      console.log('🧹 没有找到需要清理的数据');
    }
    
  } catch (error) {
    console.warn('⚠️  清理现有数据时发生警告:', error.message);
  }
}

// 验证导入的数据
async function verifyImportedData() {
  try {
    console.log('\n🔍 正在验证导入的数据...');
    
    const result = await productService.getProducts({ page: 1, limit: 1000 });
    const stats = await productService.getProductStats();
    const categories = await productService.getCategories();
    
    console.log(`📦 数据库中共有 ${result.pagination.total} 个产品`);
    console.log(`📂 共有 ${categories.length} 个分类: ${categories.join(', ')}`);
    console.log(`🏷️  推荐产品: ${result.products.filter(p => p.isFeatured).length} 个`);
    console.log(`🔥 热门产品: ${result.products.filter(p => p.isHot).length} 个`);
    console.log(`💰 总价值: ${stats.totalValue.toFixed(2)} 元`);
    
  } catch (error) {
    console.warn('⚠️  验证数据时发生警告:', error.message);
  }
}

// 处理命令行参数
const args = process.argv.slice(2);
const shouldClearData = args.includes('--clear');

if (require.main === module) {
  // 直接运行脚本
  importProducts()
    .then(() => {
      console.log('👋 脚本执行完成，正在退出...');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  importProducts,
  transformProductData,
  clearExistingData
};

/**
 * 产品控制器 - 重构版本
 * 处理产品相关的HTTP请求
 */

const productService = require('../services/productService');
const logger = require('../utils/logger');
const ApiResponse = require('../utils/response');
const { asyncHandler } = require('../middleware/errorHandler');
const { ErrorFactory } = require('../utils/errors');

const productController = {
  // 获取所有产品
  getAllProducts: asyncHandler(async (req, res) => {
    const { page = 1, limit = 20, category, featured, inStock, sortBy, sortOrder } = req.query;
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      category: category || undefined,
      featured: featured === 'true' ? true : featured === 'false' ? false : undefined,
      inStock: inStock === 'true' ? true : inStock === 'false' ? false : undefined,
      sortBy: sortBy || 'createdAt',
      sortOrder: sortOrder || 'desc'
    };

    const result = await productService.getProducts(options);

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 根据ID获取产品
  getProductById: asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { increment_view = 'true' } = req.query;

    const result = await productService.getProductById(id, increment_view === 'true');
    
    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.notFound('Product', id);
    }
  }),

  // 根据分类获取产品
  getProductsByCategory: asyncHandler(async (req, res) => {
    const { category } = req.params;
    const { page = 1, limit = 20, sortBy, sortOrder } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      category,
      sortBy: sortBy || 'createdAt',
      sortOrder: sortOrder || 'desc'
    };

    const result = await productService.getProductsByCategory(category, options);

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 搜索产品
  searchProducts: asyncHandler(async (req, res) => {
    const { query } = req.params;
    const { page = 1, limit = 20, category, sortBy, sortOrder } = req.query;
    
    const searchOptions = {
      query,
      page: parseInt(page),
      limit: parseInt(limit),
      category: category || undefined,
      sortBy: sortBy || 'createdAt',
      sortOrder: sortOrder || 'desc'
    };

    const result = await productService.searchProducts(searchOptions);

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 获取推荐产品
  getFeaturedProducts: asyncHandler(async (req, res) => {
    const { limit = 10 } = req.query;
    
    const result = await productService.getFeaturedProducts(parseInt(limit));

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 获取热门产品
  getHotProducts: asyncHandler(async (req, res) => {
    const { limit = 10 } = req.query;
    
    const result = await productService.getHotProducts(parseInt(limit));

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 获取新品推荐
  getNewProducts: asyncHandler(async (req, res) => {
    const { limit = 10 } = req.query;
    
    const result = await productService.getNewProducts(parseInt(limit));

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 获取相关产品
  getRelatedProducts: asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { limit = 6 } = req.query;
    
    const result = await productService.getRelatedProducts(id, parseInt(limit));

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 获取产品分类
  getCategories: asyncHandler(async (req, res) => {
    const result = await productService.getCategories();

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 获取产品统计信息
  getProductStats: asyncHandler(async (req, res) => {
    const result = await productService.getProductStats();

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 创建新产品 (管理员功能)
  createProduct: asyncHandler(async (req, res) => {
    const productData = req.body;
    const createdBy = req.user?.id || null;
    
    // 基本验证
    if (!productData.name || !productData.category || productData.price === undefined) {
      throw ErrorFactory.validation('产品名称、分类和价格不能为空');
    }

    const result = await productService.createProduct(productData, createdBy);

    if (result.success) {
      return ApiResponse.success(res, result.data, result.message, 201);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 更新产品信息 (管理员功能)
  updateProduct: asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const updatedBy = req.user?.id || null;

    const result = await productService.updateProduct(id, updateData, updatedBy);
    
    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.notFound('Product', id);
    }
  }),

  // 删除产品 (管理员功能)
  deleteProduct: asyncHandler(async (req, res) => {
    const { id } = req.params;
    const deletedBy = req.user?.id || null;

    const result = await productService.deleteProduct(id, deletedBy);
    
    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.notFound('Product', id);
    }
  }),

  // 更新库存 (管理员功能)
  updateStock: asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { stock } = req.body;
    const updatedBy = req.user?.id || null;

    if (typeof stock !== 'number' || stock < 0) {
      throw ErrorFactory.validation('库存数量必须是非负数');
    }

    const result = await productService.updateStock(id, stock, updatedBy);
    
    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 批量更新产品状态 (管理员功能)
  batchUpdateStatus: asyncHandler(async (req, res) => {
    const { productIds, status } = req.body;
    const updatedBy = req.user?.id || null;

    if (!Array.isArray(productIds) || productIds.length === 0) {
      throw ErrorFactory.validation('产品ID列表不能为空');
    }

    if (!['active', 'inactive', 'draft'].includes(status)) {
      throw ErrorFactory.validation('状态值无效');
    }

    const result = await productService.batchUpdateStatus(productIds, status, updatedBy);
    
    if (result.success) {
      return ApiResponse.success(res, result.data, result.message);
    } else {
      throw ErrorFactory.business(result.message);
    }
  }),

  // 批量导入产品 (管理员功能)
  batchImportProducts: asyncHandler(async (req, res) => {
    const { products } = req.body;
    const importedBy = req.user?.id || null;

    if (!Array.isArray(products) || products.length === 0) {
      throw ErrorFactory.validation('产品数据列表不能为空');
    }

    const result = await productService.batchImportProducts(products, importedBy);

    return ApiResponse.success(res, result.data, result.message);
  }),

  // 检查库存 (兼容性方法)
  checkStock: asyncHandler(async (req, res) => {
    const { id } = req.params;

    const result = await productService.getProductById(id);

    if (result.success) {
      const stockInfo = {
        productId: id,
        stockQuantity: result.data.stockQuantity,
        inStock: result.data.stockQuantity > 0,
        minOrderQuantity: result.data.minOrderQuantity
      };
      return ApiResponse.success(res, stockInfo, '库存信息获取成功');
    } else {
      throw ErrorFactory.notFound('Product', id);
    }
  })
};

module.exports = productController;

#!/bin/bash

echo "=========================================="
echo "    LC Mall - 系统诊断脚本                "
echo "=========================================="

# 检查基本环境
echo "🔍 检查系统环境..."
echo "操作系统: $(uname -s)"
echo "Node.js 版本: $(node -v 2>/dev/null || echo '未安装')"
echo "npm 版本: $(npm -v 2>/dev/null || echo '未安装')"
echo "当前目录: $(pwd)"

# 检查环境变量
echo ""
echo "🌐 检查环境变量..."
if [ -f ".env.production" ]; then
    echo "✅ .env.production 文件存在"
    echo "NEXT_PUBLIC_API_URL: $(grep NEXT_PUBLIC_API_URL .env.production | cut -d'=' -f2)"
    echo "NODE_ENV: $(grep NODE_ENV .env.production | cut -d'=' -f2)"
else
    echo "❌ .env.production 文件不存在"
fi

# 检查进程状态
echo ""
echo "🔄 检查服务进程..."

# 检查后端进程
backend_pid=$(cat logs/backend.pid 2>/dev/null)
if [ -n "$backend_pid" ] && ps -p $backend_pid > /dev/null 2>&1; then
    echo "✅ 后端服务运行中 (PID: $backend_pid)"
else
    echo "❌ 后端服务未运行"
fi

# 检查前端进程
frontend_pid=$(cat logs/frontend.pid 2>/dev/null)
if [ -n "$frontend_pid" ] && ps -p $frontend_pid > /dev/null 2>&1; then
    echo "✅ 前端服务运行中 (PID: $frontend_pid)"
else
    echo "❌ 前端服务未运行"
fi

# 检查端口占用
echo ""
echo "🔌 检查端口占用..."
if netstat -tlnp 2>/dev/null | grep :5000 > /dev/null; then
    echo "✅ 端口 5000 (后端) 已占用"
    netstat -tlnp 2>/dev/null | grep :5000
else
    echo "❌ 端口 5000 (后端) 未占用"
fi

if netstat -tlnp 2>/dev/null | grep :3000 > /dev/null; then
    echo "✅ 端口 3000 (前端) 已占用"
    netstat -tlnp 2>/dev/null | grep :3000
else
    echo "❌ 端口 3000 (前端) 未占用"
fi

# 检查Redis连接
echo ""
echo "🗄️  检查 Redis 连接..."
if command -v redis-cli > /dev/null; then
    if redis-cli -a 123456 ping 2>/dev/null | grep PONG > /dev/null; then
        echo "✅ Redis 连接正常"
        echo "Redis 产品数量: $(redis-cli -a 123456 scard products:all 2>/dev/null || echo '无法获取')"
    else
        echo "❌ Redis 连接失败"
    fi
else
    echo "⚠️  redis-cli 未安装，无法测试 Redis 连接"
fi

# 检查日志文件
echo ""
echo "📄 检查日志文件..."
if [ -f "logs/backend.log" ]; then
    echo "✅ 后端日志存在 ($(wc -l < logs/backend.log) 行)"
    echo "最新后端日志:"
    tail -3 logs/backend.log | sed 's/^/  /'
else
    echo "❌ 后端日志不存在"
fi

if [ -f "logs/frontend.log" ]; then
    echo "✅ 前端日志存在 ($(wc -l < logs/frontend.log) 行)"
    echo "最新前端日志:"
    tail -3 logs/frontend.log | sed 's/^/  /'
else
    echo "❌ 前端日志不存在"
fi

# 测试API连接
echo ""
echo "🧪 测试 API 连接..."

# 测试后端健康检查
if curl -s http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ 后端健康检查通过"
else
    echo "❌ 后端健康检查失败"
fi

# 测试后端产品API
if curl -s http://localhost:5000/api/products?limit=1 > /dev/null 2>&1; then
    echo "✅ 后端产品API正常"
    product_count=$(curl -s http://localhost:5000/api/products?limit=1 | grep -o '"total":[0-9]*' | cut -d':' -f2)
    echo "  产品总数: ${product_count:-'无法获取'}"
else
    echo "❌ 后端产品API失败"
fi

# 测试前端主页
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端主页正常"
else
    echo "❌ 前端主页无法访问"
fi

# 测试前端API代理
if curl -s http://localhost:3000/api/products?limit=1 > /dev/null 2>&1; then
    echo "✅ 前端API代理正常"
else
    echo "❌ 前端API代理失败"
fi

# 检查磁盘空间
echo ""
echo "💾 检查磁盘空间..."
df -h . | tail -1 | awk '{print "可用空间: " $4 " (使用率: " $5 ")"}'

# 检查内存使用
echo ""
echo "🧠 检查内存使用..."
free -h | grep Mem | awk '{print "内存使用: " $3 "/" $2 " (可用: " $7 ")"}'

echo ""
echo "=========================================="
echo "         📊 诊断完成                      "
echo "=========================================="
echo ""
echo "💡 常见问题解决方案:"
echo "1. 如果服务未运行: ./deploy.sh"
echo "2. 如果前端有问题: ./fix-frontend.sh"
echo "3. 如果需要重新导入产品: cd backend && node scripts/importProducts.js"
echo "4. 如果需要完整测试: node test-api.js"
echo "5. 查看实时日志: tail -f logs/backend.log 或 tail -f logs/frontend.log"
echo ""

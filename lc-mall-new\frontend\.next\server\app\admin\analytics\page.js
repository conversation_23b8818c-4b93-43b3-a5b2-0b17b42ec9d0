/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/analytics/page";
exports.ids = ["app/admin/analytics/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fanalytics%2Fpage&page=%2Fadmin%2Fanalytics%2Fpage&appPaths=%2Fadmin%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fanalytics%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fanalytics%2Fpage&page=%2Fadmin%2Fanalytics%2Fpage&appPaths=%2Fadmin%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fanalytics%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'analytics',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/analytics/page.tsx */ \"(rsc)/./src/app/admin/analytics/page.tsx\")), \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/analytics/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/analytics/page\",\n        pathname: \"/admin/analytics\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fanalytics%2Fpage&page=%2Fadmin%2Fanalytics%2Fpage&appPaths=%2Fadmin%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fanalytics%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CAnalyticsTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CAnalyticsTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/AnalyticsTracker.tsx */ \"(ssr)/./src/components/analytics/AnalyticsTracker.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CAnalyticsTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/analytics/page.tsx */ \"(ssr)/./src/app/admin/analytics/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUNMb25nQ2hpTWFsbCU1QyU1Q2xjLW1hbGwtbmV3JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhZG1pbiU1QyU1Q2FuYWx5dGljcyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBeUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYy1tYWxsLWZyb250ZW5kLz8zNDE2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcZ2l0aHViXFxcXExvbmdDaGlNYWxsXFxcXGxjLW1hbGwtbmV3XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW5cXFxcYW5hbHl0aWNzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/analytics/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/analytics/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,subDays!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,subDays!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/subDays/index.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/esm/locale/zh-CN/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,EyeIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,EyeIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,EyeIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,EyeIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,EyeIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,EyeIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,EyeIcon,GlobeAltIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _utils_adminAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/adminAuth */ \"(ssr)/./src/utils/adminAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\r\n * 访问统计页面\r\n * Analytics Dashboard - 后台管理访问统计界面\r\n */ \n\n\n\n\nfunction AnalyticsPage() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [records, setRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [recordsLoading, setRecordsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 日期范围状态\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>(0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(new Date(), 7), \"yyyy-MM-dd\"));\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>(0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(new Date(), \"yyyy-MM-dd\"));\n    // 分页状态\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    // 确保组件挂载\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 只在客户端挂载后获取数据\n        if (mounted) {\n            fetchStats();\n            fetchRecords();\n        }\n    }, [\n        mounted,\n        startDate,\n        endDate\n    ]); // 获取统计数据\n    const fetchStats = async ()=>{\n        // 多重检查确保只在客户端执行\n        if (true) {\n            return;\n        }\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(`/api/analytics/admin/stats?startDate=${startDate}&endDate=${endDate}`, {\n                headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_2__.getAdminHeaders)()\n            });\n            if (!response.ok) {\n                throw new Error(\"获取统计数据失败\");\n            }\n            const result = await response.json();\n            setStats(result.data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取统计数据失败\");\n        } finally{\n            setLoading(false);\n        }\n    }; // 获取访问记录\n    const fetchRecords = async (page = 1)=>{\n        // 多重检查确保只在客户端执行\n        if (true) {\n            return;\n        }\n        try {\n            setRecordsLoading(true);\n            const offset = (page - 1) * pageSize;\n            const response = await fetch(`/api/analytics/admin/records?startDate=${startDate}&endDate=${endDate}&limit=${pageSize}&offset=${offset}`, {\n                headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_2__.getAdminHeaders)()\n            });\n            if (!response.ok) {\n                throw new Error(\"获取访问记录失败\");\n            }\n            const result = await response.json();\n            setRecords(result.data.records);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取访问记录失败\");\n        } finally{\n            setRecordsLoading(false);\n        }\n    };\n    // 导出数据\n    const exportData = async ()=>{\n        try {\n            const response = await fetch(`/api/analytics/admin/export?startDate=${startDate}&endDate=${endDate}&format=csv`, {\n                headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_2__.getAdminHeaders)()\n            });\n            if (!response.ok) {\n                throw new Error(\"导出数据失败\");\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = `analytics_${startDate}_${endDate}.csv`;\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"导出数据失败\");\n        }\n    };\n    const formatDuration = (seconds)=>{\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = seconds % 60;\n        return `${minutes}分${remainingSeconds}秒`;\n    };\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat(\"zh-CN\").format(num);\n    };\n    const StatCard = ({ title, value, icon: Icon, className = \"\", formatter = formatNumber })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `bg-white rounded-lg shadow p-6 ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"h-8 w-8 text-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-5 w-0 flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: formatter(value)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n            lineNumber: 201,\n            columnNumber: 5\n        }, this);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/4 mb-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                1,\n                                2,\n                                3,\n                                4\n                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-20 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 19\n                                    }, this)\n                                }, i, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"访问统计\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"查看网站访问数据和用户行为分析\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"开始日期\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: startDate,\n                                                onChange: (e)=>setStartDate(e.target.value),\n                                                className: \"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"结束日期\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: endDate,\n                                                onChange: (e)=>setEndDate(e.target.value),\n                                                className: \"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: exportData,\n                                className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"导出数据\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-md p-4 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-800\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this),\n                stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"总访问量\",\n                                    value: stats.totalViews,\n                                    icon: _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"独立访客\",\n                                    value: stats.uniqueVisitors,\n                                    icon: _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"访问会话\",\n                                    value: stats.uniqueSessions,\n                                    icon: _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"平均停留时间\",\n                                    value: stats.avgDuration,\n                                    icon: _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    formatter: formatDuration\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"热门页面\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: Object.entries(stats.popularPages).sort(([, a], [, b])=>b - a).slice(0, 10).map(([page, views])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 truncate flex-1 mr-4\",\n                                                            children: page || \"/\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: formatNumber(views)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, page, true, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"设备类型\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: Object.entries(stats.deviceTypes).map(([device, count])=>{\n                                                const total = Object.values(stats.deviceTypes).reduce((sum, val)=>sum + val, 0);\n                                                const percentage = total > 0 ? Math.round(count / total * 100) : 0;\n                                                const Icon = device === \"mobile\" ? _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-5 w-5 text-gray-400 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 capitalize\",\n                                                                    children: device === \"desktop\" ? \"桌面端\" : device === \"mobile\" ? \"移动端\" : device === \"tablet\" ? \"平板端\" : device\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500 mr-2\",\n                                                                    children: [\n                                                                        percentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: formatNumber(count)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, device, true, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"浏览器分布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: Object.entries(stats.browsers).sort(([, a], [, b])=>b - a).slice(0, 8).map(([browser, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: browser\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: formatNumber(count)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, browser, true, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"地理分布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: Object.entries(stats.countries).sort(([, a], [, b])=>b - a).slice(0, 8).map(([country, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: country || \"未知\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: formatNumber(count)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, country, true, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"最近访问记录\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"min-w-full divide-y divide-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"访问时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"页面路径\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"IP地址\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"设备/浏览器\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"停留时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"来源\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"bg-white divide-y divide-gray-200\",\n                                                children: recordsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 6,\n                                                        className: \"px-6 py-4 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-pulse\",\n                                                            children: \"加载中...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 23\n                                                }, this) : records.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 6,\n                                                        className: \"px-6 py-4 text-center text-gray-500\",\n                                                        children: \"暂无访问记录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 23\n                                                }, this) : records.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                children: (0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(new Date(record.timestamp), \"yyyy-MM-dd HH:mm:ss\", {\n                                                                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-xs truncate\",\n                                                                    title: record.pathname,\n                                                                    children: record.pathname\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                children: record.ip\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"capitalize\",\n                                                                            children: record.deviceType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                record.browserName,\n                                                                                \" \",\n                                                                                record.browserVersion\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                children: record.duration > 0 ? formatDuration(record.duration) : \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-xs truncate\",\n                                                                    title: record.referer,\n                                                                    children: record.referer || \"直接访问\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, record.id, true, {\n                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/analytics/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 3000,\n                    style: {\n                        background: \"#fff\",\n                        color: \"#333\",\n                        borderRadius: \"8px\",\n                        boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                    },\n                    success: {\n                        iconTheme: {\n                            primary: \"#22c55e\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/analytics/AnalyticsTracker.tsx":
/*!*******************************************************!*\
  !*** ./src/components/analytics/AnalyticsTracker.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsTracker)\n/* harmony export */ });\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/hooks/useAnalytics */ \"(ssr)/./src/hooks/useAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ /**\r\n * 访问追踪组件\r\n * Analytics Tracker - 处理页面访问追踪\r\n */ \nfunction AnalyticsTracker() {\n    // 使用页面追踪Hook\n    (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_0__.usePageTracking)();\n    // 这个组件不渲染任何内容\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hbmFseXRpY3MvQW5hbHl0aWNzVHJhY2tlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRUE7OztDQUdDLEdBRXNEO0FBRXhDLFNBQVNDO0lBQ3RCLGFBQWE7SUFDYkQsb0VBQWVBO0lBRWYsY0FBYztJQUNkLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9hbmFseXRpY3MvQW5hbHl0aWNzVHJhY2tlci50c3g/ZTc5OCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG4vKipcclxuICog6K6/6Zeu6L+96Liq57uE5Lu2XHJcbiAqIEFuYWx5dGljcyBUcmFja2VyIC0g5aSE55CG6aG16Z2i6K6/6Zeu6L+96LiqXHJcbiAqL1xyXG5cclxuaW1wb3J0IHsgdXNlUGFnZVRyYWNraW5nIH0gZnJvbSAnQC9ob29rcy91c2VBbmFseXRpY3MnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQW5hbHl0aWNzVHJhY2tlcigpIHtcclxuICAvLyDkvb/nlKjpobXpnaLov73ouKpIb29rXHJcbiAgdXNlUGFnZVRyYWNraW5nKCk7XHJcblxyXG4gIC8vIOi/meS4que7hOS7tuS4jea4suafk+S7u+S9leWGheWuuVxyXG4gIHJldHVybiBudWxsO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VQYWdlVHJhY2tpbmciLCJBbmFseXRpY3NUcmFja2VyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/analytics/AnalyticsTracker.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAnalytics.ts":
/*!***********************************!*\
  !*** ./src/hooks/useAnalytics.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEventTracking: () => (/* binding */ useEventTracking),\n/* harmony export */   usePageTracking: () => (/* binding */ usePageTracking),\n/* harmony export */   useScrollTracking: () => (/* binding */ useScrollTracking),\n/* harmony export */   useTimeTracking: () => (/* binding */ useTimeTracking)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_analytics__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/analytics */ \"(ssr)/./src/services/analytics.ts\");\n/**\r\n * 用户访问追踪 Hook\r\n * Use Analytics Hook - React Hook for analytics tracking\r\n */ \n\n\n/**\r\n * 页面访问追踪 Hook\r\n */ function usePageTracking() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // 延迟执行以确保页面完全加载\n        const timer = setTimeout(()=>{\n            _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackPageView(pathname);\n        }, 100);\n        return ()=>clearTimeout(timer);\n    }, [\n        pathname\n    ]);\n}\n/**\r\n * 事件追踪 Hook\r\n */ function useEventTracking() {\n    const trackEvent = (event, eventData)=>{\n        _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackEvent(event, eventData);\n    };\n    const trackClick = (elementName, additionalData)=>{\n        trackEvent(\"click\", {\n            element: elementName,\n            ...additionalData\n        });\n    };\n    const trackFormSubmit = (formName, additionalData)=>{\n        trackEvent(\"form_submit\", {\n            form: formName,\n            ...additionalData\n        });\n    };\n    const trackDownload = (fileName, fileType)=>{\n        trackEvent(\"download\", {\n            fileName,\n            fileType\n        });\n    };\n    const trackSearch = (query, resultsCount)=>{\n        trackEvent(\"search\", {\n            query,\n            resultsCount\n        });\n    };\n    const trackProductView = (productId, productName, category)=>{\n        trackEvent(\"product_view\", {\n            productId,\n            productName,\n            category\n        });\n    };\n    const trackAddToCart = (productId, productName, quantity, price)=>{\n        trackEvent(\"add_to_cart\", {\n            productId,\n            productName,\n            quantity,\n            price\n        });\n    };\n    return {\n        trackEvent,\n        trackClick,\n        trackFormSubmit,\n        trackDownload,\n        trackSearch,\n        trackProductView,\n        trackAddToCart\n    };\n}\n/**\r\n * 滚动追踪 Hook\r\n */ function useScrollTracking(threshold = 50) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let hasTracked = false;\n        const trackingThreshold = threshold; // 滚动百分比阈值\n        const handleScroll = ()=>{\n            if (hasTracked) return;\n            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n            const documentHeight = document.documentElement.scrollHeight - window.innerHeight;\n            const scrollPercentage = scrollTop / documentHeight * 100;\n            if (scrollPercentage >= trackingThreshold) {\n                _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackEvent(\"scroll_threshold\", {\n                    threshold: trackingThreshold,\n                    percentage: Math.round(scrollPercentage)\n                });\n                hasTracked = true;\n            }\n        };\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        threshold\n    ]);\n}\n/**\r\n * 停留时间追踪 Hook\r\n */ function useTimeTracking(intervals = [\n    30,\n    60,\n    120,\n    300\n]) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const trackedIntervals = new Set();\n        const startTime = Date.now();\n        const checkIntervals = ()=>{\n            const elapsed = Math.floor((Date.now() - startTime) / 1000);\n            intervals.forEach((interval)=>{\n                if (elapsed >= interval && !trackedIntervals.has(interval)) {\n                    _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackEvent(\"time_on_page\", {\n                        interval,\n                        elapsed\n                    });\n                    trackedIntervals.add(interval);\n                }\n            });\n        };\n        const timer = setInterval(checkIntervals, 1000);\n        return ()=>clearInterval(timer);\n    }, [\n        intervals\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlQW5hbHl0aWNzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7OztDQUdDLEdBRWlDO0FBQ1k7QUFDTTtBQUVwRDs7Q0FFQyxHQUNNLFNBQVNHO0lBQ2QsTUFBTUMsV0FBV0gsNERBQVdBO0lBRTVCRCxnREFBU0EsQ0FBQztRQUNSLGdCQUFnQjtRQUNoQixNQUFNSyxRQUFRQyxXQUFXO1lBQ3ZCSiwyREFBZ0JBLENBQUNLLGFBQWEsQ0FBQ0g7UUFDakMsR0FBRztRQUVILE9BQU8sSUFBTUksYUFBYUg7SUFDNUIsR0FBRztRQUFDRDtLQUFTO0FBQ2Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNLO0lBQ2QsTUFBTUMsYUFBYSxDQUFDQyxPQUFlQztRQUNqQ1YsMkRBQWdCQSxDQUFDUSxVQUFVLENBQUNDLE9BQU9DO0lBQ3JDO0lBRUEsTUFBTUMsYUFBYSxDQUFDQyxhQUFxQkM7UUFDdkNMLFdBQVcsU0FBUztZQUNsQk0sU0FBU0Y7WUFDVCxHQUFHQyxjQUFjO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNRSxrQkFBa0IsQ0FBQ0MsVUFBa0JIO1FBQ3pDTCxXQUFXLGVBQWU7WUFDeEJTLE1BQU1EO1lBQ04sR0FBR0gsY0FBYztRQUNuQjtJQUNGO0lBRUEsTUFBTUssZ0JBQWdCLENBQUNDLFVBQWtCQztRQUN2Q1osV0FBVyxZQUFZO1lBQ3JCVztZQUNBQztRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxjQUFjLENBQUNDLE9BQWVDO1FBQ2xDZixXQUFXLFVBQVU7WUFDbkJjO1lBQ0FDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLG1CQUFtQixDQUFDQyxXQUFtQkMsYUFBcUJDO1FBQ2hFbkIsV0FBVyxnQkFBZ0I7WUFDekJpQjtZQUNBQztZQUNBQztRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUIsQ0FBQ0gsV0FBbUJDLGFBQXFCRyxVQUFrQkM7UUFDaEZ0QixXQUFXLGVBQWU7WUFDeEJpQjtZQUNBQztZQUNBRztZQUNBQztRQUNGO0lBQ0Y7SUFFQSxPQUFPO1FBQ0x0QjtRQUNBRztRQUNBSTtRQUNBRztRQUNBRztRQUNBRztRQUNBSTtJQUNGO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNHLGtCQUFrQkMsWUFBb0IsRUFBRTtJQUN0RGxDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSW1DLGFBQWE7UUFDakIsTUFBTUMsb0JBQW9CRixXQUFXLFVBQVU7UUFFL0MsTUFBTUcsZUFBZTtZQUNuQixJQUFJRixZQUFZO1lBRWhCLE1BQU1HLFlBQVlDLE9BQU9DLFdBQVcsSUFBSUMsU0FBU0MsZUFBZSxDQUFDSixTQUFTO1lBQzFFLE1BQU1LLGlCQUFpQkYsU0FBU0MsZUFBZSxDQUFDRSxZQUFZLEdBQUdMLE9BQU9NLFdBQVc7WUFDakYsTUFBTUMsbUJBQW1CLFlBQWFILGlCQUFrQjtZQUV4RCxJQUFJRyxvQkFBb0JWLG1CQUFtQjtnQkFDekNsQywyREFBZ0JBLENBQUNRLFVBQVUsQ0FBQyxvQkFBb0I7b0JBQzlDd0IsV0FBV0U7b0JBQ1hXLFlBQVlDLEtBQUtDLEtBQUssQ0FBQ0g7Z0JBQ3pCO2dCQUNBWCxhQUFhO1lBQ2Y7UUFDRjtRQUVBSSxPQUFPVyxnQkFBZ0IsQ0FBQyxVQUFVYixjQUFjO1lBQUVjLFNBQVM7UUFBSztRQUVoRSxPQUFPO1lBQ0xaLE9BQU9hLG1CQUFtQixDQUFDLFVBQVVmO1FBQ3ZDO0lBQ0YsR0FBRztRQUFDSDtLQUFVO0FBQ2hCO0FBRUE7O0NBRUMsR0FDTSxTQUFTbUIsZ0JBQWdCQyxZQUFzQjtJQUFDO0lBQUk7SUFBSTtJQUFLO0NBQUk7SUFDdEV0RCxnREFBU0EsQ0FBQztRQUNSLE1BQU11RCxtQkFBbUIsSUFBSUM7UUFDN0IsTUFBTUMsWUFBWUMsS0FBS0MsR0FBRztRQUUxQixNQUFNQyxpQkFBaUI7WUFDckIsTUFBTUMsVUFBVWIsS0FBS2MsS0FBSyxDQUFDLENBQUNKLEtBQUtDLEdBQUcsS0FBS0YsU0FBUSxJQUFLO1lBRXRESCxVQUFVUyxPQUFPLENBQUNDLENBQUFBO2dCQUNoQixJQUFJSCxXQUFXRyxZQUFZLENBQUNULGlCQUFpQlUsR0FBRyxDQUFDRCxXQUFXO29CQUMxRDlELDJEQUFnQkEsQ0FBQ1EsVUFBVSxDQUFDLGdCQUFnQjt3QkFDMUNzRDt3QkFDQUg7b0JBQ0Y7b0JBQ0FOLGlCQUFpQlcsR0FBRyxDQUFDRjtnQkFDdkI7WUFDRjtRQUNGO1FBRUEsTUFBTTNELFFBQVE4RCxZQUFZUCxnQkFBZ0I7UUFFMUMsT0FBTyxJQUFNUSxjQUFjL0Q7SUFDN0IsR0FBRztRQUFDaUQ7S0FBVTtBQUNoQiIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvLi9zcmMvaG9va3MvdXNlQW5hbHl0aWNzLnRzPzNiYTMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIOeUqOaIt+iuv+mXrui/vei4qiBIb29rXHJcbiAqIFVzZSBBbmFseXRpY3MgSG9vayAtIFJlYWN0IEhvb2sgZm9yIGFuYWx5dGljcyB0cmFja2luZ1xyXG4gKi9cclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5pbXBvcnQgYW5hbHl0aWNzU2VydmljZSBmcm9tICdAL3NlcnZpY2VzL2FuYWx5dGljcyc7XHJcblxyXG4vKipcclxuICog6aG16Z2i6K6/6Zeu6L+96LiqIEhvb2tcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiB1c2VQYWdlVHJhY2tpbmcoKSB7XHJcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8g5bu26L+f5omn6KGM5Lul56Gu5L+d6aG16Z2i5a6M5YWo5Yqg6L29XHJcbiAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICBhbmFseXRpY3NTZXJ2aWNlLnRyYWNrUGFnZVZpZXcocGF0aG5hbWUpO1xyXG4gICAgfSwgMTAwKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcclxuICB9LCBbcGF0aG5hbWVdKTtcclxufVxyXG5cclxuLyoqXHJcbiAqIOS6i+S7tui/vei4qiBIb29rXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gdXNlRXZlbnRUcmFja2luZygpIHtcclxuICBjb25zdCB0cmFja0V2ZW50ID0gKGV2ZW50OiBzdHJpbmcsIGV2ZW50RGF0YT86IGFueSkgPT4ge1xyXG4gICAgYW5hbHl0aWNzU2VydmljZS50cmFja0V2ZW50KGV2ZW50LCBldmVudERhdGEpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHRyYWNrQ2xpY2sgPSAoZWxlbWVudE5hbWU6IHN0cmluZywgYWRkaXRpb25hbERhdGE/OiBhbnkpID0+IHtcclxuICAgIHRyYWNrRXZlbnQoJ2NsaWNrJywge1xyXG4gICAgICBlbGVtZW50OiBlbGVtZW50TmFtZSxcclxuICAgICAgLi4uYWRkaXRpb25hbERhdGFcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHRyYWNrRm9ybVN1Ym1pdCA9IChmb3JtTmFtZTogc3RyaW5nLCBhZGRpdGlvbmFsRGF0YT86IGFueSkgPT4ge1xyXG4gICAgdHJhY2tFdmVudCgnZm9ybV9zdWJtaXQnLCB7XHJcbiAgICAgIGZvcm06IGZvcm1OYW1lLFxyXG4gICAgICAuLi5hZGRpdGlvbmFsRGF0YVxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdHJhY2tEb3dubG9hZCA9IChmaWxlTmFtZTogc3RyaW5nLCBmaWxlVHlwZT86IHN0cmluZykgPT4ge1xyXG4gICAgdHJhY2tFdmVudCgnZG93bmxvYWQnLCB7XHJcbiAgICAgIGZpbGVOYW1lLFxyXG4gICAgICBmaWxlVHlwZVxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdHJhY2tTZWFyY2ggPSAocXVlcnk6IHN0cmluZywgcmVzdWx0c0NvdW50PzogbnVtYmVyKSA9PiB7XHJcbiAgICB0cmFja0V2ZW50KCdzZWFyY2gnLCB7XHJcbiAgICAgIHF1ZXJ5LFxyXG4gICAgICByZXN1bHRzQ291bnRcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHRyYWNrUHJvZHVjdFZpZXcgPSAocHJvZHVjdElkOiBzdHJpbmcsIHByb2R1Y3ROYW1lOiBzdHJpbmcsIGNhdGVnb3J5Pzogc3RyaW5nKSA9PiB7XHJcbiAgICB0cmFja0V2ZW50KCdwcm9kdWN0X3ZpZXcnLCB7XHJcbiAgICAgIHByb2R1Y3RJZCxcclxuICAgICAgcHJvZHVjdE5hbWUsXHJcbiAgICAgIGNhdGVnb3J5XHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICBjb25zdCB0cmFja0FkZFRvQ2FydCA9IChwcm9kdWN0SWQ6IHN0cmluZywgcHJvZHVjdE5hbWU6IHN0cmluZywgcXVhbnRpdHk6IG51bWJlciwgcHJpY2U6IG51bWJlcikgPT4ge1xyXG4gICAgdHJhY2tFdmVudCgnYWRkX3RvX2NhcnQnLCB7XHJcbiAgICAgIHByb2R1Y3RJZCxcclxuICAgICAgcHJvZHVjdE5hbWUsXHJcbiAgICAgIHF1YW50aXR5LFxyXG4gICAgICBwcmljZVxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIHRyYWNrRXZlbnQsXHJcbiAgICB0cmFja0NsaWNrLFxyXG4gICAgdHJhY2tGb3JtU3VibWl0LFxyXG4gICAgdHJhY2tEb3dubG9hZCxcclxuICAgIHRyYWNrU2VhcmNoLFxyXG4gICAgdHJhY2tQcm9kdWN0VmlldyxcclxuICAgIHRyYWNrQWRkVG9DYXJ0XHJcbiAgfTtcclxufVxyXG5cclxuLyoqXHJcbiAqIOa7muWKqOi/vei4qiBIb29rXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gdXNlU2Nyb2xsVHJhY2tpbmcodGhyZXNob2xkOiBudW1iZXIgPSA1MCkge1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBsZXQgaGFzVHJhY2tlZCA9IGZhbHNlO1xyXG4gICAgY29uc3QgdHJhY2tpbmdUaHJlc2hvbGQgPSB0aHJlc2hvbGQ7IC8vIOa7muWKqOeZvuWIhuavlOmYiOWAvFxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNjcm9sbCA9ICgpID0+IHtcclxuICAgICAgaWYgKGhhc1RyYWNrZWQpIHJldHVybjtcclxuXHJcbiAgICAgIGNvbnN0IHNjcm9sbFRvcCA9IHdpbmRvdy5wYWdlWU9mZnNldCB8fCBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc2Nyb2xsVG9wO1xyXG4gICAgICBjb25zdCBkb2N1bWVudEhlaWdodCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zY3JvbGxIZWlnaHQgLSB3aW5kb3cuaW5uZXJIZWlnaHQ7XHJcbiAgICAgIGNvbnN0IHNjcm9sbFBlcmNlbnRhZ2UgPSAoc2Nyb2xsVG9wIC8gZG9jdW1lbnRIZWlnaHQpICogMTAwO1xyXG5cclxuICAgICAgaWYgKHNjcm9sbFBlcmNlbnRhZ2UgPj0gdHJhY2tpbmdUaHJlc2hvbGQpIHtcclxuICAgICAgICBhbmFseXRpY3NTZXJ2aWNlLnRyYWNrRXZlbnQoJ3Njcm9sbF90aHJlc2hvbGQnLCB7XHJcbiAgICAgICAgICB0aHJlc2hvbGQ6IHRyYWNraW5nVGhyZXNob2xkLFxyXG4gICAgICAgICAgcGVyY2VudGFnZTogTWF0aC5yb3VuZChzY3JvbGxQZXJjZW50YWdlKVxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGhhc1RyYWNrZWQgPSB0cnVlO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwsIHsgcGFzc2l2ZTogdHJ1ZSB9KTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignc2Nyb2xsJywgaGFuZGxlU2Nyb2xsKTtcclxuICAgIH07XHJcbiAgfSwgW3RocmVzaG9sZF0pO1xyXG59XHJcblxyXG4vKipcclxuICog5YGc55WZ5pe26Ze06L+96LiqIEhvb2tcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiB1c2VUaW1lVHJhY2tpbmcoaW50ZXJ2YWxzOiBudW1iZXJbXSA9IFszMCwgNjAsIDEyMCwgMzAwXSkge1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCB0cmFja2VkSW50ZXJ2YWxzID0gbmV3IFNldDxudW1iZXI+KCk7XHJcbiAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xyXG5cclxuICAgIGNvbnN0IGNoZWNrSW50ZXJ2YWxzID0gKCkgPT4ge1xyXG4gICAgICBjb25zdCBlbGFwc2VkID0gTWF0aC5mbG9vcigoRGF0ZS5ub3coKSAtIHN0YXJ0VGltZSkgLyAxMDAwKTtcclxuICAgICAgXHJcbiAgICAgIGludGVydmFscy5mb3JFYWNoKGludGVydmFsID0+IHtcclxuICAgICAgICBpZiAoZWxhcHNlZCA+PSBpbnRlcnZhbCAmJiAhdHJhY2tlZEludGVydmFscy5oYXMoaW50ZXJ2YWwpKSB7XHJcbiAgICAgICAgICBhbmFseXRpY3NTZXJ2aWNlLnRyYWNrRXZlbnQoJ3RpbWVfb25fcGFnZScsIHtcclxuICAgICAgICAgICAgaW50ZXJ2YWwsXHJcbiAgICAgICAgICAgIGVsYXBzZWRcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgdHJhY2tlZEludGVydmFscy5hZGQoaW50ZXJ2YWwpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IHRpbWVyID0gc2V0SW50ZXJ2YWwoY2hlY2tJbnRlcnZhbHMsIDEwMDApO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKHRpbWVyKTtcclxuICB9LCBbaW50ZXJ2YWxzXSk7XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVBhdGhuYW1lIiwiYW5hbHl0aWNzU2VydmljZSIsInVzZVBhZ2VUcmFja2luZyIsInBhdGhuYW1lIiwidGltZXIiLCJzZXRUaW1lb3V0IiwidHJhY2tQYWdlVmlldyIsImNsZWFyVGltZW91dCIsInVzZUV2ZW50VHJhY2tpbmciLCJ0cmFja0V2ZW50IiwiZXZlbnQiLCJldmVudERhdGEiLCJ0cmFja0NsaWNrIiwiZWxlbWVudE5hbWUiLCJhZGRpdGlvbmFsRGF0YSIsImVsZW1lbnQiLCJ0cmFja0Zvcm1TdWJtaXQiLCJmb3JtTmFtZSIsImZvcm0iLCJ0cmFja0Rvd25sb2FkIiwiZmlsZU5hbWUiLCJmaWxlVHlwZSIsInRyYWNrU2VhcmNoIiwicXVlcnkiLCJyZXN1bHRzQ291bnQiLCJ0cmFja1Byb2R1Y3RWaWV3IiwicHJvZHVjdElkIiwicHJvZHVjdE5hbWUiLCJjYXRlZ29yeSIsInRyYWNrQWRkVG9DYXJ0IiwicXVhbnRpdHkiLCJwcmljZSIsInVzZVNjcm9sbFRyYWNraW5nIiwidGhyZXNob2xkIiwiaGFzVHJhY2tlZCIsInRyYWNraW5nVGhyZXNob2xkIiwiaGFuZGxlU2Nyb2xsIiwic2Nyb2xsVG9wIiwid2luZG93IiwicGFnZVlPZmZzZXQiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImRvY3VtZW50SGVpZ2h0Iiwic2Nyb2xsSGVpZ2h0IiwiaW5uZXJIZWlnaHQiLCJzY3JvbGxQZXJjZW50YWdlIiwicGVyY2VudGFnZSIsIk1hdGgiLCJyb3VuZCIsImFkZEV2ZW50TGlzdGVuZXIiLCJwYXNzaXZlIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInVzZVRpbWVUcmFja2luZyIsImludGVydmFscyIsInRyYWNrZWRJbnRlcnZhbHMiLCJTZXQiLCJzdGFydFRpbWUiLCJEYXRlIiwibm93IiwiY2hlY2tJbnRlcnZhbHMiLCJlbGFwc2VkIiwiZmxvb3IiLCJmb3JFYWNoIiwiaW50ZXJ2YWwiLCJoYXMiLCJhZGQiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAnalytics.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/analytics.ts":
/*!***********************************!*\
  !*** ./src/services/analytics.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\r\n * 用户访问追踪服务\r\n * Analytics Service - 前端访问数据收集和发送\r\n */ class AnalyticsService {\n    constructor(){\n        this.sessionId = this.generateSessionId();\n        this.startTime = Date.now();\n        this.lastPath = \"\";\n        this.deviceInfo = this.getDeviceInfo();\n        this.isTracking = false;\n        // 只在客户端运行\n        if (false) {}\n    }\n    /**\r\n   * 初始化追踪\r\n   */ init() {\n        // 检查是否启用追踪\n        const trackingEnabled = process.env.NEXT_PUBLIC_ANALYTICS_ENABLED !== \"false\";\n        if (!trackingEnabled) {\n            console.log(\"Analytics tracking is disabled\");\n            return;\n        }\n        this.isTracking = true;\n        // 页面加载时记录访问\n        this.trackPageView();\n        // 监听页面变化（SPA路由）\n        this.setupRouteChangeTracking();\n        // 监听页面离开\n        this.setupBeforeUnload();\n        // 监听页面可见性变化\n        this.setupVisibilityChange();\n    }\n    /**\r\n   * 生成会话ID\r\n   */ generateSessionId() {\n        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    }\n    /**\r\n   * 获取设备信息\r\n   */ getDeviceInfo() {\n        if (true) {\n            return {\n                deviceType: \"unknown\",\n                browserName: \"unknown\",\n                browserVersion: \"unknown\",\n                osName: \"unknown\",\n                osVersion: \"unknown\"\n            };\n        }\n        const userAgent = navigator.userAgent;\n        // 检测设备类型\n        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n        const isTablet = /iPad|Android(?=.*Mobile)/i.test(userAgent);\n        const deviceType = isTablet ? \"tablet\" : isMobile ? \"mobile\" : \"desktop\";\n        // 检测浏览器\n        let browserName = \"unknown\";\n        let browserVersion = \"unknown\";\n        if (userAgent.indexOf(\"Chrome\") > -1) {\n            browserName = \"Chrome\";\n            browserVersion = userAgent.match(/Chrome\\/([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Firefox\") > -1) {\n            browserName = \"Firefox\";\n            browserVersion = userAgent.match(/Firefox\\/([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Safari\") > -1) {\n            browserName = \"Safari\";\n            browserVersion = userAgent.match(/Version\\/([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Edge\") > -1) {\n            browserName = \"Edge\";\n            browserVersion = userAgent.match(/Edge\\/([0-9.]+)/)?.[1] || \"unknown\";\n        }\n        // 检测操作系统\n        let osName = \"unknown\";\n        let osVersion = \"unknown\";\n        if (userAgent.indexOf(\"Windows\") > -1) {\n            osName = \"Windows\";\n            osVersion = userAgent.match(/Windows NT ([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Mac\") > -1) {\n            osName = \"macOS\";\n            osVersion = userAgent.match(/Mac OS X ([0-9_]+)/)?.[1]?.replace(/_/g, \".\") || \"unknown\";\n        } else if (userAgent.indexOf(\"Linux\") > -1) {\n            osName = \"Linux\";\n        } else if (userAgent.indexOf(\"Android\") > -1) {\n            osName = \"Android\";\n            osVersion = userAgent.match(/Android ([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"iOS\") > -1) {\n            osName = \"iOS\";\n            osVersion = userAgent.match(/OS ([0-9_]+)/)?.[1]?.replace(/_/g, \".\") || \"unknown\";\n        }\n        return {\n            deviceType,\n            browserName,\n            browserVersion,\n            osName,\n            osVersion\n        };\n    }\n    /**\r\n   * 记录页面访问\r\n   */ trackPageView(customPath) {\n        if (!this.isTracking || \"undefined\" === \"undefined\") return;\n        const currentPath = customPath || window.location.pathname;\n        // 如果路径没有变化且不是首次访问，则不记录\n        if (this.lastPath === currentPath && this.lastPath !== \"\") {\n            return;\n        }\n        // 计算上一页面的停留时间\n        const duration = this.lastPath ? Math.floor((Date.now() - this.startTime) / 1000) : 0;\n        const data = {\n            sessionId: this.sessionId,\n            url: window.location.href,\n            pathname: currentPath,\n            referer: document.referrer || \"\",\n            duration,\n            deviceType: this.deviceInfo.deviceType,\n            browserName: this.deviceInfo.browserName,\n            browserVersion: this.deviceInfo.browserVersion,\n            osName: this.deviceInfo.osName,\n            osVersion: this.deviceInfo.osVersion,\n            screenResolution: `${screen.width}x${screen.height}`,\n            language: navigator.language,\n            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n            event: \"pageview\"\n        };\n        this.sendData(data);\n        // 更新状态\n        this.lastPath = currentPath;\n        this.startTime = Date.now();\n    }\n    /**\r\n   * 记录自定义事件\r\n   */ trackEvent(event, eventData) {\n        if (!this.isTracking || \"undefined\" === \"undefined\") return;\n        const data = {\n            sessionId: this.sessionId,\n            url: window.location.href,\n            pathname: window.location.pathname,\n            referer: document.referrer || \"\",\n            deviceType: this.deviceInfo.deviceType,\n            browserName: this.deviceInfo.browserName,\n            browserVersion: this.deviceInfo.browserVersion,\n            osName: this.deviceInfo.osName,\n            osVersion: this.deviceInfo.osVersion,\n            screenResolution: `${screen.width}x${screen.height}`,\n            language: navigator.language,\n            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n            event,\n            eventData\n        };\n        this.sendData(data);\n    }\n    /**\r\n   * 设置路由变化监听\r\n   */ setupRouteChangeTracking() {\n        // 监听 popstate 事件（浏览器前进后退）\n        window.addEventListener(\"popstate\", ()=>{\n            setTimeout(()=>this.trackPageView(), 100);\n        });\n        // 监听 pushState 和 replaceState（SPA路由变化）\n        const originalPushState = history.pushState;\n        const originalReplaceState = history.replaceState;\n        history.pushState = function(state, title, url) {\n            originalPushState.apply(history, arguments);\n            setTimeout(()=>analyticsService.trackPageView(), 100);\n        };\n        history.replaceState = function(state, title, url) {\n            originalReplaceState.apply(history, arguments);\n            setTimeout(()=>analyticsService.trackPageView(), 100);\n        };\n    }\n    /**\r\n   * 设置页面离开监听\r\n   */ setupBeforeUnload() {\n        window.addEventListener(\"beforeunload\", ()=>{\n            // 记录最后一次停留时间\n            const duration = Math.floor((Date.now() - this.startTime) / 1000);\n            if (duration > 0) {\n                // 使用 sendBeacon 确保数据能发送出去\n                this.sendDataSync({\n                    sessionId: this.sessionId,\n                    url: window.location.href,\n                    pathname: window.location.pathname,\n                    referer: document.referrer || \"\",\n                    duration,\n                    deviceType: this.deviceInfo.deviceType,\n                    browserName: this.deviceInfo.browserName,\n                    browserVersion: this.deviceInfo.browserVersion,\n                    osName: this.deviceInfo.osName,\n                    osVersion: this.deviceInfo.osVersion,\n                    screenResolution: `${screen.width}x${screen.height}`,\n                    language: navigator.language,\n                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                    event: \"pageview\"\n                });\n            }\n        });\n    }\n    /**\r\n   * 设置页面可见性变化监听\r\n   */ setupVisibilityChange() {\n        document.addEventListener(\"visibilitychange\", ()=>{\n            if (document.hidden) {\n                // 页面隐藏时记录停留时间\n                const duration = Math.floor((Date.now() - this.startTime) / 1000);\n                if (duration > 5) {\n                    this.trackEvent(\"page_hidden\", {\n                        duration\n                    });\n                }\n            } else {\n                // 页面重新可见时重新开始计时\n                this.startTime = Date.now();\n            }\n        });\n    }\n    /**\r\n   * 发送数据到服务器\r\n   */ async sendData(data) {\n        try {\n            await fetch(\"/api/analytics/track\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n        } catch (error) {\n            console.warn(\"Analytics tracking failed:\", error);\n        }\n    }\n    /**\r\n   * 同步发送数据（用于页面离开时）\r\n   */ sendDataSync(data) {\n        try {\n            if (navigator.sendBeacon) {\n                navigator.sendBeacon(\"/api/analytics/track\", JSON.stringify(data));\n            }\n        } catch (error) {\n            console.warn(\"Analytics sync tracking failed:\", error);\n        }\n    }\n}\n// 创建全局实例\nconst analyticsService = new AnalyticsService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (analyticsService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/analytics.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/adminAuth.ts":
/*!********************************!*\
  !*** ./src/utils/adminAuth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminHeaders: () => (/* binding */ getAdminHeaders)\n/* harmony export */ });\n/**\r\n * 管理员认证相关工具函数\r\n */ /**\r\n * 获取管理员API请求头\r\n * @returns 包含Content-Type和管理员API密钥的请求头对象\r\n */ function getAdminHeaders() {\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // 获取管理员API密钥\n    let adminApiKey;\n    if (true) {\n        adminApiKey = \"lc_admin_dev_key_2025\" || 0 || 0;\n        console.log(\"Server-side Admin API Key source:\",  true ? \"NEXT_PUBLIC_ADMIN_API_KEY\" : 0);\n    } else {}\n    if (adminApiKey) {\n        headers[\"x-admin-api-key\"] = adminApiKey;\n    }\n    return headers;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/adminAuth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"610e40cc88fd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGMtbWFsbC1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTViYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjYxMGU0MGNjODhmZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/analytics/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/analytics/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\LongChiMall\lc-mall-new\frontend\src\app\admin\analytics\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _components_analytics_AnalyticsTracker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/analytics/AnalyticsTracker */ \"(rsc)/./src/components/analytics/AnalyticsTracker.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"龙驰新材料门户网站\",\n    description: \"专业聚氨酯材料供应商 - 提供优质的聚氨酯原料、助剂和技术支持\",\n    keywords: \"聚氨酯,聚氨酯材料,潜固化剂,催化剂,流平剂,分散剂,龙驰新材料\",\n    authors: [\n        {\n            name: \"广州市龙驰新材料科技有限公司\"\n        }\n    ],\n    creator: \"龙驰新材料\",\n    publisher: \"龙驰新材料\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(process.env.NEXT_PUBLIC_FRONTEND_URL || \"http://localhost:3000\"),\n    openGraph: {\n        type: \"website\",\n        locale: \"zh_CN\",\n        url: process.env.NEXT_PUBLIC_FRONTEND_URL || \"http://localhost:3000\",\n        title: \"龙驰新材料门户网站\",\n        description: \"专业聚氨酯材料供应商 - 提供优质的聚氨酯原料、助剂和技术支持\",\n        siteName: \"龙驰新材料\",\n        images: [\n            {\n                url: \"/images/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"龙驰新材料门户网站\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"龙驰新材料门户网站\",\n        description: \"专业聚氨酯材料供应商\",\n        images: [\n            \"/images/twitter-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    icons: {\n        icon: [\n            {\n                url: \"/favicon/favicon.ico\",\n                sizes: \"any\"\n            },\n            {\n                url: \"/favicon/favicon.svg\",\n                type: \"image/svg+xml\"\n            },\n            {\n                url: \"/favicon/favicon-96x96.png\",\n                sizes: \"96x96\",\n                type: \"image/png\"\n            }\n        ],\n        shortcut: \"/favicon/favicon.ico\",\n        apple: \"/favicon/apple-touch-icon.png\",\n        other: [\n            {\n                rel: \"icon\",\n                url: \"/favicon/web-app-manifest-192x192.png\",\n                sizes: \"192x192\",\n                type: \"image/png\"\n            },\n            {\n                rel: \"icon\",\n                url: \"/favicon/web-app-manifest-512x512.png\",\n                sizes: \"512x512\",\n                type: \"image/png\"\n            }\n        ]\n    },\n    manifest: \"/favicon/site.webmanifest\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.loli.net\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.loli.net/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-sans\",\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_3__.Providers, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_AnalyticsTracker__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 3000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\",\n                                borderRadius: \"8px\",\n                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                            },\n                            success: {\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 87,\n                columnNumber: 20\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 83,\n        columnNumber: 15\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\LongChiMall\lc-mall-new\frontend\src\app\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/analytics/AnalyticsTracker.tsx":
/*!*******************************************************!*\
  !*** ./src/components/analytics/AnalyticsTracker.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\LongChiMall\lc-mall-new\frontend\src\components\analytics\AnalyticsTracker.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/@heroicons","vendor-chunks/date-fns","vendor-chunks/@babel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fanalytics%2Fpage&page=%2Fadmin%2Fanalytics%2Fpage&appPaths=%2Fadmin%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fanalytics%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
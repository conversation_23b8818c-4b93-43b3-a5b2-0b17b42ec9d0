#!/bin/bash
# 快速重启服务脚本 (适用于云服务器直接部署)

echo "=========================================="
echo "    龙驰新材料商城 - 快速重启服务      "
echo "=========================================="

# 停止现有服务
echo "1. 停止现有服务..."
./stop.sh

# 等待进程完全停止
echo "2. 等待进程停止..."
sleep 3

# 强制清理端口
echo "3. 清理端口..."
./kill_ports.sh

# 等待端口释放
sleep 2

# 检查端口是否真正释放
echo "4. 检查端口状态..."
if netstat -tlnp | grep -E ":3000|:5000" > /dev/null; then
    echo "⚠️  端口仍被占用，再次清理..."
    ./kill_ports.sh
    sleep 3
fi

# 只启动后端 (先测试后端API)
echo "5. 启动后端服务..."
cd backend

# 检查node_modules
if [ ! -d "node_modules" ]; then
    echo "安装后端依赖..."
    npm install
fi

# 启动后端
echo "启动后端服务 (端口5000)..."
nohup npm start > ../logs/backend.log 2>&1 &
backend_pid=$!
echo "后端 PID: $backend_pid"
echo $backend_pid > ../logs/backend.pid

# 等待后端启动
echo "等待后端启动..."
sleep 8

# 测试后端
echo "6. 测试后端状态..."
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ 后端启动成功"
    echo "测试API路由..."
    
    # 测试产品API
    if curl -f http://localhost:5000/api/products > /dev/null 2>&1; then
        echo "✅ 产品API正常"
    else
        echo "❌ 产品API异常"
        echo "API响应:"
        curl -s http://localhost:5000/api/products | head -3
    fi
else
    echo "❌ 后端启动失败"
    echo "后端日志:"
    tail -20 ../logs/backend.log
    exit 1
fi

# 启动前端
echo ""
echo "7. 启动前端服务..."
cd ../frontend

# 检查构建
if [ ! -d ".next" ]; then
    echo "构建前端..."
    npm run build
    if [ $? -ne 0 ]; then
        echo "❌ 前端构建失败"
        exit 1
    fi
fi

# 启动前端
echo "启动前端服务 (端口3000)..."
nohup npm start > ../logs/frontend.log 2>&1 &
frontend_pid=$!
echo "前端 PID: $frontend_pid"
echo $frontend_pid > ../logs/frontend.pid

# 等待前端启动
echo "等待前端启动..."
sleep 10

# 测试前端
echo "8. 测试前端状态..."
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端启动成功"
    
    # 测试前端API路由
    echo "测试前端API路由..."
    if curl -f http://localhost:3000/api/videos > /dev/null 2>&1; then
        echo "✅ 前端API路由正常"
    else
        echo "❌ 前端API路由异常"
    fi
else
    echo "❌ 前端启动失败"
    echo "前端日志:"
    tail -20 ../logs/frontend.log
    exit 1
fi

cd ..

echo ""
echo "=========================================="
echo "           ✅ 服务重启完成!              "
echo "=========================================="
echo "后端: http://localhost:5000"
echo "前端: http://localhost:3000"
echo "健康检查: http://localhost:5000/health"
echo ""
echo "快速状态检查: ./check-services.sh"
echo "详细诊断: ./diagnose-api-issue.sh"
echo "查看日志: tail -f logs/backend.log"
echo "=========================================="

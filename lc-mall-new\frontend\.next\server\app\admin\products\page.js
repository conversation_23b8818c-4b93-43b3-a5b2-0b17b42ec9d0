/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/products/page";
exports.ids = ["app/admin/products/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fpage&page=%2Fadmin%2Fproducts%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fpage&page=%2Fadmin%2Fproducts%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'products',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/products/page.tsx */ \"(rsc)/./src/app/admin/products/page.tsx\")), \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/products/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/products/page\",\n        pathname: \"/admin/products\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fpage&page=%2Fadmin%2Fproducts%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CAnalyticsTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CAnalyticsTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/AnalyticsTracker.tsx */ \"(ssr)/./src/components/analytics/AnalyticsTracker.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CAnalyticsTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/products/page.tsx */ \"(ssr)/./src/app/admin/products/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUNMb25nQ2hpTWFsbCU1QyU1Q2xjLW1hbGwtbmV3JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhZG1pbiU1QyU1Q3Byb2R1Y3RzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhLQUF3SCIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvPzgwOGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxnaXRodWJcXFxcTG9uZ0NoaU1hbGxcXFxcbGMtbWFsbC1uZXdcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhZG1pblxcXFxwcm9kdWN0c1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/products/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/products/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _utils_adminAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/adminAuth */ \"(ssr)/./src/utils/adminAuth.ts\");\n/* harmony import */ var _shared_config_productCategories__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/shared/config/productCategories */ \"(ssr)/../shared/config/productCategories.js\");\n/* harmony import */ var _shared_config_productCategories__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_shared_config_productCategories__WEBPACK_IMPORTED_MODULE_4__);\n// frontend/src/app/admin/products/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst ProductManagement = ()=>{\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // 使用共享的分类配置，格式化为筛选器需要的格式\n    const categories = [\n        {\n            id: \"\",\n            name: \"全部分类\"\n        },\n        ..._shared_config_productCategories__WEBPACK_IMPORTED_MODULE_4__.categoryOptions.filter((cat)=>cat.value !== \"\").map((cat)=>({\n                id: cat.value,\n                name: cat.label\n            }))\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProducts();\n    }, [\n        currentPage,\n        selectedCategory,\n        searchTerm\n    ]);\n    const fetchProducts = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: \"10\"\n            });\n            if (selectedCategory) params.append(\"category\", selectedCategory);\n            if (searchTerm) params.append(\"search\", searchTerm);\n            const response = await fetch(`/api/admin/products?${params}`, {\n                headers: {\n                    ...(0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_3__.getAdminHeaders)(),\n                    \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                    \"Pragma\": \"no-cache\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setProducts(data.products || []);\n                setTotalPages(data.pagination?.totalPages || 1);\n            }\n        } catch (error) {\n            console.error(\"获取产品失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeleteProduct = async (productId)=>{\n        if (!confirm(\"确定要删除这个产品吗？\")) return;\n        try {\n            const response = await fetch(`/api/admin/products/${productId}/`, {\n                method: \"DELETE\",\n                headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_3__.getAdminHeaders)()\n            });\n            if (response.ok) {\n                await fetchProducts(); // 重新获取数据\n                alert(\"产品删除成功\");\n            } else {\n                const error = await response.json();\n                alert(`删除失败: ${error.message || \"未知错误\"}`);\n            }\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            alert(\"删除失败\");\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"zh-CN\", {\n            style: \"currency\",\n            currency: \"CNY\"\n        }).format(price);\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            active: {\n                label: \"上架\",\n                color: \"bg-green-100 text-green-800\"\n            },\n            inactive: {\n                label: \"下架\",\n                color: \"bg-red-100 text-red-800\"\n            },\n            draft: {\n                label: \"草稿\",\n                color: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const statusInfo = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusInfo.color}`,\n            children: statusInfo.label\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"产品管理\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex items-center text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/admin\",\n                                                className: \"hover:text-gray-700\",\n                                                children: \"管理后台\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mx-2\",\n                                                children: \"/\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"产品管理\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/admin/products/create\",\n                                className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"-ml-1 mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"添加产品\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 sm:px-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"搜索产品名称...\",\n                                                    className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                    value: selectedCategory,\n                                                    onChange: (e)=>setSelectedCategory(e.target.value),\n                                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category.id,\n                                                            children: category.name\n                                                        }, category.id, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: fetchProducts,\n                                            className: \"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                                            children: \"搜索\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-gray-500\",\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, undefined) : products.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"暂无产品数据\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        className: \"bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"产品信息\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"分类\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"价格\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"库存\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"状态\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"操作\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"                      \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                        children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"hover:bg-gray-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-shrink-0 h-12 w-12\",\n                                                                                    children: product.images && product.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        className: \"h-12 w-12 rounded-md object-cover\",\n                                                                                        src: typeof product.images === \"string\" ? JSON.parse(product.images)[0] : product.images[0],\n                                                                                        alt: product.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                        lineNumber: 231,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"h-12 w-12 rounded-md bg-gray-200 flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400 text-xs\",\n                                                                                            children: \"无图\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                            lineNumber: 238,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                        lineNumber: 237,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 229,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"ml-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                                            children: product.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                            lineNumber: 243,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm text-gray-500\",\n                                                                                            children: [\n                                                                                                \"ID: \",\n                                                                                                product.id\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                            lineNumber: 244,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 242,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 228,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                        children: (0,_shared_config_productCategories__WEBPACK_IMPORTED_MODULE_4__.getCategoryDisplayName)(product.category)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 9\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                        children: formatPrice(product.price)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                        children: product.stockQuantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: getStatusBadge(product.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                    href: `/admin/products/edit/${product.id}`,\n                                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                        className: \"h-5 w-5\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                        lineNumber: 266,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 262,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDeleteProduct(product.id),\n                                                                                    className: \"text-red-600 hover:text-red-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                        className: \"h-5 w-5\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                        lineNumber: 272,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 268,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, product.id, true, {\n                                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: [\n                                                        \"显示第 \",\n                                                        (currentPage - 1) * 10 + 1,\n                                                        \" - \",\n                                                        Math.min(currentPage * 10, products.length),\n                                                        \" 条，共 \",\n                                                        products.length,\n                                                        \" 条\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                                            disabled: currentPage === 1,\n                                                            className: \"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400\",\n                                                            children: \"上一页\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-2 text-sm text-gray-700\",\n                                                            children: [\n                                                                currentPage,\n                                                                \" / \",\n                                                                totalPages\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                                            disabled: currentPage === totalPages,\n                                                            className: \"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400\",\n                                                            children: \"下一页\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductManagement);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/products/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 3000,\n                    style: {\n                        background: \"#fff\",\n                        color: \"#333\",\n                        borderRadius: \"8px\",\n                        boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                    },\n                    success: {\n                        iconTheme: {\n                            primary: \"#22c55e\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/analytics/AnalyticsTracker.tsx":
/*!*******************************************************!*\
  !*** ./src/components/analytics/AnalyticsTracker.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsTracker)\n/* harmony export */ });\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/hooks/useAnalytics */ \"(ssr)/./src/hooks/useAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ /**\r\n * 访问追踪组件\r\n * Analytics Tracker - 处理页面访问追踪\r\n */ \nfunction AnalyticsTracker() {\n    // 使用页面追踪Hook\n    (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_0__.usePageTracking)();\n    // 这个组件不渲染任何内容\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hbmFseXRpY3MvQW5hbHl0aWNzVHJhY2tlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRUE7OztDQUdDLEdBRXNEO0FBRXhDLFNBQVNDO0lBQ3RCLGFBQWE7SUFDYkQsb0VBQWVBO0lBRWYsY0FBYztJQUNkLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9hbmFseXRpY3MvQW5hbHl0aWNzVHJhY2tlci50c3g/ZTc5OCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG4vKipcclxuICog6K6/6Zeu6L+96Liq57uE5Lu2XHJcbiAqIEFuYWx5dGljcyBUcmFja2VyIC0g5aSE55CG6aG16Z2i6K6/6Zeu6L+96LiqXHJcbiAqL1xyXG5cclxuaW1wb3J0IHsgdXNlUGFnZVRyYWNraW5nIH0gZnJvbSAnQC9ob29rcy91c2VBbmFseXRpY3MnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQW5hbHl0aWNzVHJhY2tlcigpIHtcclxuICAvLyDkvb/nlKjpobXpnaLov73ouKpIb29rXHJcbiAgdXNlUGFnZVRyYWNraW5nKCk7XHJcblxyXG4gIC8vIOi/meS4que7hOS7tuS4jea4suafk+S7u+S9leWGheWuuVxyXG4gIHJldHVybiBudWxsO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VQYWdlVHJhY2tpbmciLCJBbmFseXRpY3NUcmFja2VyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/analytics/AnalyticsTracker.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAnalytics.ts":
/*!***********************************!*\
  !*** ./src/hooks/useAnalytics.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEventTracking: () => (/* binding */ useEventTracking),\n/* harmony export */   usePageTracking: () => (/* binding */ usePageTracking),\n/* harmony export */   useScrollTracking: () => (/* binding */ useScrollTracking),\n/* harmony export */   useTimeTracking: () => (/* binding */ useTimeTracking)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_analytics__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/analytics */ \"(ssr)/./src/services/analytics.ts\");\n/**\r\n * 用户访问追踪 Hook\r\n * Use Analytics Hook - React Hook for analytics tracking\r\n */ \n\n\n/**\r\n * 页面访问追踪 Hook\r\n */ function usePageTracking() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // 延迟执行以确保页面完全加载\n        const timer = setTimeout(()=>{\n            _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackPageView(pathname);\n        }, 100);\n        return ()=>clearTimeout(timer);\n    }, [\n        pathname\n    ]);\n}\n/**\r\n * 事件追踪 Hook\r\n */ function useEventTracking() {\n    const trackEvent = (event, eventData)=>{\n        _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackEvent(event, eventData);\n    };\n    const trackClick = (elementName, additionalData)=>{\n        trackEvent(\"click\", {\n            element: elementName,\n            ...additionalData\n        });\n    };\n    const trackFormSubmit = (formName, additionalData)=>{\n        trackEvent(\"form_submit\", {\n            form: formName,\n            ...additionalData\n        });\n    };\n    const trackDownload = (fileName, fileType)=>{\n        trackEvent(\"download\", {\n            fileName,\n            fileType\n        });\n    };\n    const trackSearch = (query, resultsCount)=>{\n        trackEvent(\"search\", {\n            query,\n            resultsCount\n        });\n    };\n    const trackProductView = (productId, productName, category)=>{\n        trackEvent(\"product_view\", {\n            productId,\n            productName,\n            category\n        });\n    };\n    const trackAddToCart = (productId, productName, quantity, price)=>{\n        trackEvent(\"add_to_cart\", {\n            productId,\n            productName,\n            quantity,\n            price\n        });\n    };\n    return {\n        trackEvent,\n        trackClick,\n        trackFormSubmit,\n        trackDownload,\n        trackSearch,\n        trackProductView,\n        trackAddToCart\n    };\n}\n/**\r\n * 滚动追踪 Hook\r\n */ function useScrollTracking(threshold = 50) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let hasTracked = false;\n        const trackingThreshold = threshold; // 滚动百分比阈值\n        const handleScroll = ()=>{\n            if (hasTracked) return;\n            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n            const documentHeight = document.documentElement.scrollHeight - window.innerHeight;\n            const scrollPercentage = scrollTop / documentHeight * 100;\n            if (scrollPercentage >= trackingThreshold) {\n                _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackEvent(\"scroll_threshold\", {\n                    threshold: trackingThreshold,\n                    percentage: Math.round(scrollPercentage)\n                });\n                hasTracked = true;\n            }\n        };\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        threshold\n    ]);\n}\n/**\r\n * 停留时间追踪 Hook\r\n */ function useTimeTracking(intervals = [\n    30,\n    60,\n    120,\n    300\n]) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const trackedIntervals = new Set();\n        const startTime = Date.now();\n        const checkIntervals = ()=>{\n            const elapsed = Math.floor((Date.now() - startTime) / 1000);\n            intervals.forEach((interval)=>{\n                if (elapsed >= interval && !trackedIntervals.has(interval)) {\n                    _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackEvent(\"time_on_page\", {\n                        interval,\n                        elapsed\n                    });\n                    trackedIntervals.add(interval);\n                }\n            });\n        };\n        const timer = setInterval(checkIntervals, 1000);\n        return ()=>clearInterval(timer);\n    }, [\n        intervals\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAnalytics.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/analytics.ts":
/*!***********************************!*\
  !*** ./src/services/analytics.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\r\n * 用户访问追踪服务\r\n * Analytics Service - 前端访问数据收集和发送\r\n */ class AnalyticsService {\n    constructor(){\n        this.sessionId = this.generateSessionId();\n        this.startTime = Date.now();\n        this.lastPath = \"\";\n        this.deviceInfo = this.getDeviceInfo();\n        this.isTracking = false;\n        // 只在客户端运行\n        if (false) {}\n    }\n    /**\r\n   * 初始化追踪\r\n   */ init() {\n        // 检查是否启用追踪\n        const trackingEnabled = process.env.NEXT_PUBLIC_ANALYTICS_ENABLED !== \"false\";\n        if (!trackingEnabled) {\n            console.log(\"Analytics tracking is disabled\");\n            return;\n        }\n        this.isTracking = true;\n        // 页面加载时记录访问\n        this.trackPageView();\n        // 监听页面变化（SPA路由）\n        this.setupRouteChangeTracking();\n        // 监听页面离开\n        this.setupBeforeUnload();\n        // 监听页面可见性变化\n        this.setupVisibilityChange();\n    }\n    /**\r\n   * 生成会话ID\r\n   */ generateSessionId() {\n        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    }\n    /**\r\n   * 获取设备信息\r\n   */ getDeviceInfo() {\n        if (true) {\n            return {\n                deviceType: \"unknown\",\n                browserName: \"unknown\",\n                browserVersion: \"unknown\",\n                osName: \"unknown\",\n                osVersion: \"unknown\"\n            };\n        }\n        const userAgent = navigator.userAgent;\n        // 检测设备类型\n        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n        const isTablet = /iPad|Android(?=.*Mobile)/i.test(userAgent);\n        const deviceType = isTablet ? \"tablet\" : isMobile ? \"mobile\" : \"desktop\";\n        // 检测浏览器\n        let browserName = \"unknown\";\n        let browserVersion = \"unknown\";\n        if (userAgent.indexOf(\"Chrome\") > -1) {\n            browserName = \"Chrome\";\n            browserVersion = userAgent.match(/Chrome\\/([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Firefox\") > -1) {\n            browserName = \"Firefox\";\n            browserVersion = userAgent.match(/Firefox\\/([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Safari\") > -1) {\n            browserName = \"Safari\";\n            browserVersion = userAgent.match(/Version\\/([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Edge\") > -1) {\n            browserName = \"Edge\";\n            browserVersion = userAgent.match(/Edge\\/([0-9.]+)/)?.[1] || \"unknown\";\n        }\n        // 检测操作系统\n        let osName = \"unknown\";\n        let osVersion = \"unknown\";\n        if (userAgent.indexOf(\"Windows\") > -1) {\n            osName = \"Windows\";\n            osVersion = userAgent.match(/Windows NT ([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Mac\") > -1) {\n            osName = \"macOS\";\n            osVersion = userAgent.match(/Mac OS X ([0-9_]+)/)?.[1]?.replace(/_/g, \".\") || \"unknown\";\n        } else if (userAgent.indexOf(\"Linux\") > -1) {\n            osName = \"Linux\";\n        } else if (userAgent.indexOf(\"Android\") > -1) {\n            osName = \"Android\";\n            osVersion = userAgent.match(/Android ([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"iOS\") > -1) {\n            osName = \"iOS\";\n            osVersion = userAgent.match(/OS ([0-9_]+)/)?.[1]?.replace(/_/g, \".\") || \"unknown\";\n        }\n        return {\n            deviceType,\n            browserName,\n            browserVersion,\n            osName,\n            osVersion\n        };\n    }\n    /**\r\n   * 记录页面访问\r\n   */ trackPageView(customPath) {\n        if (!this.isTracking || \"undefined\" === \"undefined\") return;\n        const currentPath = customPath || window.location.pathname;\n        // 如果路径没有变化且不是首次访问，则不记录\n        if (this.lastPath === currentPath && this.lastPath !== \"\") {\n            return;\n        }\n        // 计算上一页面的停留时间\n        const duration = this.lastPath ? Math.floor((Date.now() - this.startTime) / 1000) : 0;\n        const data = {\n            sessionId: this.sessionId,\n            url: window.location.href,\n            pathname: currentPath,\n            referer: document.referrer || \"\",\n            duration,\n            deviceType: this.deviceInfo.deviceType,\n            browserName: this.deviceInfo.browserName,\n            browserVersion: this.deviceInfo.browserVersion,\n            osName: this.deviceInfo.osName,\n            osVersion: this.deviceInfo.osVersion,\n            screenResolution: `${screen.width}x${screen.height}`,\n            language: navigator.language,\n            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n            event: \"pageview\"\n        };\n        this.sendData(data);\n        // 更新状态\n        this.lastPath = currentPath;\n        this.startTime = Date.now();\n    }\n    /**\r\n   * 记录自定义事件\r\n   */ trackEvent(event, eventData) {\n        if (!this.isTracking || \"undefined\" === \"undefined\") return;\n        const data = {\n            sessionId: this.sessionId,\n            url: window.location.href,\n            pathname: window.location.pathname,\n            referer: document.referrer || \"\",\n            deviceType: this.deviceInfo.deviceType,\n            browserName: this.deviceInfo.browserName,\n            browserVersion: this.deviceInfo.browserVersion,\n            osName: this.deviceInfo.osName,\n            osVersion: this.deviceInfo.osVersion,\n            screenResolution: `${screen.width}x${screen.height}`,\n            language: navigator.language,\n            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n            event,\n            eventData\n        };\n        this.sendData(data);\n    }\n    /**\r\n   * 设置路由变化监听\r\n   */ setupRouteChangeTracking() {\n        // 监听 popstate 事件（浏览器前进后退）\n        window.addEventListener(\"popstate\", ()=>{\n            setTimeout(()=>this.trackPageView(), 100);\n        });\n        // 监听 pushState 和 replaceState（SPA路由变化）\n        const originalPushState = history.pushState;\n        const originalReplaceState = history.replaceState;\n        history.pushState = function(state, title, url) {\n            originalPushState.apply(history, arguments);\n            setTimeout(()=>analyticsService.trackPageView(), 100);\n        };\n        history.replaceState = function(state, title, url) {\n            originalReplaceState.apply(history, arguments);\n            setTimeout(()=>analyticsService.trackPageView(), 100);\n        };\n    }\n    /**\r\n   * 设置页面离开监听\r\n   */ setupBeforeUnload() {\n        window.addEventListener(\"beforeunload\", ()=>{\n            // 记录最后一次停留时间\n            const duration = Math.floor((Date.now() - this.startTime) / 1000);\n            if (duration > 0) {\n                // 使用 sendBeacon 确保数据能发送出去\n                this.sendDataSync({\n                    sessionId: this.sessionId,\n                    url: window.location.href,\n                    pathname: window.location.pathname,\n                    referer: document.referrer || \"\",\n                    duration,\n                    deviceType: this.deviceInfo.deviceType,\n                    browserName: this.deviceInfo.browserName,\n                    browserVersion: this.deviceInfo.browserVersion,\n                    osName: this.deviceInfo.osName,\n                    osVersion: this.deviceInfo.osVersion,\n                    screenResolution: `${screen.width}x${screen.height}`,\n                    language: navigator.language,\n                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                    event: \"pageview\"\n                });\n            }\n        });\n    }\n    /**\r\n   * 设置页面可见性变化监听\r\n   */ setupVisibilityChange() {\n        document.addEventListener(\"visibilitychange\", ()=>{\n            if (document.hidden) {\n                // 页面隐藏时记录停留时间\n                const duration = Math.floor((Date.now() - this.startTime) / 1000);\n                if (duration > 5) {\n                    this.trackEvent(\"page_hidden\", {\n                        duration\n                    });\n                }\n            } else {\n                // 页面重新可见时重新开始计时\n                this.startTime = Date.now();\n            }\n        });\n    }\n    /**\r\n   * 发送数据到服务器\r\n   */ async sendData(data) {\n        try {\n            await fetch(\"/api/analytics/track\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n        } catch (error) {\n            console.warn(\"Analytics tracking failed:\", error);\n        }\n    }\n    /**\r\n   * 同步发送数据（用于页面离开时）\r\n   */ sendDataSync(data) {\n        try {\n            if (navigator.sendBeacon) {\n                navigator.sendBeacon(\"/api/analytics/track\", JSON.stringify(data));\n            }\n        } catch (error) {\n            console.warn(\"Analytics sync tracking failed:\", error);\n        }\n    }\n}\n// 创建全局实例\nconst analyticsService = new AnalyticsService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (analyticsService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/analytics.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/adminAuth.ts":
/*!********************************!*\
  !*** ./src/utils/adminAuth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminHeaders: () => (/* binding */ getAdminHeaders)\n/* harmony export */ });\n/**\r\n * 管理员认证相关工具函数\r\n */ /**\r\n * 获取管理员API请求头\r\n * @returns 包含Content-Type和管理员API密钥的请求头对象\r\n */ function getAdminHeaders() {\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // 获取管理员API密钥\n    let adminApiKey;\n    if (true) {\n        adminApiKey = \"lc_admin_dev_key_2025\" || 0 || 0;\n        console.log(\"Server-side Admin API Key source:\",  true ? \"NEXT_PUBLIC_ADMIN_API_KEY\" : 0);\n    } else {}\n    if (adminApiKey) {\n        headers[\"x-admin-api-key\"] = adminApiKey;\n    }\n    return headers;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/adminAuth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"610e40cc88fd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGMtbWFsbC1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTViYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjYxMGU0MGNjODhmZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/products/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/products/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\github\\LongChiMall\\lc-mall-new\\frontend\\src\\app\\admin\\products\\page.tsx#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/admin/products/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _components_analytics_AnalyticsTracker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/analytics/AnalyticsTracker */ \"(rsc)/./src/components/analytics/AnalyticsTracker.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"龙驰新材料门户网站\",\n    description: \"专业聚氨酯材料供应商 - 提供优质的聚氨酯原料、助剂和技术支持\",\n    keywords: \"聚氨酯,聚氨酯材料,潜固化剂,催化剂,流平剂,分散剂,龙驰新材料\",\n    authors: [\n        {\n            name: \"广州市龙驰新材料科技有限公司\"\n        }\n    ],\n    creator: \"龙驰新材料\",\n    publisher: \"龙驰新材料\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(process.env.NEXT_PUBLIC_FRONTEND_URL || \"http://localhost:3000\"),\n    openGraph: {\n        type: \"website\",\n        locale: \"zh_CN\",\n        url: process.env.NEXT_PUBLIC_FRONTEND_URL || \"http://localhost:3000\",\n        title: \"龙驰新材料门户网站\",\n        description: \"专业聚氨酯材料供应商 - 提供优质的聚氨酯原料、助剂和技术支持\",\n        siteName: \"龙驰新材料\",\n        images: [\n            {\n                url: \"/images/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"龙驰新材料门户网站\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"龙驰新材料门户网站\",\n        description: \"专业聚氨酯材料供应商\",\n        images: [\n            \"/images/twitter-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    icons: {\n        icon: [\n            {\n                url: \"/favicon/favicon.ico\",\n                sizes: \"any\"\n            },\n            {\n                url: \"/favicon/favicon.svg\",\n                type: \"image/svg+xml\"\n            },\n            {\n                url: \"/favicon/favicon-96x96.png\",\n                sizes: \"96x96\",\n                type: \"image/png\"\n            }\n        ],\n        shortcut: \"/favicon/favicon.ico\",\n        apple: \"/favicon/apple-touch-icon.png\",\n        other: [\n            {\n                rel: \"icon\",\n                url: \"/favicon/web-app-manifest-192x192.png\",\n                sizes: \"192x192\",\n                type: \"image/png\"\n            },\n            {\n                rel: \"icon\",\n                url: \"/favicon/web-app-manifest-512x512.png\",\n                sizes: \"512x512\",\n                type: \"image/png\"\n            }\n        ]\n    },\n    manifest: \"/favicon/site.webmanifest\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.loli.net\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.loli.net/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-sans\",\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_3__.Providers, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_AnalyticsTracker__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 3000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\",\n                                borderRadius: \"8px\",\n                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                            },\n                            success: {\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 87,\n                columnNumber: 20\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 83,\n        columnNumber: 15\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\LongChiMall\lc-mall-new\frontend\src\app\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/analytics/AnalyticsTracker.tsx":
/*!*******************************************************!*\
  !*** ./src/components/analytics/AnalyticsTracker.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\LongChiMall\lc-mall-new\frontend\src\components\analytics\AnalyticsTracker.tsx#default`));


/***/ }),

/***/ "(ssr)/../shared/config/productCategories.js":
/*!*********************************************!*\
  !*** ../shared/config/productCategories.js ***!
  \*********************************************/
/***/ ((module, exports) => {

"use strict";
eval("/**\r\n * 产品分类配置 - 前后端共享\r\n * 包含分类映射、分类选项、分类显示名称等配置\r\n */ // 中文分类名称到英文分类值的映射\n\nconst categoryMapping = {\n    \"样品\": \"samples\",\n    \"潜固化剂\": \"latent-curing-agents\",\n    \"催化剂\": \"catalysts\",\n    \"分散剂\": \"dispersants\",\n    \"流平剂\": \"leveling-agents\",\n    \"助剂\": \"additives\",\n    \"原料\": \"raw-materials\",\n    \"附着力促进剂\": \"adhesion-promoters\",\n    \"固化剂\": \"curing-agents\",\n    \"设备\": \"equipment\",\n    \"扩链剂\": \"chain-extenders\",\n    \"消泡剂\": \"defoamers\",\n    \"生物基树脂\": \"bio-based-resins\",\n    \"粉体助剂\": \"powder-additives\",\n    \"防沉剂\": \"anti-settling-agents\"\n};\n// 分类选项配置（用于前端下拉选择器）\nconst categoryOptions = [\n    {\n        value: \"\",\n        label: \"请选择分类\"\n    },\n    {\n        value: \"catalysts\",\n        label: \"催化剂\"\n    },\n    {\n        value: \"curing-agents\",\n        label: \"固化剂\"\n    },\n    {\n        value: \"adhesion-promoters\",\n        label: \"附着力促进剂\"\n    },\n    {\n        value: \"dispersants\",\n        label: \"分散剂\"\n    },\n    {\n        value: \"chain-extenders\",\n        label: \"扩链剂\"\n    },\n    {\n        value: \"leveling-agents\",\n        label: \"流平剂\"\n    },\n    {\n        value: \"defoamers\",\n        label: \"消泡剂\"\n    },\n    {\n        value: \"latent-curing-agents\",\n        label: \"潜固化剂\"\n    },\n    {\n        value: \"bio-based-resins\",\n        label: \"生物基树脂\"\n    },\n    {\n        value: \"powder-additives\",\n        label: \"粉体助剂\"\n    },\n    {\n        value: \"anti-settling-agents\",\n        label: \"防沉剂\"\n    },\n    {\n        value: \"additives\",\n        label: \"助剂\"\n    },\n    {\n        value: \"raw-materials\",\n        label: \"原材料\"\n    },\n    {\n        value: \"equipment\",\n        label: \"设备\"\n    },\n    {\n        value: \"samples\",\n        label: \"样品\"\n    }\n];\n// 英文分类值到中文显示名称的映射\nconst categoryDisplayNames = {\n    \"catalysts\": \"催化剂\",\n    \"curing-agents\": \"固化剂\",\n    \"adhesion-promoters\": \"附着力促进剂\",\n    \"dispersants\": \"分散剂\",\n    \"chain-extenders\": \"扩链剂\",\n    \"leveling-agents\": \"流平剂\",\n    \"defoamers\": \"消泡剂\",\n    \"latent-curing-agents\": \"潜固化剂\",\n    \"bio-based-resins\": \"生物基树脂\",\n    \"powder-additives\": \"粉体助剂\",\n    \"anti-settling-agents\": \"防沉剂\",\n    \"additives\": \"助剂\",\n    \"raw-materials\": \"原材料\",\n    \"equipment\": \"设备\",\n    \"samples\": \"样品\"\n};\n// 产品状态选项\nconst statusOptions = [\n    {\n        value: \"active\",\n        label: \"上架\"\n    },\n    {\n        value: \"inactive\",\n        label: \"下架\"\n    },\n    {\n        value: \"draft\",\n        label: \"草稿\"\n    }\n];\n// 计量单位选项\nconst unitOptions = [\n    {\n        value: \"吨\",\n        label: \"吨\"\n    },\n    {\n        value: \"公斤\",\n        label: \"公斤\"\n    },\n    {\n        value: \"kg\",\n        label: \"kg\"\n    },\n    {\n        value: \"升\",\n        label: \"升\"\n    },\n    {\n        value: \"桶\",\n        label: \"桶\"\n    },\n    {\n        value: \"个\",\n        label: \"个\"\n    },\n    {\n        value: \"件\",\n        label: \"件\"\n    }\n];\n// 获取分类显示名称\nconst getCategoryDisplayName = (categoryValue)=>{\n    return categoryDisplayNames[categoryValue] || categoryValue;\n};\n// 根据中文分类名称获取英文分类值\nconst getCategoryValue = (chineseName)=>{\n    return categoryMapping[chineseName] || \"raw-materials\";\n};\n// 验证分类值是否有效\nconst isValidCategory = (categoryValue)=>{\n    return Object.values(categoryMapping).includes(categoryValue) || categoryOptions.some((option)=>option.value === categoryValue);\n};\n// 双重导出，同时支持 ESM 和 CommonJS\nconst exportData = {\n    categoryMapping,\n    categoryOptions,\n    categoryDisplayNames,\n    statusOptions,\n    unitOptions,\n    getCategoryDisplayName,\n    getCategoryValue,\n    isValidCategory\n};\n// CommonJS 导出\nmodule.exports = exportData;\n// 为了支持 ES6 模块导入（Next.js）\nif (false) {}\n// 为了支持 ES6 模块导入\nif (true) {\n    Object.defineProperty(exports, \"__esModule\", ({\n        value: true\n    }));\n    Object.keys(exportData).forEach((key)=>{\n        exports[key] = exportData[key];\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../shared/config/productCategories.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons","vendor-chunks/react-hot-toast","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fpage&page=%2Fadmin%2Fproducts%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
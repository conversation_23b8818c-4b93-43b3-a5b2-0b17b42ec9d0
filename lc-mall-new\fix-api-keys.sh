#!/bin/bash
# 修复云服务器API密钥不匹配问题的快速部署脚本

echo "=========================================="
echo "  LC Mall - API Key 修复部署脚本        "
echo "=========================================="

# 设置环境变量
export NODE_ENV=production

# 获取当前目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "当前目录: $SCRIPT_DIR"

# 检查.env.production文件
if [ ! -f ".env.production" ]; then
    echo "❌ 错误: .env.production 文件不存在"
    exit 1
fi

# 显示API密钥配置
echo "检查API密钥配置..."
echo "后端API密钥:"
grep "ADMIN_API_KEY=" .env.production | head -1
echo "前端API密钥:"
grep "NEXT_PUBLIC_ADMIN_API_KEY=" frontend/.env.production | head -1

# 进入前端目录
cd frontend || { echo "❌ 错误: 无法进入frontend目录"; exit 1; }

echo ""
echo "=========================================="
echo "重新构建前端..."
echo "=========================================="

# 清理现有构建
echo "清理现有构建文件..."
rm -rf .next node_modules/.cache

# 重新构建
echo "重新构建前端应用..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

echo "✅ 前端构建成功"

# 重启前端服务（如果正在运行）
echo ""
echo "检查并重启前端服务..."

# 检查是否有前端进程在运行
frontend_pid=$(pgrep -f "next start" || pgrep -f "npm.*start" || echo "")

if [ ! -z "$frontend_pid" ]; then
    echo "发现运行中的前端进程: $frontend_pid"
    echo "停止现有前端服务..."
    kill $frontend_pid
    sleep 3
    
    # 强制杀死如果还在运行
    if kill -0 $frontend_pid 2>/dev/null; then
        echo "强制停止前端服务..."
        kill -9 $frontend_pid
        sleep 2
    fi
fi

# 启动新的前端服务
echo "启动前端服务..."
export NEXT_PUBLIC_API_URL=http://gdlongchi.cn:5000/api
export NEXT_PUBLIC_ADMIN_API_KEY=lc_admin_2025_secure_key_prod

nohup npm start > ../logs/frontend.log 2>&1 &
frontend_pid=$!

echo "前端服务已启动，PID: $frontend_pid"
echo $frontend_pid > ../logs/frontend.pid

# 等待服务启动
echo "等待前端服务启动..."
sleep 8

# 检查服务状态
if ps -p $frontend_pid > /dev/null; then
    echo "✅ 前端服务启动成功"
    
    # 测试服务响应
    echo "测试前端服务..."
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ 前端服务响应正常"
    else
        echo "⚠️  前端服务启动但无响应，请检查日志"
    fi
else
    echo "❌ 前端服务启动失败"
    echo "前端日志:"
    tail -20 ../logs/frontend.log
    exit 1
fi

# 测试图片上传API
echo ""
echo "测试图片上传API..."
api_response=$(curl -s -X GET "http://localhost:3000/api/admin/upload/image" -H "x-admin-api-key: lc_admin_2025_secure_key_prod")

if echo "$api_response" | grep -q "success.*true"; then
    echo "✅ 图片上传API测试通过"
else
    echo "⚠️  图片上传API测试失败"
    echo "响应: $api_response"
fi

echo ""
echo "=========================================="
echo "✅ API密钥修复部署完成！"
echo "=========================================="
echo ""
echo "服务信息:"
echo "  - 前端PID: $frontend_pid"
echo "  - 前端URL: http://localhost:3000"
echo "  - 管理后台: http://localhost:3000/admin"
echo ""
echo "日志文件:"
echo "  - 前端日志: logs/frontend.log"
echo ""
echo "如果图片上传仍有问题，请检查:"
echo "1. 后端服务是否正常运行"
echo "2. 后端images目录权限 (chmod 755 backend/public/images)"
echo "3. API密钥是否完全匹配"
echo ""
echo "测试图片上传:"
echo "  访问 http://localhost:3000/admin/products/create"
echo "  尝试上传产品图片"

# 用户访问记录和后台管理系统

## 系统概述

本系统为龙驰新材料电商平台实现了完整的用户访问记录和后台管理功能，包括：

### 功能特性

#### 前端访问追踪
- ✅ 自动页面访问记录
- ✅ 用户会话追踪
- ✅ 设备信息收集（桌面/移动端、浏览器、操作系统）
- ✅ 页面停留时间统计
- ✅ 产品查看和购物车事件追踪
- ✅ 滚动行为追踪
- ✅ 地理位置信息（基于IP）

#### 后端数据处理
- ✅ 高性能Redis数据存储
- ✅ 自动数据索引和分类
- ✅ 访问记录去重和防刷
- ✅ 定时数据清理（保留30天）
- ✅ 完整的REST API接口

#### 后台管理界面
- ✅ 实时访问统计仪表板
- ✅ 访问记录列表（支持分页、筛选）
- ✅ 数据可视化图表
- ✅ 热门页面分析
- ✅ 设备和浏览器分布统计
- ✅ 地理位置分析
- ✅ CSV数据导出功能

## 技术架构

### 前端技术栈
- **框架**: Next.js 14 (React 18)
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **图标**: Heroicons
- **日期处理**: date-fns
- **HTTP请求**: Fetch API

### 后端技术栈
- **运行时**: Node.js
- **框架**: Express.js
- **数据库**: Redis
- **认证**: API Key认证
- **限流**: express-rate-limit
- **日志**: Winston
- **定时任务**: node-cron

### 数据存储结构
```
Redis Keys:
├── analytics:{id}                    # 访问记录详情
├── analytics:date:{date}             # 日期索引
├── analytics:ip:{ip}:{date}          # IP去重索引
├── analytics:session:{sessionId}     # 会话索引
└── analytics:url:{pathname}:{date}   # URL索引
```

## 部署说明

### 1. 环境要求
- Node.js 18+
- Redis 6+
- PM2 (可选，用于进程管理)
- Nginx (生产环境)

### 2. 安装部署

#### 自动部署（推荐）
```bash
# Linux/macOS
chmod +x deploy-analytics.sh
./deploy-analytics.sh

# Windows
deploy-analytics.bat
```

#### 手动部署
```bash
# 1. 安装依赖
cd backend && npm install
cd ../frontend && npm install

# 2. 构建前端
cd frontend && npm run build

# 3. 启动服务
cd ../backend && npm start
cd ../frontend && npm start
```

### 3. 环境变量配置

#### 后端 (.env.production)
```bash
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# API认证
ADMIN_API_KEY=lc_admin_2025_secure_key_prod

# 服务配置
PORT=5000
NODE_ENV=production
```

#### 前端 (.env.production)
```bash
# API配置
NEXT_PUBLIC_API_URL=http://gdlongchi.cn:5000/api
NEXT_PUBLIC_ADMIN_API_KEY=lc_admin_2025_secure_key_prod

# Analytics配置
NEXT_PUBLIC_ANALYTICS_ENABLED=true
```

## API接口文档

### 公共接口

#### 记录访问
```http
POST /api/analytics/track
Content-Type: application/json

{
  "sessionId": "session-123",
  "url": "https://example.com/page",
  "pathname": "/page",
  "referer": "https://example.com/",
  "duration": 30,
  "deviceType": "desktop",
  "browserName": "Chrome",
  "browserVersion": "120.0.0",
  "osName": "Windows",
  "osVersion": "11",
  "screenResolution": "1920x1080",
  "language": "zh-CN",
  "timezone": "Asia/Shanghai",
  "event": "pageview"
}
```

### 管理员接口（需要认证）

#### 获取统计数据
```http
GET /api/analytics/admin/stats?startDate=2025-01-01&endDate=2025-01-31
X-Admin-Key: your-admin-api-key
```

#### 获取访问记录
```http
GET /api/analytics/admin/records?startDate=2025-01-01&endDate=2025-01-31&limit=50&offset=0
X-Admin-Key: your-admin-api-key
```

#### 获取实时统计
```http
GET /api/analytics/admin/realtime
X-Admin-Key: your-admin-api-key
```

#### 导出数据
```http
GET /api/analytics/admin/export?startDate=2025-01-01&endDate=2025-01-31&format=csv
X-Admin-Key: your-admin-api-key
```

## 使用说明

### 1. 访问后台管理
访问地址：`http://gdlongchi.cn/admin/analytics`

### 2. 数据查看
- **实时统计**: 查看最近24小时的访问数据
- **历史统计**: 选择日期范围查看历史数据
- **热门页面**: 查看访问量最高的页面
- **设备分析**: 查看访客设备和浏览器分布
- **地理分析**: 查看访客地理位置分布

### 3. 数据导出
点击"导出数据"按钮可导出选定时间范围的CSV格式数据，包含所有访问记录详情。

### 4. 系统测试
```bash
# 运行测试脚本
node test-analytics-system.js
```

## 性能优化

### 1. 数据存储优化
- 使用Redis内存数据库，读写速度快
- 自动索引和分类，查询效率高
- 定时清理过期数据，控制存储空间

### 2. 请求优化
- 客户端请求限流（30次/分钟）
- 管理员请求限流（100次/分钟）
- 批量数据处理，减少数据库查询

### 3. 前端优化
- 非阻塞式数据发送
- 页面离开时使用sendBeacon确保数据发送
- 防抖和节流，避免重复请求

## 隐私和合规

### 1. 数据收集原则
- 仅收集必要的访问数据
- 不收集个人敏感信息
- IP地址仅用于地理统计

### 2. 数据保护
- 数据加密存储
- 自动过期删除（30天）
- 访问权限控制

### 3. 用户权利
- 用户可通过环境变量关闭追踪
- 支持数据删除请求
- 透明的数据使用说明

## 故障排除

### 1. 常见问题

#### 访问追踪不工作
```bash
# 检查环境变量
echo $NEXT_PUBLIC_ANALYTICS_ENABLED

# 检查API连接
curl -X POST http://localhost:5000/api/analytics/track \
  -H "Content-Type: application/json" \
  -d '{"sessionId":"test","url":"http://test.com","pathname":"/test","event":"pageview"}'
```

#### 后台数据不显示
```bash
# 检查API认证
curl -H "X-Admin-Key: your-api-key" \
  http://localhost:5000/api/analytics/admin/realtime
```

#### Redis连接问题
```bash
# 检查Redis状态
redis-cli ping

# 检查Redis数据
redis-cli keys "analytics:*"
```

### 2. 日志查看
```bash
# 后端日志
pm2 logs lc-mall-backend

# 前端日志
pm2 logs lc-mall-frontend

# Nginx日志
sudo tail -f /var/log/nginx/error.log
```

### 3. 性能监控
```bash
# 检查服务状态
pm2 status

# 检查系统资源
top
free -h
df -h

# 检查Redis状态
redis-cli info memory
```

## 扩展功能

### 1. 计划中的功能
- [ ] 用户行为漏斗分析
- [ ] A/B测试支持
- [ ] 自定义事件追踪
- [ ] 邮件报告功能
- [ ] 更多图表类型

### 2. API扩展
- [ ] GraphQL接口
- [ ] WebSocket实时推送
- [ ] 第三方集成接口

### 3. 数据分析
- [ ] 机器学习用户行为预测
- [ ] 异常访问检测
- [ ] 转化率分析

## 技术支持

如有问题，请联系技术团队或查看相关日志文件。

---

**开发团队**: 龙驰新材料技术团队  
**最后更新**: 2025年6月  
**版本**: v1.0.0

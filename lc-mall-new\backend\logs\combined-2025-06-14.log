{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-14 13:28:50"}
{"environment":"development","level":"info","message":"<PERSON><PERSON> connected successfully","service":"lc-mall-backend","timestamp":"2025-06-14 13:28:50"}
{"environment":"development","level":"info","message":"Server running on port 5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-14 13:28:50"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-14 13:31:51"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-14 13:32:22"}
{"environment":"development","level":"info","message":"<PERSON><PERSON> connected successfully","service":"lc-mall-backend","timestamp":"2025-06-14 13:32:22"}
{"environment":"development","level":"info","message":"Server running on port 5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-14 13:32:22"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-14 13:41:07"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-14 13:41:07"}
{"environment":"development","level":"info","message":"Server running on port 5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-14 13:41:07"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-14 13:41:14"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-14 13:41:14"}
{"environment":"development","level":"info","message":"Server running on port 5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-14 13:41:14"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-14 13:42:48"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-14 13:42:48"}
{"environment":"development","level":"info","message":"Server running on port 5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-14 13:42:48"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-14 13:43:00"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-14 13:43:00"}
{"environment":"development","level":"info","message":"Server running on port 5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-14 13:43:00"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-14 13:43:08"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-14 13:43:08"}
{"environment":"development","level":"info","message":"Server running on port 5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-14 13:43:08"}
{"environment":"development","level":"info","message":"::1 - - [14/Jun/2025:05:48:41 +0000] \"GET /api/news/sample-news-1 HTTP/1.1\" 404 85 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-14 13:48:41"}
{"environment":"development","level":"info","message":"::1 - - [14/Jun/2025:05:59:59 +0000] \"GET /api/news/sample-news-1 HTTP/1.1\" 404 85 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-14 13:59:59"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-14 14:00:24"}
{"environment":"development","level":"info","message":"Redis client connected","service":"lc-mall-backend","timestamp":"2025-06-14 14:01:00"}
{"environment":"development","level":"info","message":"Redis connected successfully","service":"lc-mall-backend","timestamp":"2025-06-14 14:01:00"}
{"environment":"development","level":"info","message":"Server running on port 5000 in development mode","service":"lc-mall-backend","timestamp":"2025-06-14 14:01:00"}
{"environment":"development","level":"info","message":"::1 - - [14/Jun/2025:06:03:43 +0000] \"GET /api/news/sample-news-1 HTTP/1.1\" 404 85 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-14 14:03:43"}
{"environment":"development","level":"info","message":"::1 - - [14/Jun/2025:06:04:07 +0000] \"GET /api/news/sample-news-1 HTTP/1.1\" 404 85 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-14 14:04:07"}
{"environment":"development","level":"info","message":"::1 - - [14/Jun/2025:06:04:07 +0000] \"GET /api/news/sample-news-1 HTTP/1.1\" 404 85 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-14 14:04:07"}
{"environment":"development","level":"info","message":"::1 - - [14/Jun/2025:06:04:13 +0000] \"GET /api/news/sample-news-1 HTTP/1.1\" 404 85 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-14 14:04:13"}
{"environment":"development","level":"info","message":"::1 - - [14/Jun/2025:06:06:13 +0000] \"GET /api/news/sample-news-1 HTTP/1.1\" 404 85 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-14 14:06:13"}
{"environment":"development","level":"info","message":"::1 - - [14/Jun/2025:06:06:13 +0000] \"GET /api/news/sample-news-1 HTTP/1.1\" 404 85 \"-\" \"node\"","service":"lc-mall-backend","timestamp":"2025-06-14 14:06:13"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"lc-mall-backend","timestamp":"2025-06-14 14:11:07"}

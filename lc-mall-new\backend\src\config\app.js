const path = require('path');
const logger = require('../utils/logger');

/**
 * 应用基础配置
 */
const appConfig = {
  // 应用信息
  name: process.env.APP_NAME || '龙驰新材料商城',
  version: process.env.APP_VERSION || '1.0.0',
  description: '聚氨酯新材料B2B电商平台',
    // 服务器配置
  port: parseInt(process.env.PORT) || 5000,
  host: process.env.HOST || '0.0.0.0',
  
  // 环境配置
  env: process.env.NODE_ENV || 'development',
  
  // API配置
  apiPrefix: process.env.API_PREFIX || '/api',
  apiVersion: process.env.API_VERSION || 'v1',
  
  // 域名配置
  domain: process.env.DOMAIN || 'localhost',  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
  backendUrl: process.env.BACKEND_URL || 'http://localhost:5000',
  
  // 静态文件配置
  staticPath: process.env.STATIC_PATH || path.join(__dirname, '../../public'),
  uploadsPath: process.env.UPLOADS_PATH || path.join(__dirname, '../../uploads'),
  
  // 日志配置
  logLevel: process.env.LOG_LEVEL || 'info',
  logPath: process.env.LOG_PATH || path.join(__dirname, '../../logs'),
  
  // 时区配置
  timezone: process.env.TZ || 'Asia/Shanghai',
  
  // 语言配置
  defaultLanguage: process.env.DEFAULT_LANGUAGE || 'zh-CN',
  supportedLanguages: ['zh-CN', 'en-US']
};

/**
 * 安全配置
 */
const securityConfig = {
  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGIN ? 
      process.env.CORS_ORIGIN.split(',') : 
      ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    optionsSuccessStatus: 200,
    maxAge: 86400 // 24小时
  },
  
  // 速率限制配置
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100, // 每个IP最多100次请求
    message: '请求过于频繁，请稍后再试',
    standardHeaders: true,
    legacyHeaders: false,
    
    // 不同端点的特殊限制
    endpoints: {
      '/api/auth/login': {
        windowMs: 15 * 60 * 1000,
        max: 5 // 登录限制更严格
      },
      '/api/auth/register': {
        windowMs: 60 * 60 * 1000, // 1小时
        max: 3 // 注册限制
      },
      '/api/payments': {
        windowMs: 5 * 60 * 1000, // 5分钟
        max: 10 // 支付相关限制
      }
    }
  },
  
  // 内容安全策略
  csp: {
    directives: {
      // 默认资源加载来源，仅允许本站
      defaultSrc: ["'self'"],

      // 允许样式表来自本站、内联样式、Google Fonts 的 css
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com', 'https://fonts.loli.net'],

      // 允许字体文件仅来自本站和 Google 字体静态资源
      fontSrc: ["'self'", 'https://fonts.gstatic.com', 'https://fonts.loli.net'],

      // 图片资源允许本站、data: 协议（内嵌图片）、以及任意 https 来源
      imgSrc: ["'self'", 'data:', 'https:'],

      // 仅允许本站的脚本资源
      scriptSrc: ["'self'"],

      // 允许 Ajax/WebSocket 连接到本站和指定 API 域名
      connectSrc: ["'self'", 'https://api.longchi.com'],

      // 不允许页面被嵌入到任何 frame/iframe 里
      frameAncestors: ["'none'"],

      // 限制 <base> 标签的 href，仅允许本站
      baseUri: ["'self'"],

      // 限制表单提交目标，仅允许本站
      formAction: ["'self'"]
    }
  },
  
  // 密码策略
  password: {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAttempts: 5,
    lockoutDuration: 15 * 60 * 1000 // 15分钟
  },
  
  // 会话安全
  session: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'strict',
    maxAge: 24 * 60 * 60 * 1000 // 24小时
  },
  
  // HTTPS配置
  https: {
    enabled: process.env.HTTPS_ENABLED === 'true',
    forceRedirect: process.env.HTTPS_FORCE_REDIRECT === 'true',
    hsts: {
      maxAge: 31536000, // 1年
      includeSubDomains: true,
      preload: true
    }
  }
};

/**
 * 文件上传配置
 */
const uploadConfig = {
  // 文件大小限制
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
  maxFiles: parseInt(process.env.MAX_FILES) || 10,
  
  // 允许的文件类型
  allowedMimeTypes: {
    images: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml'
    ],
    documents: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
  },
  
  // 存储配置
  storage: {
    type: process.env.STORAGE_TYPE || 'local', // local, s3, oss
    local: {
      destination: path.join(__dirname, '../../uploads'),
      urlPrefix: '/uploads'
    },
    s3: {
      bucket: process.env.S3_BUCKET,
      region: process.env.S3_REGION,
      accessKeyId: process.env.S3_ACCESS_KEY_ID,
      secretAccessKey: process.env.S3_SECRET_ACCESS_KEY
    },
    oss: {
      bucket: process.env.OSS_BUCKET,
      region: process.env.OSS_REGION,
      accessKeyId: process.env.OSS_ACCESS_KEY_ID,
      accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET
    }
  },
  
  // 图片处理配置
  imageProcessing: {
    enabled: true,
    resize: {
      enabled: true,
      sizes: [
        { name: 'thumbnail', width: 150, height: 150 },
        { name: 'small', width: 300, height: 300 },
        { name: 'medium', width: 600, height: 600 },
        { name: 'large', width: 1200, height: 1200 }
      ]
    },
    watermark: {
      enabled: process.env.WATERMARK_ENABLED === 'true',
      text: '龙驰新材料',
      position: 'bottom-right',
      opacity: 0.3
    }
  }
};

/**
 * 支付配置
 */
const paymentConfig = {
  // 支付方式
  methods: {
    wechat: {
      enabled: process.env.WECHAT_PAY_ENABLED === 'true',
      appId: process.env.WECHAT_APP_ID,
      mchId: process.env.WECHAT_MCH_ID,
      apiKey: process.env.WECHAT_API_KEY,
      notifyUrl: process.env.WECHAT_NOTIFY_URL || `${appConfig.backendUrl}/api/payments/wechat/callback`,
      environment: process.env.WECHAT_ENVIRONMENT || 'sandbox' // sandbox, production
    },
    alipay: {
      enabled: process.env.ALIPAY_ENABLED === 'true',
      appId: process.env.ALIPAY_APP_ID,
      privateKey: process.env.ALIPAY_PRIVATE_KEY,
      publicKey: process.env.ALIPAY_PUBLIC_KEY,
      notifyUrl: process.env.ALIPAY_NOTIFY_URL || `${appConfig.backendUrl}/api/payments/alipay/callback`,
      environment: process.env.ALIPAY_ENVIRONMENT || 'sandbox'
    },
    bankTransfer: {
      enabled: process.env.BANK_TRANSFER_ENABLED !== 'false',
      accounts: [
        {
          bankName: '中国工商银行',
          accountName: '龙驰新材料有限公司',
          accountNumber: '1234567890123456789',
          branchName: '深圳分行'
        }
      ]
    }
  },
  
  // 支付超时配置
  timeout: {
    payment: parseInt(process.env.PAYMENT_TIMEOUT) || 15 * 60 * 1000, // 15分钟
    refund: parseInt(process.env.REFUND_TIMEOUT) || 30 * 24 * 60 * 60 * 1000 // 30天
  },
  
  // 支付回调验证
  callback: {
    retryAttempts: parseInt(process.env.CALLBACK_RETRY_ATTEMPTS) || 3,
    retryDelay: parseInt(process.env.CALLBACK_RETRY_DELAY) || 5000 // 5秒
  }
};

/**
 * 邮件配置
 */
const emailConfig = {
  enabled: process.env.EMAIL_ENABLED === 'true',
  
  // SMTP配置
  smtp: {
    host: process.env.SMTP_HOST || 'smtp.qq.com',
    port: parseInt(process.env.SMTP_PORT) || 587,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  },
  
  // 发件人配置
  from: {
    name: process.env.EMAIL_FROM_NAME || '龙驰新材料',
    address: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'
  },
  
  // 邮件模板配置
  templates: {
    path: path.join(__dirname, '../../templates/email'),
    engine: 'handlebars'
  },
  
  // 邮件发送限制
  limits: {
    dailyLimit: parseInt(process.env.EMAIL_DAILY_LIMIT) || 1000,
    hourlyLimit: parseInt(process.env.EMAIL_HOURLY_LIMIT) || 100
  }
};

/**
 * 短信配置
 */
const smsConfig = {
  enabled: process.env.SMS_ENABLED === 'true',
  
  // 服务提供商
  provider: process.env.SMS_PROVIDER || 'aliyun', // aliyun, tencent
  
  // 阿里云短信配置
  aliyun: {
    accessKeyId: process.env.ALIYUN_SMS_ACCESS_KEY_ID,
    accessKeySecret: process.env.ALIYUN_SMS_ACCESS_KEY_SECRET,
    signName: process.env.ALIYUN_SMS_SIGN_NAME || '龙驰新材料',
    templates: {
      verification: process.env.ALIYUN_SMS_VERIFICATION_TEMPLATE,
      orderNotification: process.env.ALIYUN_SMS_ORDER_TEMPLATE
    }
  },
  
  // 腾讯云短信配置
  tencent: {
    secretId: process.env.TENCENT_SMS_SECRET_ID,
    secretKey: process.env.TENCENT_SMS_SECRET_KEY,
    sdkAppId: process.env.TENCENT_SMS_SDK_APP_ID,
    signName: process.env.TENCENT_SMS_SIGN_NAME || '龙驰新材料'
  },
  
  // 短信发送限制
  limits: {
    dailyLimit: parseInt(process.env.SMS_DAILY_LIMIT) || 200,
    hourlyLimit: parseInt(process.env.SMS_HOURLY_LIMIT) || 20,
    verificationInterval: parseInt(process.env.SMS_VERIFICATION_INTERVAL) || 60000 // 1分钟
  }
};

/**
 * 缓存配置
 */
const cacheConfig = {
  // 缓存策略
  strategy: process.env.CACHE_STRATEGY || 'redis', // redis, memory
  
  // 内存缓存配置
  memory: {
    max: parseInt(process.env.MEMORY_CACHE_MAX) || 1000,
    ttl: parseInt(process.env.MEMORY_CACHE_TTL) || 3600000 // 1小时
  },
  
  // 缓存预热
  warmup: {
    enabled: process.env.CACHE_WARMUP_ENABLED === 'true',
    delay: parseInt(process.env.CACHE_WARMUP_DELAY) || 10000 // 10秒
  }
};

/**
 * 搜索配置
 */
const searchConfig = {
  // 搜索引擎
  engine: process.env.SEARCH_ENGINE || 'redis', // redis, elasticsearch
  
  // 搜索参数
  params: {
    maxResults: parseInt(process.env.SEARCH_MAX_RESULTS) || 1000,
    defaultLimit: parseInt(process.env.SEARCH_DEFAULT_LIMIT) || 20,
    minQueryLength: parseInt(process.env.SEARCH_MIN_QUERY_LENGTH) || 1,
    maxQueryLength: parseInt(process.env.SEARCH_MAX_QUERY_LENGTH) || 100
  },
  
  // 搜索索引
  indexing: {
    enabled: true,
    batchSize: parseInt(process.env.SEARCH_INDEX_BATCH_SIZE) || 100,
    rebuildInterval: parseInt(process.env.SEARCH_REBUILD_INTERVAL) || 24 * 60 * 60 * 1000 // 24小时
  }
};

/**
 * 监控配置
 */
const monitoringConfig = {
  enabled: process.env.MONITORING_ENABLED !== 'false',
  
  // 性能监控
  performance: {
    enabled: true,
    slowRequestThreshold: parseInt(process.env.SLOW_REQUEST_THRESHOLD) || 1000, // 1秒
    memoryWarningThreshold: parseFloat(process.env.MEMORY_WARNING_THRESHOLD) || 0.8 // 80%
  },
  
  // 健康检查
  healthCheck: {
    enabled: true,
    endpoint: '/health',
    interval: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 30000 // 30秒
  },
  
  // 错误跟踪
  errorTracking: {
    enabled: process.env.ERROR_TRACKING_ENABLED === 'true',
    provider: process.env.ERROR_TRACKING_PROVIDER, // sentry
    dsn: process.env.ERROR_TRACKING_DSN
  }
};

/**
 * 获取完整应用配置
 */
const getAppConfig = () => {
  return {
    app: appConfig,
    security: securityConfig,
    upload: uploadConfig,
    payment: paymentConfig,
    email: emailConfig,
    sms: smsConfig,
    cache: cacheConfig,
    search: searchConfig,
    monitoring: monitoringConfig
  };
};

/**
 * 验证应用配置
 */
const validateAppConfig = () => {
  const errors = [];

  // 验证基本配置
  if (!appConfig.name) {
    errors.push('App name is required');
  }

  if (appConfig.port < 1 || appConfig.port > 65535) {
    errors.push('Port must be between 1 and 65535');
  }

  // 验证支付配置
  if (paymentConfig.methods.wechat.enabled) {
    if (!paymentConfig.methods.wechat.appId || !paymentConfig.methods.wechat.apiKey) {
      errors.push('WeChat payment configuration is incomplete');
    }
  }

  if (paymentConfig.methods.alipay.enabled) {
    if (!paymentConfig.methods.alipay.appId || !paymentConfig.methods.alipay.privateKey) {
      errors.push('Alipay payment configuration is incomplete');
    }
  }

  // 验证邮件配置
  if (emailConfig.enabled) {
    if (!emailConfig.smtp.host || !emailConfig.smtp.auth.user) {
      errors.push('Email configuration is incomplete');
    }
  }

  // 验证短信配置
  if (smsConfig.enabled) {
    if (smsConfig.provider === 'aliyun' && (!smsConfig.aliyun.accessKeyId || !smsConfig.aliyun.accessKeySecret)) {
      errors.push('Aliyun SMS configuration is incomplete');
    }
  }

  if (errors.length > 0) {
    logger.error('App configuration validation failed', { errors });
    throw new Error(`App configuration invalid: ${errors.join(', ')}`);
  }

  logger.info('App configuration validated successfully');
};

/**
 * 获取环境特定配置覆盖
 */
const getEnvironmentOverrides = () => {
  const env = process.env.NODE_ENV || 'development';
  
  const overrides = {
    development: {
      app: {
        logLevel: 'debug'
      },
      security: {
        rateLimit: {
          max: 1000 // 开发环境放宽限制
        }
      }
    },
    test: {
      app: {
        logLevel: 'warn'
      },
      email: {
        enabled: false
      },
      sms: {
        enabled: false
      }
    },
    production: {
      app: {
        logLevel: 'error'
      },
      security: {
        https: {
          enabled: true,
          forceRedirect: true
        }
      },
      monitoring: {
        enabled: true,
        errorTracking: {
          enabled: true
        }
      }
    }
  };

  return overrides[env] || {};
};

module.exports = {
  appConfig,
  securityConfig,
  uploadConfig,
  paymentConfig,
  emailConfig,
  smsConfig,
  cacheConfig,
  searchConfig,
  monitoringConfig,
  getAppConfig,
  validateAppConfig,
  getEnvironmentOverrides
};

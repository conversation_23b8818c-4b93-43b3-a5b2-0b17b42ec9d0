/**
 * Tailwind CSS 测试页面
 * 用于验证Tailwind CSS是否正常工作
 */

export default function TestTailwindPage() {
  return (
    <div className="min-h-screen bg-gray-100 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-4xl font-bold text-center text-gray-900 mb-8">
          Tailwind CSS 测试页面
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 卡片1 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">基础样式</h2>
            <p className="text-gray-600 mb-4">
              这是一个测试卡片，用于验证基础的Tailwind CSS样式是否正常工作。
            </p>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors">
              测试按钮
            </button>
          </div>

          {/* 卡片2 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-green-800 mb-4">颜色系统</h2>
            <div className="space-y-2">
              <div className="w-full h-4 bg-red-500 rounded"></div>
              <div className="w-full h-4 bg-green-500 rounded"></div>
              <div className="w-full h-4 bg-blue-500 rounded"></div>
              <div className="w-full h-4 bg-yellow-500 rounded"></div>
              <div className="w-full h-4 bg-purple-500 rounded"></div>
            </div>
          </div>

          {/* 卡片3 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-purple-800 mb-4">响应式设计</h2>
            <div className="grid grid-cols-2 gap-2">
              <div className="bg-gray-200 h-12 rounded flex items-center justify-center text-sm">
                响应式
              </div>
              <div className="bg-gray-300 h-12 rounded flex items-center justify-center text-sm">
                网格
              </div>
              <div className="bg-gray-400 h-12 rounded flex items-center justify-center text-sm text-white">
                布局
              </div>
              <div className="bg-gray-500 h-12 rounded flex items-center justify-center text-sm text-white">
                测试
              </div>
            </div>
          </div>
        </div>

        {/* 动画测试 */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">动画和过渡效果</h2>
          <div className="flex flex-wrap gap-4">
            <div className="bg-blue-500 text-white px-4 py-2 rounded hover:scale-105 transform transition-transform cursor-pointer">
              悬停缩放
            </div>
            <div className="bg-green-500 text-white px-4 py-2 rounded hover:rotate-3 transform transition-transform cursor-pointer">
              悬停旋转
            </div>
            <div className="bg-red-500 text-white px-4 py-2 rounded hover:shadow-lg transition-shadow cursor-pointer">
              悬停阴影
            </div>
            <div className="bg-yellow-500 text-white px-4 py-2 rounded animate-pulse">
              脉冲动画
            </div>
            <div className="bg-purple-500 text-white px-4 py-2 rounded animate-bounce">
              弹跳动画
            </div>
          </div>
        </div>

        {/* 表单测试 */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">表单样式</h2>
          <form className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                输入框
              </label>
              <input
                type="text"
                placeholder="请输入内容..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择框
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option>选项 1</option>
                <option>选项 2</option>
                <option>选项 3</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                文本域
              </label>
              <textarea
                rows={3}
                placeholder="请输入多行内容..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              ></textarea>
            </div>
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-2 rounded-md transition-colors"
            >
              提交表单
            </button>
          </form>
        </div>

        {/* 状态指示 */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="font-medium">Tailwind CSS 正常工作</span>
          </div>
        </div>
      </div>
    </div>
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/components/product/ProductSection.tsx":
/*!***************************************************!*\
  !*** ./src/components/product/ProductSection.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductSection: function() { return /* binding */ ProductSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./src/components/product/ProductCard.tsx\");\n/* harmony import */ var _ProductDetailModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProductDetailModal */ \"(app-pages-browser)/./src/components/product/ProductDetailModal.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Loading */ \"(app-pages-browser)/./src/components/ui/Loading.tsx\");\n/* harmony import */ var _utils_searchService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/searchService */ \"(app-pages-browser)/./src/utils/searchService.ts\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ ProductSection,default auto */ var _process_env_NEXT_PUBLIC_API_URL;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n // 导入 Search 图标\n// 统一 API 基础地址，构建时注入\nconst apiBase = ((_process_env_NEXT_PUBLIC_API_URL = \"http://localhost:5000/api\") === null || _process_env_NEXT_PUBLIC_API_URL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_API_URL.replace(/\\/$/, \"\")) || \"http://localhost:5000/api\";\nconst ProductSection = (param)=>{\n    let { selectedCategory, searchQuery, onSearchQueryChange } = param;\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"default\");\n    // Fetch products from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchProducts = async ()=>{\n            try {\n                setLoading(true);\n                console.log(\"ProductSection: Fetching products...\");\n                // 使用前端API路由，添加缓存控制头确保获取最新数据\n                const response = await fetch(\"/api/products?limit=50\", {\n                    cache: \"no-store\",\n                    headers: {\n                        \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                        \"Pragma\": \"no-cache\"\n                    }\n                });\n                console.log(\"ProductSection: API response status:\", response.status);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    console.error(\"ProductSection: API error:\", response.status, errorText);\n                    throw new Error(\"Failed to fetch products: \".concat(response.status, \" \").concat(response.statusText));\n                }\n                const result = await response.json();\n                console.log(\"ProductSection: API result:\", result);\n                // 检查是否有错误\n                if (result.error) {\n                    throw new Error(result.error);\n                }\n                // 前端API返回的数据结构：{ products: [...], pagination: {...} }\n                const data = result.products || result;\n                console.log(\"ProductSection: Products data:\", Array.isArray(data) ? data.length : \"not array\", data);\n                if (Array.isArray(data) && data.length > 0) {\n                    setProducts(data);\n                    setError(null);\n                    console.log(\"ProductSection: Successfully loaded\", data.length, \"products\");\n                } else {\n                    console.warn(\"ProductSection: No products found, loading sample data\");\n                    loadSampleProducts();\n                }\n            } catch (err) {\n                console.error(\"ProductSection: Error fetching products:\", err);\n                setError(\"Failed to load products: \".concat(err instanceof Error ? err.message : \"Unknown error\"));\n                // Load sample data as fallback\n                loadSampleProducts();\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchProducts();\n    }, []);\n    // Load sample products as fallback\n    const loadSampleProducts = ()=>{\n        const sampleProducts = [\n            {\n                id: \"1\",\n                name: \"Professional Hair Dryer Pro Max\",\n                category: \"hair-dryer\",\n                price: 299.99,\n                image: \"/images/products/hair-dryer-1.jpg\",\n                description: \"高性能专业吹风机，采用离子技术，快速干发同时保护头发健康\",\n                stock: 15,\n                featured: true,\n                specifications: \"Brand: ProStyle, Model: HD-2000, Power: 2000W, Voltage: 220V, Weight: 800g, Warranty: 2 years\",\n                sku: \"HD-2000-PRO\",\n                details: {\n                    model: \"HD-2000\",\n                    appearance: \"Black/Silver\",\n                    standard: \"CE Certified\",\n                    usage: \"Professional hair drying\",\n                    flashpoint: \"N/A\",\n                    smell: \"Odorless\",\n                    function: \"Ionic hair drying technology\",\n                    amineValue: \"N/A\",\n                    content: \"1 unit\",\n                    imported: \"Yes\",\n                    density: \"N/A\",\n                    color: \"Black\",\n                    viscosity: \"N/A\"\n                }\n            },\n            {\n                id: \"2\",\n                name: \"Smart Home Security Camera\",\n                category: \"security\",\n                price: 149.99,\n                image: \"/images/products/camera-1.jpg\",\n                description: \"1080p高清无线安全摄像头，具有夜视功能和移动检测\",\n                stock: 25,\n                featured: false,\n                specifications: \"Brand: SecureView, Model: SV-1080, Type: Wireless, Power: 12V DC, Warranty: 1 year\",\n                sku: \"SV-1080-WL\",\n                details: {\n                    model: \"SV-1080\",\n                    appearance: \"White\",\n                    standard: \"IP65\",\n                    usage: \"Home security monitoring\",\n                    flashpoint: \"N/A\",\n                    smell: \"Odorless\",\n                    function: \"1080p HD recording with night vision\",\n                    amineValue: \"N/A\",\n                    content: \"1 unit\",\n                    imported: \"Yes\",\n                    density: \"N/A\",\n                    color: \"White\",\n                    viscosity: \"N/A\"\n                }\n            },\n            {\n                id: \"3\",\n                name: \"Bluetooth Wireless Headphones\",\n                category: \"electronics\",\n                price: 79.99,\n                image: \"/images/products/headphones-1.jpg\",\n                description: \"高品质无线蓝牙耳机，具有主动降噪功能\",\n                stock: 30,\n                featured: true,\n                specifications: \"Brand: AudioMax, Model: AM-BT500, Type: Over-ear, Warranty: 1 year\",\n                sku: \"AM-BT500-BT\",\n                details: {\n                    model: \"AM-BT500\",\n                    appearance: \"Black\",\n                    standard: \"Bluetooth 5.0\",\n                    usage: \"Audio listening\",\n                    flashpoint: \"N/A\",\n                    smell: \"Odorless\",\n                    function: \"Active noise cancellation\",\n                    amineValue: \"N/A\",\n                    content: \"1 unit\",\n                    imported: \"Yes\",\n                    density: \"N/A\",\n                    color: \"Black\",\n                    viscosity: \"N/A\"\n                }\n            }\n        ];\n        setProducts(sampleProducts);\n    };\n    // Filter and sort products\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = products;\n        // Filter by category\n        if (selectedCategory && selectedCategory !== \"all\") {\n            filtered = filtered.filter((product)=>product.category === selectedCategory);\n        }\n        // Filter by search query using SearchService\n        if (searchQuery) {\n            filtered = _utils_searchService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].searchProducts(filtered, searchQuery);\n        } else {\n            // Sort products when no search query\n            switch(sortBy){\n                case \"price-low\":\n                    filtered.sort((a, b)=>a.price - b.price);\n                    break;\n                case \"price-high\":\n                    filtered.sort((a, b)=>b.price - a.price);\n                    break;\n                case \"name\":\n                    filtered.sort((a, b)=>a.name.localeCompare(b.name));\n                    break;\n                default:\n                    // Default sort: featured first, then by name\n                    filtered.sort((a, b)=>{\n                        if (a.featured && !b.featured) return -1;\n                        if (!a.featured && b.featured) return 1;\n                        return a.name.localeCompare(b.name);\n                    });\n            }\n        }\n        setFilteredProducts(filtered);\n    }, [\n        products,\n        selectedCategory,\n        searchQuery,\n        sortBy\n    ]);\n    const handleProductClick = (product)=>{\n        setSelectedProduct(product);\n    };\n    const handleCloseModal = ()=>{\n        setSelectedProduct(null);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_4__.LoadingCard, {\n                    count: 8\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 p-4 md:p-6\",\n            children: [\n                \" \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-6 md:p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-400 text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-red-800 mb-2\",\n                            children: \"加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                            children: \"重新加载\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-4 md:p-6\",\n        children: [\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-center mb-6 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full md:max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"search\",\n                                placeholder: \"搜索产品...\",\n                                value: searchQuery,\n                                onChange: (e)=>onSearchQueryChange(e.target.value),\n                                className: \"w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 md:space-x-4 w-full md:w-auto justify-between md:justify-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm text-gray-600 whitespace-nowrap\",\n                                children: \"排序方式:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: sortBy,\n                                onChange: (e)=>setSortBy(e.target.value),\n                                className: \"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"default\",\n                                        children: \"默认排序\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"price-low\",\n                                        children: \"价格从低到高\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"price-high\",\n                                        children: \"价格从高到低\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"name\",\n                                        children: \"按名称排序\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            filteredProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6\",\n                children: [\n                    \" \",\n                    filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_2__.ProductCard, {\n                            product: product,\n                            onClick: ()=>handleProductClick(product)\n                        }, product.id, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCE6\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-600 mb-2\",\n                        children: \"没有找到产品\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: searchQuery ? '没有找到包含 \"'.concat(searchQuery, '\" 的产品') : \"该分类下暂无产品\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, undefined),\n            selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductDetailModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                product: selectedProduct,\n                isOpen: !!selectedProduct,\n                onClose: handleCloseModal\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\product\\\\ProductSection.tsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductSection, \"54UuAGsAEZtlbneh97Hs4XFAI/I=\");\n_c = ProductSection;\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductSection);\nvar _c;\n$RefreshReg$(_c, \"ProductSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/product/ProductSection.tsx\n"));

/***/ })

});
/**
 * 管理员认证相关工具函数
 */

/**
 * 获取管理员API请求头
 * @returns 包含Content-Type和管理员API密钥的请求头对象
 */
export function getAdminHeaders(): Record<string, string> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  // 获取管理员API密钥
  let adminApiKey: string;
    if (typeof window === 'undefined') {    // 服务器端 - 从环境变量获取，优先使用NEXT_PUBLIC_前缀的变量
    adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 
                 process.env.ADMIN_API_KEY || 
                 'lc_admin_dev_key_2025';
    console.log('Server-side Admin API Key source:', 
                process.env.NEXT_PUBLIC_ADMIN_API_KEY ? 'NEXT_PUBLIC_ADMIN_API_KEY' : 
                process.env.ADMIN_API_KEY ? 'ADMIN_API_KEY' : 'fallback');  
	} else {
    // 客户端 - 从构建时注入的环境变量获取
    adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'lc_admin_dev_key_2025';
    console.log('Client-side using NEXT_PUBLIC_ADMIN_API_KEY');
  }
  
  if (adminApiKey) {
    headers['x-admin-api-key'] = adminApiKey;
  }
  
  return headers;
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-from-markdown";
exports.ids = ["vendor-chunks/mdast-util-from-markdown"];
exports.modules = {

/***/ "(rsc)/../node_modules/mdast-util-from-markdown/dev/lib/index.js":
/*!*****************************************************************!*\
  !*** ../node_modules/mdast-util-from-markdown/dev/lib/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromMarkdown: () => (/* binding */ fromMarkdown)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! mdast-util-to-string */ \"(rsc)/../node_modules/mdast-util-to-string/lib/index.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark */ \"(rsc)/../node_modules/micromark/dev/lib/postprocess.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark */ \"(rsc)/../node_modules/micromark/dev/lib/parse.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark */ \"(rsc)/../node_modules/micromark/dev/lib/preprocess.js\");\n/* harmony import */ var micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-util-decode-numeric-character-reference */ \"(rsc)/../node_modules/micromark-util-decode-numeric-character-reference/dev/index.js\");\n/* harmony import */ var micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-util-decode-string */ \"(rsc)/../node_modules/micromark-util-decode-string/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(rsc)/../node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! decode-named-character-reference */ \"(rsc)/../node_modules/decode-named-character-reference/index.js\");\n/* harmony import */ var unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! unist-util-stringify-position */ \"(rsc)/../node_modules/unist-util-stringify-position/lib/index.js\");\n/**\n * @import {\n *   Break,\n *   Blockquote,\n *   Code,\n *   Definition,\n *   Emphasis,\n *   Heading,\n *   Html,\n *   Image,\n *   InlineCode,\n *   Link,\n *   ListItem,\n *   List,\n *   Nodes,\n *   Paragraph,\n *   PhrasingContent,\n *   ReferenceType,\n *   Root,\n *   Strong,\n *   Text,\n *   ThematicBreak\n * } from 'mdast'\n * @import {\n *   Encoding,\n *   Event,\n *   Token,\n *   Value\n * } from 'micromark-util-types'\n * @import {Point} from 'unist'\n * @import {\n *   CompileContext,\n *   CompileData,\n *   Config,\n *   Extension,\n *   Handle,\n *   OnEnterError,\n *   Options\n * } from './types.js'\n */ \n\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty;\n/**\n * Turn markdown into a syntax tree.\n *\n * @overload\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @overload\n * @param {Value} value\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @param {Value} value\n *   Markdown to parse.\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding for when `value` is `Buffer`.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {Root}\n *   mdast tree.\n */ function fromMarkdown(value, encoding, options) {\n    if (typeof encoding !== \"string\") {\n        options = encoding;\n        encoding = undefined;\n    }\n    return compiler(options)((0,micromark__WEBPACK_IMPORTED_MODULE_0__.postprocess)((0,micromark__WEBPACK_IMPORTED_MODULE_1__.parse)(options).document().write((0,micromark__WEBPACK_IMPORTED_MODULE_2__.preprocess)()(value, encoding, true))));\n}\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */ function compiler(options) {\n    /** @type {Config} */ const config = {\n        transforms: [],\n        canContainEols: [\n            \"emphasis\",\n            \"fragment\",\n            \"heading\",\n            \"paragraph\",\n            \"strong\"\n        ],\n        enter: {\n            autolink: opener(link),\n            autolinkProtocol: onenterdata,\n            autolinkEmail: onenterdata,\n            atxHeading: opener(heading),\n            blockQuote: opener(blockQuote),\n            characterEscape: onenterdata,\n            characterReference: onenterdata,\n            codeFenced: opener(codeFlow),\n            codeFencedFenceInfo: buffer,\n            codeFencedFenceMeta: buffer,\n            codeIndented: opener(codeFlow, buffer),\n            codeText: opener(codeText, buffer),\n            codeTextData: onenterdata,\n            data: onenterdata,\n            codeFlowValue: onenterdata,\n            definition: opener(definition),\n            definitionDestinationString: buffer,\n            definitionLabelString: buffer,\n            definitionTitleString: buffer,\n            emphasis: opener(emphasis),\n            hardBreakEscape: opener(hardBreak),\n            hardBreakTrailing: opener(hardBreak),\n            htmlFlow: opener(html, buffer),\n            htmlFlowData: onenterdata,\n            htmlText: opener(html, buffer),\n            htmlTextData: onenterdata,\n            image: opener(image),\n            label: buffer,\n            link: opener(link),\n            listItem: opener(listItem),\n            listItemValue: onenterlistitemvalue,\n            listOrdered: opener(list, onenterlistordered),\n            listUnordered: opener(list),\n            paragraph: opener(paragraph),\n            reference: onenterreference,\n            referenceString: buffer,\n            resourceDestinationString: buffer,\n            resourceTitleString: buffer,\n            setextHeading: opener(heading),\n            strong: opener(strong),\n            thematicBreak: opener(thematicBreak)\n        },\n        exit: {\n            atxHeading: closer(),\n            atxHeadingSequence: onexitatxheadingsequence,\n            autolink: closer(),\n            autolinkEmail: onexitautolinkemail,\n            autolinkProtocol: onexitautolinkprotocol,\n            blockQuote: closer(),\n            characterEscapeValue: onexitdata,\n            characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n            characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n            characterReferenceValue: onexitcharacterreferencevalue,\n            characterReference: onexitcharacterreference,\n            codeFenced: closer(onexitcodefenced),\n            codeFencedFence: onexitcodefencedfence,\n            codeFencedFenceInfo: onexitcodefencedfenceinfo,\n            codeFencedFenceMeta: onexitcodefencedfencemeta,\n            codeFlowValue: onexitdata,\n            codeIndented: closer(onexitcodeindented),\n            codeText: closer(onexitcodetext),\n            codeTextData: onexitdata,\n            data: onexitdata,\n            definition: closer(),\n            definitionDestinationString: onexitdefinitiondestinationstring,\n            definitionLabelString: onexitdefinitionlabelstring,\n            definitionTitleString: onexitdefinitiontitlestring,\n            emphasis: closer(),\n            hardBreakEscape: closer(onexithardbreak),\n            hardBreakTrailing: closer(onexithardbreak),\n            htmlFlow: closer(onexithtmlflow),\n            htmlFlowData: onexitdata,\n            htmlText: closer(onexithtmltext),\n            htmlTextData: onexitdata,\n            image: closer(onexitimage),\n            label: onexitlabel,\n            labelText: onexitlabeltext,\n            lineEnding: onexitlineending,\n            link: closer(onexitlink),\n            listItem: closer(),\n            listOrdered: closer(),\n            listUnordered: closer(),\n            paragraph: closer(),\n            referenceString: onexitreferencestring,\n            resourceDestinationString: onexitresourcedestinationstring,\n            resourceTitleString: onexitresourcetitlestring,\n            resource: onexitresource,\n            setextHeading: closer(onexitsetextheading),\n            setextHeadingLineSequence: onexitsetextheadinglinesequence,\n            setextHeadingText: onexitsetextheadingtext,\n            strong: closer(),\n            thematicBreak: closer()\n        }\n    };\n    configure(config, (options || {}).mdastExtensions || []);\n    /** @type {CompileData} */ const data = {};\n    return compile;\n    /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */ function compile(events) {\n        /** @type {Root} */ let tree = {\n            type: \"root\",\n            children: []\n        };\n        /** @type {Omit<CompileContext, 'sliceSerialize'>} */ const context = {\n            stack: [\n                tree\n            ],\n            tokenStack: [],\n            config,\n            enter,\n            exit,\n            buffer,\n            resume,\n            data\n        };\n        /** @type {Array<number>} */ const listStack = [];\n        let index = -1;\n        while(++index < events.length){\n            // We preprocess lists to add `listItem` tokens, and to infer whether\n            // items the list itself are spread out.\n            if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered || events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered) {\n                if (events[index][0] === \"enter\") {\n                    listStack.push(index);\n                } else {\n                    const tail = listStack.pop();\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof tail === \"number\", \"expected list ot be open\");\n                    index = prepareList(events, tail, index);\n                }\n            }\n        }\n        index = -1;\n        while(++index < events.length){\n            const handler = config[events[index][0]];\n            if (own.call(handler, events[index][1].type)) {\n                handler[events[index][1].type].call(Object.assign({\n                    sliceSerialize: events[index][2].sliceSerialize\n                }, context), events[index][1]);\n            }\n        }\n        // Handle tokens still being open.\n        if (context.tokenStack.length > 0) {\n            const tail = context.tokenStack[context.tokenStack.length - 1];\n            const handler = tail[1] || defaultOnError;\n            handler.call(context, undefined, tail[0]);\n        }\n        // Figure out `root` position.\n        tree.position = {\n            start: point(events.length > 0 ? events[0][1].start : {\n                line: 1,\n                column: 1,\n                offset: 0\n            }),\n            end: point(events.length > 0 ? events[events.length - 2][1].end : {\n                line: 1,\n                column: 1,\n                offset: 0\n            })\n        };\n        // Call transforms.\n        index = -1;\n        while(++index < config.transforms.length){\n            tree = config.transforms[index](tree) || tree;\n        }\n        return tree;\n    }\n    /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */ function prepareList(events, start, length) {\n        let index = start - 1;\n        let containerBalance = -1;\n        let listSpread = false;\n        /** @type {Token | undefined} */ let listItem;\n        /** @type {number | undefined} */ let lineIndex;\n        /** @type {number | undefined} */ let firstBlankLineIndex;\n        /** @type {boolean | undefined} */ let atMarker;\n        while(++index <= length){\n            const event = events[index];\n            switch(event[1].type){\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuote:\n                    {\n                        if (event[0] === \"enter\") {\n                            containerBalance++;\n                        } else {\n                            containerBalance--;\n                        }\n                        atMarker = undefined;\n                        break;\n                    }\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank:\n                    {\n                        if (event[0] === \"enter\") {\n                            if (listItem && !atMarker && !containerBalance && !firstBlankLineIndex) {\n                                firstBlankLineIndex = index;\n                            }\n                            atMarker = undefined;\n                        }\n                        break;\n                    }\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemValue:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemMarker:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefixWhitespace:\n                    {\n                        break;\n                    }\n                default:\n                    {\n                        atMarker = undefined;\n                    }\n            }\n            if (!containerBalance && event[0] === \"enter\" && event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix || containerBalance === -1 && event[0] === \"exit\" && (event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered || event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered)) {\n                if (listItem) {\n                    let tailIndex = index;\n                    lineIndex = undefined;\n                    while(tailIndex--){\n                        const tailEvent = events[tailIndex];\n                        if (tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank) {\n                            if (tailEvent[0] === \"exit\") continue;\n                            if (lineIndex) {\n                                events[lineIndex][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank;\n                                listSpread = true;\n                            }\n                            tailEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding;\n                            lineIndex = tailIndex;\n                        } else if (tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuotePrefix || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuotePrefixWhitespace || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuoteMarker || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemIndent) {\n                        // Empty\n                        } else {\n                            break;\n                        }\n                    }\n                    if (firstBlankLineIndex && (!lineIndex || firstBlankLineIndex < lineIndex)) {\n                        listItem._spread = true;\n                    }\n                    // Fix position.\n                    listItem.end = Object.assign({}, lineIndex ? events[lineIndex][1].start : event[1].end);\n                    events.splice(lineIndex || index, 0, [\n                        \"exit\",\n                        listItem,\n                        event[2]\n                    ]);\n                    index++;\n                    length++;\n                }\n                // Create a new list item.\n                if (event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix) {\n                    /** @type {Token} */ const item = {\n                        type: \"listItem\",\n                        _spread: false,\n                        start: Object.assign({}, event[1].start),\n                        // @ts-expect-error: we’ll add `end` in a second.\n                        end: undefined\n                    };\n                    listItem = item;\n                    events.splice(index, 0, [\n                        \"enter\",\n                        item,\n                        event[2]\n                    ]);\n                    index++;\n                    length++;\n                    firstBlankLineIndex = undefined;\n                    atMarker = true;\n                }\n            }\n        }\n        events[start][1]._spread = listSpread;\n        return length;\n    }\n    /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Nodes} create\n   *   Create a node.\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */ function opener(create, and) {\n        return open;\n        /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */ function open(token) {\n            enter.call(this, create(token), token);\n            if (and) and.call(this, token);\n        }\n    }\n    /**\n   * @type {CompileContext['buffer']}\n   */ function buffer() {\n        this.stack.push({\n            type: \"fragment\",\n            children: []\n        });\n    }\n    /**\n   * @type {CompileContext['enter']}\n   */ function enter(node, token, errorHandler) {\n        const parent = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(parent, \"expected `parent`\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"children\" in parent, \"expected `parent`\");\n        /** @type {Array<Nodes>} */ const siblings = parent.children;\n        siblings.push(node);\n        this.stack.push(node);\n        this.tokenStack.push([\n            token,\n            errorHandler || undefined\n        ]);\n        node.position = {\n            start: point(token.start),\n            // @ts-expect-error: `end` will be patched later.\n            end: undefined\n        };\n    }\n    /**\n   * Create a closer handle.\n   *\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */ function closer(and) {\n        return close;\n        /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */ function close(token) {\n            if (and) and.call(this, token);\n            exit.call(this, token);\n        }\n    }\n    /**\n   * @type {CompileContext['exit']}\n   */ function exit(token, onExitError) {\n        const node = this.stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected `node`\");\n        const open = this.tokenStack.pop();\n        if (!open) {\n            throw new Error(\"Cannot close `\" + token.type + \"` (\" + (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({\n                start: token.start,\n                end: token.end\n            }) + \"): it’s not open\");\n        } else if (open[0].type !== token.type) {\n            if (onExitError) {\n                onExitError.call(this, token, open[0]);\n            } else {\n                const handler = open[1] || defaultOnError;\n                handler.call(this, token, open[0]);\n            }\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type !== \"fragment\", \"unexpected fragment `exit`ed\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.position, \"expected `position` to be defined\");\n        node.position.end = point(token.end);\n    }\n    /**\n   * @type {CompileContext['resume']}\n   */ function resume() {\n        return (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__.toString)(this.stack.pop());\n    }\n    //\n    // Handlers.\n    //\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onenterlistordered() {\n        this.data.expectingFirstListItemValue = true;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onenterlistitemvalue(token) {\n        if (this.data.expectingFirstListItemValue) {\n            const ancestor = this.stack[this.stack.length - 2];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor, \"expected nodes on stack\");\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor.type === \"list\", \"expected list on stack\");\n            ancestor.start = Number.parseInt(this.sliceSerialize(token), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal);\n            this.data.expectingFirstListItemValue = undefined;\n        }\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodefencedfenceinfo() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"code\", \"expected code on stack\");\n        node.lang = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodefencedfencemeta() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"code\", \"expected code on stack\");\n        node.meta = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodefencedfence() {\n        // Exit if this is the closing fence.\n        if (this.data.flowCodeInside) return;\n        this.buffer();\n        this.data.flowCodeInside = true;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodefenced() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"code\", \"expected code on stack\");\n        node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, \"\");\n        this.data.flowCodeInside = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodeindented() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"code\", \"expected code on stack\");\n        node.value = data.replace(/(\\r?\\n|\\r)$/g, \"\");\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitdefinitionlabelstring(token) {\n        const label = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"definition\", \"expected definition on stack\");\n        node.label = label;\n        node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(this.sliceSerialize(token)).toLowerCase();\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitdefinitiontitlestring() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"definition\", \"expected definition on stack\");\n        node.title = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitdefinitiondestinationstring() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"definition\", \"expected definition on stack\");\n        node.url = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitatxheadingsequence(token) {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"heading\", \"expected heading on stack\");\n        if (!node.depth) {\n            const depth = this.sliceSerialize(token).length;\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(depth === 1 || depth === 2 || depth === 3 || depth === 4 || depth === 5 || depth === 6, \"expected `depth` between `1` and `6`\");\n            node.depth = depth;\n        }\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitsetextheadingtext() {\n        this.data.setextHeadingSlurpLineEnding = true;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitsetextheadinglinesequence(token) {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"heading\", \"expected heading on stack\");\n        node.depth = this.sliceSerialize(token).codePointAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__.codes.equalsTo ? 1 : 2;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitsetextheading() {\n        this.data.setextHeadingSlurpLineEnding = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onenterdata(token) {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"children\" in node, \"expected parent on stack\");\n        /** @type {Array<Nodes>} */ const siblings = node.children;\n        let tail = siblings[siblings.length - 1];\n        if (!tail || tail.type !== \"text\") {\n            // Add a new text node.\n            tail = text();\n            tail.position = {\n                start: point(token.start),\n                // @ts-expect-error: we’ll add `end` later.\n                end: undefined\n            };\n            siblings.push(tail);\n        }\n        this.stack.push(tail);\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitdata(token) {\n        const tail = this.stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, \"expected a `node` to be on the stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"value\" in tail, \"expected a `literal` to be on the stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, \"expected `node` to have an open position\");\n        tail.value += this.sliceSerialize(token);\n        tail.position.end = point(token.end);\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitlineending(token) {\n        const context = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(context, \"expected `node`\");\n        // If we’re at a hard break, include the line ending in there.\n        if (this.data.atHardBreak) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"children\" in context, \"expected `parent`\");\n            const tail = context.children[context.children.length - 1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, \"expected tail to have a starting position\");\n            tail.position.end = point(token.end);\n            this.data.atHardBreak = undefined;\n            return;\n        }\n        if (!this.data.setextHeadingSlurpLineEnding && config.canContainEols.includes(context.type)) {\n            onenterdata.call(this, token);\n            onexitdata.call(this, token);\n        }\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexithardbreak() {\n        this.data.atHardBreak = true;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexithtmlflow() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"html\", \"expected html on stack\");\n        node.value = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexithtmltext() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"html\", \"expected html on stack\");\n        node.value = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodetext() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"inlineCode\", \"expected inline code on stack\");\n        node.value = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitlink() {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"link\", \"expected link on stack\");\n        // Note: there are also `identifier` and `label` fields on this link node!\n        // These are used / cleaned here.\n        // To do: clean.\n        if (this.data.inReference) {\n            /** @type {ReferenceType} */ const referenceType = this.data.referenceType || \"shortcut\";\n            node.type += \"Reference\";\n            // @ts-expect-error: mutate.\n            node.referenceType = referenceType;\n            // @ts-expect-error: mutate.\n            delete node.url;\n            delete node.title;\n        } else {\n            // @ts-expect-error: mutate.\n            delete node.identifier;\n            // @ts-expect-error: mutate.\n            delete node.label;\n        }\n        this.data.referenceType = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitimage() {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\", \"expected image on stack\");\n        // Note: there are also `identifier` and `label` fields on this link node!\n        // These are used / cleaned here.\n        // To do: clean.\n        if (this.data.inReference) {\n            /** @type {ReferenceType} */ const referenceType = this.data.referenceType || \"shortcut\";\n            node.type += \"Reference\";\n            // @ts-expect-error: mutate.\n            node.referenceType = referenceType;\n            // @ts-expect-error: mutate.\n            delete node.url;\n            delete node.title;\n        } else {\n            // @ts-expect-error: mutate.\n            delete node.identifier;\n            // @ts-expect-error: mutate.\n            delete node.label;\n        }\n        this.data.referenceType = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitlabeltext(token) {\n        const string = this.sliceSerialize(token);\n        const ancestor = this.stack[this.stack.length - 2];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor, \"expected ancestor on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor.type === \"image\" || ancestor.type === \"link\", \"expected image or link on stack\");\n        // @ts-expect-error: stash this on the node, as it might become a reference\n        // later.\n        ancestor.label = (0,micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__.decodeString)(string);\n        // @ts-expect-error: same as above.\n        ancestor.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(string).toLowerCase();\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitlabel() {\n        const fragment = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(fragment, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(fragment.type === \"fragment\", \"expected fragment on stack\");\n        const value = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\" || node.type === \"link\", \"expected image or link on stack\");\n        // Assume a reference.\n        this.data.inReference = true;\n        if (node.type === \"link\") {\n            /** @type {Array<PhrasingContent>} */ const children = fragment.children;\n            node.children = children;\n        } else {\n            node.alt = value;\n        }\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitresourcedestinationstring() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\" || node.type === \"link\", \"expected image or link on stack\");\n        node.url = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitresourcetitlestring() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\" || node.type === \"link\", \"expected image or link on stack\");\n        node.title = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitresource() {\n        this.data.inReference = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onenterreference() {\n        this.data.referenceType = \"collapsed\";\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitreferencestring(token) {\n        const label = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\" || node.type === \"link\", \"expected image reference or link reference on stack\");\n        // @ts-expect-error: stash this on the node, as it might become a reference\n        // later.\n        node.label = label;\n        // @ts-expect-error: same as above.\n        node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(this.sliceSerialize(token)).toLowerCase();\n        this.data.referenceType = \"full\";\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcharacterreferencemarker(token) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(token.type === \"characterReferenceMarkerNumeric\" || token.type === \"characterReferenceMarkerHexadecimal\");\n        this.data.characterReferenceType = token.type;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcharacterreferencevalue(token) {\n        const data = this.sliceSerialize(token);\n        const type = this.data.characterReferenceType;\n        /** @type {string} */ let value;\n        if (type) {\n            value = (0,micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__.decodeNumericCharacterReference)(data, type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.characterReferenceMarkerNumeric ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseHexadecimal);\n            this.data.characterReferenceType = undefined;\n        } else {\n            const result = (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__.decodeNamedCharacterReference)(data);\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result !== false, \"expected reference to decode\");\n            value = result;\n        }\n        const tail = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, \"expected `node`\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"value\" in tail, \"expected `node.value`\");\n        tail.value += value;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcharacterreference(token) {\n        const tail = this.stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, \"expected `node`\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, \"expected `node.position`\");\n        tail.position.end = point(token.end);\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitautolinkprotocol(token) {\n        onexitdata.call(this, token);\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"link\", \"expected link on stack\");\n        node.url = this.sliceSerialize(token);\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitautolinkemail(token) {\n        onexitdata.call(this, token);\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"link\", \"expected link on stack\");\n        node.url = \"mailto:\" + this.sliceSerialize(token);\n    }\n    //\n    // Creaters.\n    //\n    /** @returns {Blockquote} */ function blockQuote() {\n        return {\n            type: \"blockquote\",\n            children: []\n        };\n    }\n    /** @returns {Code} */ function codeFlow() {\n        return {\n            type: \"code\",\n            lang: null,\n            meta: null,\n            value: \"\"\n        };\n    }\n    /** @returns {InlineCode} */ function codeText() {\n        return {\n            type: \"inlineCode\",\n            value: \"\"\n        };\n    }\n    /** @returns {Definition} */ function definition() {\n        return {\n            type: \"definition\",\n            identifier: \"\",\n            label: null,\n            title: null,\n            url: \"\"\n        };\n    }\n    /** @returns {Emphasis} */ function emphasis() {\n        return {\n            type: \"emphasis\",\n            children: []\n        };\n    }\n    /** @returns {Heading} */ function heading() {\n        return {\n            type: \"heading\",\n            // @ts-expect-error `depth` will be set later.\n            depth: 0,\n            children: []\n        };\n    }\n    /** @returns {Break} */ function hardBreak() {\n        return {\n            type: \"break\"\n        };\n    }\n    /** @returns {Html} */ function html() {\n        return {\n            type: \"html\",\n            value: \"\"\n        };\n    }\n    /** @returns {Image} */ function image() {\n        return {\n            type: \"image\",\n            title: null,\n            url: \"\",\n            alt: null\n        };\n    }\n    /** @returns {Link} */ function link() {\n        return {\n            type: \"link\",\n            title: null,\n            url: \"\",\n            children: []\n        };\n    }\n    /**\n   * @param {Token} token\n   * @returns {List}\n   */ function list(token) {\n        return {\n            type: \"list\",\n            ordered: token.type === \"listOrdered\",\n            start: null,\n            spread: token._spread,\n            children: []\n        };\n    }\n    /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */ function listItem(token) {\n        return {\n            type: \"listItem\",\n            spread: token._spread,\n            checked: null,\n            children: []\n        };\n    }\n    /** @returns {Paragraph} */ function paragraph() {\n        return {\n            type: \"paragraph\",\n            children: []\n        };\n    }\n    /** @returns {Strong} */ function strong() {\n        return {\n            type: \"strong\",\n            children: []\n        };\n    }\n    /** @returns {Text} */ function text() {\n        return {\n            type: \"text\",\n            value: \"\"\n        };\n    }\n    /** @returns {ThematicBreak} */ function thematicBreak() {\n        return {\n            type: \"thematicBreak\"\n        };\n    }\n}\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */ function point(d) {\n    return {\n        line: d.line,\n        column: d.column,\n        offset: d.offset\n    };\n}\n/**\n * @param {Config} combined\n * @param {Array<Array<Extension> | Extension>} extensions\n * @returns {undefined}\n */ function configure(combined, extensions) {\n    let index = -1;\n    while(++index < extensions.length){\n        const value = extensions[index];\n        if (Array.isArray(value)) {\n            configure(combined, value);\n        } else {\n            extension(combined, value);\n        }\n    }\n}\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {undefined}\n */ function extension(combined, extension) {\n    /** @type {keyof Extension} */ let key;\n    for(key in extension){\n        if (own.call(extension, key)) {\n            switch(key){\n                case \"canContainEols\":\n                    {\n                        const right = extension[key];\n                        if (right) {\n                            combined[key].push(...right);\n                        }\n                        break;\n                    }\n                case \"transforms\":\n                    {\n                        const right = extension[key];\n                        if (right) {\n                            combined[key].push(...right);\n                        }\n                        break;\n                    }\n                case \"enter\":\n                case \"exit\":\n                    {\n                        const right = extension[key];\n                        if (right) {\n                            Object.assign(combined[key], right);\n                        }\n                        break;\n                    }\n            }\n        }\n    }\n}\n/** @type {OnEnterError} */ function defaultOnError(left, right) {\n    if (left) {\n        throw new Error(\"Cannot close `\" + left.type + \"` (\" + (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({\n            start: left.start,\n            end: left.end\n        }) + \"): a different token (`\" + right.type + \"`, \" + (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({\n            start: right.start,\n            end: right.end\n        }) + \") is open\");\n    } else {\n        throw new Error(\"Cannot close document, a token (`\" + right.type + \"`, \" + (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({\n            start: right.start,\n            end: right.end\n        }) + \") is still open\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/mdast-util-from-markdown/dev/lib/index.js\n");

/***/ })

};
;
{"date":"Fri Jun 20 2025 19:42:49 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":290289.234},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":67325952,"heapUsed":39370680,"rss":112529408},"pid":23016,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 19:42:49","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 20:42:02 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":293841.546},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":67850240,"heapUsed":39282744,"rss":113164288},"pid":44592,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 20:42:02","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 20:43:31 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":293931.187},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":67325952,"heapUsed":38687448,"rss":107524096},"pid":40320,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 20:43:31","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 20:54:00 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":294560.031},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":66801664,"heapUsed":39282776,"rss":106614784},"pid":44660,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 20:54:00","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 20:54:43 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":294602.796},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67850240,"heapUsed":40231048,"rss":108187648},"pid":14512,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 20:54:43","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 20:56:06 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":294686.484},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":66539520,"heapUsed":39297216,"rss":106037248},"pid":43444,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 20:56:06","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 20:56:47 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":294727.343},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67850240,"heapUsed":40268008,"rss":108310528},"pid":14848,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 20:56:47","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 20:59:33 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":294893.265},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67850240,"heapUsed":39984184,"rss":107577344},"pid":14308,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 20:59:33","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:01:38 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295018.156},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":67063808,"heapUsed":39196648,"rss":107233280},"pid":4740,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:01:38","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:03:20 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295119.703},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67588096,"heapUsed":40467792,"rss":112168960},"pid":17792,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:03:20","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:04:15 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: analyticsRoutes is not defined\nReferenceError: analyticsRoutes is not defined\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js:137:27)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js:9:13)","os":{"loadavg":[0,0,0],"uptime":295174.734},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":67063808,"heapUsed":39205752,"rss":111923200},"pid":40940,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"ReferenceError: analyticsRoutes is not defined\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js:137:27)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js:9:13)","timestamp":"2025-06-20 21:04:15","trace":[{"column":27,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js","function":null,"line":137,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js","function":null,"line":9,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:05:25 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295244.546},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":67063808,"heapUsed":39541128,"rss":114315264},"pid":36360,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:05:25","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:05:34 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295254.125},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":67063808,"heapUsed":39301184,"rss":114003968},"pid":43464,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:05:34","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:05:46 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295265.984},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67850240,"heapUsed":39893672,"rss":112934912},"pid":39572,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:05:46","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:05:54 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295274.14},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":67325952,"heapUsed":38953648,"rss":112300032},"pid":44808,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:05:54","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:06:03 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: analyticsRoutes is not defined\nReferenceError: analyticsRoutes is not defined\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js:137:27)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js:9:13)","os":{"loadavg":[0,0,0],"uptime":295282.546},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":66801664,"heapUsed":39215264,"rss":111882240},"pid":22992,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"ReferenceError: analyticsRoutes is not defined\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js:137:27)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js:9:13)","timestamp":"2025-06-20 21:06:03","trace":[{"column":27,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js","function":null,"line":137,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js","function":null,"line":9,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:07:40 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295380.046},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":66801664,"heapUsed":38660104,"rss":113709056},"pid":13100,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:07:40","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:07:49 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295388.859},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":66539520,"heapUsed":38358216,"rss":110907392},"pid":11992,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:07:49","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:08:22 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295422.078},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":66801664,"heapUsed":38646736,"rss":111468544},"pid":15552,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:08:22","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:08:29 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295429.484},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":66801664,"heapUsed":38234736,"rss":111149056},"pid":27440,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:08:29","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:08:39 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295438.64},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66116,"external":3411884,"heapTotal":66539520,"heapUsed":39505512,"rss":111022080},"pid":44748,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:08:39","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:08:48 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295447.718},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3411885,"heapTotal":67588096,"heapUsed":37593592,"rss":107200512},"pid":41868,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:08:48","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:09:15 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295475.046},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67588096,"heapUsed":40188632,"rss":107630592},"pid":39636,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:09:15","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:12:53 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295692.968},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67588096,"heapUsed":40001720,"rss":106131456},"pid":37980,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:12:53","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":40,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:17:32 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":295972.453},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67850240,"heapUsed":39809976,"rss":107843584},"pid":43500,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:17:32","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":44,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:23:39 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":296338.953},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67325952,"heapUsed":40466728,"rss":107335680},"pid":42012,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:23:39","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":44,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:24:26 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":296386.531},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67588096,"heapUsed":40488736,"rss":107651072},"pid":44704,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:24:26","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":44,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:29:12 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":296671.812},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67588096,"heapUsed":40397984,"rss":107601920},"pid":43404,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:29:12","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":44,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:29:43 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":296703.046},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67588096,"heapUsed":40526520,"rss":107757568},"pid":32372,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:44:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:29:43","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":44,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:30:12 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Router.use() requires a middleware function but got a undefined\nTypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:47:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":296732.468},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65940,"external":3386401,"heapTotal":67588096,"heapUsed":40152416,"rss":107847680},"pid":21968,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: Router.use() requires a middleware function but got a undefined\n    at Function.use (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:469:13)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js:47:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-20 21:30:12","trace":[{"column":13,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"Function.use","line":469,"method":"use","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\analytics.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 21:48:14 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":297814.437},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68792320,"heapUsed":41928176,"rss":119631872},"pid":38060,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-20 21:48:14","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Fri Jun 20 2025 21:50:24 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":297944.484},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68792320,"heapUsed":42212504,"rss":119889920},"pid":29168,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-20 21:50:24","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-table";
exports.ids = ["vendor-chunks/micromark-extension-gfm-table"];
exports.modules = {

/***/ "(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js":
/*!*************************************************************************!*\
  !*** ../node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditMap: () => (/* binding */ EditMap)\n/* harmony export */ });\n/**\n * @import {Event} from 'micromark-util-types'\n */ // Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */ /**\n * Tracks a bunch of edits.\n */ class EditMap {\n    /**\n   * Create a new edit map.\n   */ constructor(){\n        /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */ this.map = [];\n    }\n    /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {undefined}\n   */ add(index, remove, add) {\n        addImplementation(this, index, remove, add);\n    }\n    // To do: add this when moving to `micromark`.\n    // /**\n    //  * Create an edit: but insert `add` before existing additions.\n    //  *\n    //  * @param {number} index\n    //  * @param {number} remove\n    //  * @param {Array<Event>} add\n    //  * @returns {undefined}\n    //  */\n    // addBefore(index, remove, add) {\n    //   addImplementation(this, index, remove, add, true)\n    // }\n    /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {undefined}\n   */ consume(events) {\n        this.map.sort(function(a, b) {\n            return a[0] - b[0];\n        });\n        /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */ if (this.map.length === 0) {\n            return;\n        }\n        // To do: if links are added in events, like they are in `markdown-rs`,\n        // this is needed.\n        // // Calculate jumps: where items in the current list move to.\n        // /** @type {Array<Jump>} */\n        // const jumps = []\n        // let index = 0\n        // let addAcc = 0\n        // let removeAcc = 0\n        // while (index < this.map.length) {\n        //   const [at, remove, add] = this.map[index]\n        //   removeAcc += remove\n        //   addAcc += add.length\n        //   jumps.push([at, removeAcc, addAcc])\n        //   index += 1\n        // }\n        //\n        // . shiftLinks(events, jumps)\n        let index = this.map.length;\n        /** @type {Array<Array<Event>>} */ const vecs = [];\n        while(index > 0){\n            index -= 1;\n            vecs.push(events.slice(this.map[index][0] + this.map[index][1]), this.map[index][2]);\n            // Truncate rest.\n            events.length = this.map[index][0];\n        }\n        vecs.push(events.slice());\n        events.length = 0;\n        let slice = vecs.pop();\n        while(slice){\n            for (const element of slice){\n                events.push(element);\n            }\n            slice = vecs.pop();\n        }\n        // Truncate everything.\n        this.map.length = 0;\n    }\n}\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {undefined}\n */ function addImplementation(editMap, at, remove, add) {\n    let index = 0;\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */ if (remove === 0 && add.length === 0) {\n        return;\n    }\n    while(index < editMap.map.length){\n        if (editMap.map[index][0] === at) {\n            editMap.map[index][1] += remove;\n            // To do: before not used by tables, use when moving to micromark.\n            // if (before) {\n            //   add.push(...editMap.map[index][2])\n            //   editMap.map[index][2] = add\n            // } else {\n            editMap.map[index][2].push(...add);\n            // }\n            return;\n        }\n        index += 1;\n    }\n    editMap.map.push([\n        at,\n        remove,\n        add\n    ]);\n} // /**\n //  * Shift `previous` and `next` links according to `jumps`.\n //  *\n //  * This fixes links in case there are events removed or added between them.\n //  *\n //  * @param {Array<Event>} events\n //  * @param {Array<Jump>} jumps\n //  */\n // function shiftLinks(events, jumps) {\n //   let jumpIndex = 0\n //   let index = 0\n //   let add = 0\n //   let rm = 0\n //   while (index < events.length) {\n //     const rmCurr = rm\n //     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n //       add = jumps[jumpIndex][2]\n //       rm = jumps[jumpIndex][1]\n //       jumpIndex += 1\n //     }\n //     // Ignore items that will be removed.\n //     if (rm > rmCurr) {\n //       index += rm - rmCurr\n //     } else {\n //       // ?\n //       // if let Some(link) = &events[index].link {\n //       //     if let Some(next) = link.next {\n //       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n //       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n //       //             add = jumps[jumpIndex].2;\n //       //             rm = jumps[jumpIndex].1;\n //       //             jumpIndex += 1;\n //       //         }\n //       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n //       //         index = next;\n //       //         continue;\n //       //     }\n //       // }\n //       index += 1\n //     }\n //   }\n // }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/html.js":
/*!*********************************************************************!*\
  !*** ../node_modules/micromark-extension-gfm-table/dev/lib/html.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableHtml: () => (/* binding */ gfmTableHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */ \nconst alignment = {\n    none: \"\",\n    left: ' align=\"left\"',\n    right: ' align=\"right\"',\n    center: ' align=\"center\"'\n};\n// To do: micromark@5: use `infer` here, when all events are exposed.\n/**\n * Create an HTML extension for `micromark` to support GitHub tables when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub tables when serializing to HTML.\n */ function gfmTableHtml() {\n    return {\n        enter: {\n            table (token) {\n                const tableAlign = token._align;\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `_align`\");\n                this.lineEndingIfNeeded();\n                this.tag(\"<table>\");\n                this.setData(\"tableAlign\", tableAlign);\n            },\n            tableBody () {\n                this.tag(\"<tbody>\");\n            },\n            tableData () {\n                const tableAlign = this.getData(\"tableAlign\");\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                const align = alignment[tableAlign[tableColumn]];\n                if (align === undefined) {\n                    // Capture results to ignore them.\n                    this.buffer();\n                } else {\n                    this.lineEndingIfNeeded();\n                    this.tag(\"<td\" + align + \">\");\n                }\n            },\n            tableHead () {\n                this.lineEndingIfNeeded();\n                this.tag(\"<thead>\");\n            },\n            tableHeader () {\n                const tableAlign = this.getData(\"tableAlign\");\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                const align = alignment[tableAlign[tableColumn]];\n                this.lineEndingIfNeeded();\n                this.tag(\"<th\" + align + \">\");\n            },\n            tableRow () {\n                this.setData(\"tableColumn\", 0);\n                this.lineEndingIfNeeded();\n                this.tag(\"<tr>\");\n            }\n        },\n        exit: {\n            // Overwrite the default code text data handler to unescape escaped pipes when\n            // they are in tables.\n            codeTextData (token) {\n                let value = this.sliceSerialize(token);\n                if (this.getData(\"tableAlign\")) {\n                    value = value.replace(/\\\\([\\\\|])/g, replace);\n                }\n                this.raw(this.encode(value));\n            },\n            table () {\n                this.setData(\"tableAlign\");\n                // Note: we don’t set `slurpAllLineEndings` anymore, in delimiter rows,\n                // but we do need to reset it to match a funky newline GH generates for\n                // list items combined with tables.\n                this.setData(\"slurpAllLineEndings\");\n                this.lineEndingIfNeeded();\n                this.tag(\"</table>\");\n            },\n            tableBody () {\n                this.lineEndingIfNeeded();\n                this.tag(\"</tbody>\");\n            },\n            tableData () {\n                const tableAlign = this.getData(\"tableAlign\");\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                if (tableColumn in tableAlign) {\n                    this.tag(\"</td>\");\n                    this.setData(\"tableColumn\", tableColumn + 1);\n                } else {\n                    // Stop capturing.\n                    this.resume();\n                }\n            },\n            tableHead () {\n                this.lineEndingIfNeeded();\n                this.tag(\"</thead>\");\n            },\n            tableHeader () {\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                this.tag(\"</th>\");\n                this.setData(\"tableColumn\", tableColumn + 1);\n            },\n            tableRow () {\n                const tableAlign = this.getData(\"tableAlign\");\n                let tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                while(tableColumn < tableAlign.length){\n                    this.lineEndingIfNeeded();\n                    this.tag(\"<td\" + alignment[tableAlign[tableColumn]] + \"></td>\");\n                    tableColumn++;\n                }\n                this.setData(\"tableColumn\", tableColumn);\n                this.lineEndingIfNeeded();\n                this.tag(\"</tr>\");\n            }\n        }\n    };\n}\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */ function replace($0, $1) {\n    // Pipes work, backslashes don’t (but can’t escape pipes).\n    return $1 === \"|\" ? $1 : $0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/html.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/infer.js":
/*!**********************************************************************!*\
  !*** ../node_modules/micromark-extension-gfm-table/dev/lib/infer.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableAlign: () => (/* binding */ gfmTableAlign)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */ /**\n * @typedef {'center' | 'left' | 'none' | 'right'} Align\n */ \n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Readonly<Array<Event>>} events\n *   List of events.\n * @param {number} index\n *   Table enter event.\n * @returns {Array<Align>}\n *   List of aligns.\n */ function gfmTableAlign(events, index) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(events[index][1].type === \"table\", \"expected table\");\n    let inDelimiterRow = false;\n    /** @type {Array<Align>} */ const align = [];\n    while(index < events.length){\n        const event = events[index];\n        if (inDelimiterRow) {\n            if (event[0] === \"enter\") {\n                // Start of alignment value: set a new column.\n                // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n                if (event[1].type === \"tableContent\") {\n                    align.push(events[index + 1][1].type === \"tableDelimiterMarker\" ? \"left\" : \"none\");\n                }\n            } else if (event[1].type === \"tableContent\") {\n                if (events[index - 1][1].type === \"tableDelimiterMarker\") {\n                    const alignIndex = align.length - 1;\n                    align[alignIndex] = align[alignIndex] === \"left\" ? \"center\" : \"right\";\n                }\n            } else if (event[1].type === \"tableDelimiterRow\") {\n                break;\n            }\n        } else if (event[0] === \"enter\" && event[1].type === \"tableDelimiterRow\") {\n            inDelimiterRow = true;\n        }\n        index += 1;\n    }\n    return align;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/infer.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/syntax.js":
/*!***********************************************************************!*\
  !*** ../node_modules/micromark-extension-gfm-table/dev/lib/syntax.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTable: () => (/* binding */ gfmTable)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(rsc)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(rsc)/../node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/../node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var _edit_map_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./edit-map.js */ \"(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\");\n/* harmony import */ var _infer_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infer.js */ \"(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/infer.js\");\n/**\n * @import {Event, Extension, Point, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ /**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */ \n\n\n\n\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   table syntax.\n */ function gfmTable() {\n    return {\n        flow: {\n            null: {\n                name: \"table\",\n                tokenize: tokenizeTable,\n                resolveAll: resolveTable\n            }\n        }\n    };\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeTable(effects, ok, nok) {\n    const self = this;\n    let size = 0;\n    let sizeB = 0;\n    /** @type {boolean | undefined} */ let seen;\n    return start;\n    /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */ function start(code) {\n        let index = self.events.length - 1;\n        while(index > -1){\n            const type = self.events[index][1].type;\n            if (type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding || // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n            type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix) index--;\n            else break;\n        }\n        const tail = index > -1 ? self.events[index][1].type : null;\n        const next = tail === \"tableHead\" || tail === \"tableRow\" ? bodyRowStart : headRowBefore;\n        // Don’t allow lazy body rows.\n        if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n            return nok(code);\n        }\n        return next(code);\n    }\n    /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowBefore(code) {\n        effects.enter(\"tableHead\");\n        effects.enter(\"tableRow\");\n        return headRowStart(code);\n    }\n    /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowStart(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            return headRowBreak(code);\n        }\n        // To do: micromark-js should let us parse our own whitespace in extensions,\n        // like `markdown-rs`:\n        //\n        // ```js\n        // // 4+ spaces.\n        // if (markdownSpace(code)) {\n        //   return nok(code)\n        // }\n        // ```\n        seen = true;\n        // Count the first character, that isn’t a pipe, double.\n        sizeB += 1;\n        return headRowBreak(code);\n    }\n    /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowBreak(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n            return nok(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n            if (sizeB > 1) {\n                sizeB = 0;\n                // To do: check if this works.\n                // Feel free to interrupt:\n                self.interrupt = true;\n                effects.exit(\"tableRow\");\n                effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding);\n                effects.consume(code);\n                effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding);\n                return headDelimiterStart;\n            }\n            // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n            return nok(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            // To do: check if this is fine.\n            // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n            // State::Retry(space_or_tab(tokenizer))\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        sizeB += 1;\n        if (seen) {\n            seen = false;\n            // Header cell count.\n            size += 1;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.enter(\"tableCellDivider\");\n            effects.consume(code);\n            effects.exit(\"tableCellDivider\");\n            // Whether a delimiter was seen.\n            seen = true;\n            return headRowBreak;\n        }\n        // Anything else is cell data.\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n        return headRowData(code);\n    }\n    /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowData(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n            return headRowBreak(code);\n        }\n        effects.consume(code);\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? headRowEscape : headRowData;\n    }\n    /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */ function headRowEscape(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.consume(code);\n            return headRowData;\n        }\n        return headRowData(code);\n    }\n    /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterStart(code) {\n        // Reset `interrupt`.\n        self.interrupt = false;\n        // Note: in `markdown-rs`, we need to handle piercing here too.\n        if (self.parser.lazy[self.now().line]) {\n            return nok(code);\n        }\n        effects.enter(\"tableDelimiterRow\");\n        // Track if we’ve seen a `:` or `|`.\n        seen = false;\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(self.parser.constructs.disable.null, \"expected `disabled.null`\");\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headDelimiterBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix, self.parser.constructs.disable.null.includes(\"codeIndented\") ? undefined : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize)(code);\n        }\n        return headDelimiterBefore(code);\n    }\n    /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterBefore(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n            return headDelimiterValueBefore(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            seen = true;\n            // If we start with a pipe, we open a cell marker.\n            effects.enter(\"tableCellDivider\");\n            effects.consume(code);\n            effects.exit(\"tableCellDivider\");\n            return headDelimiterCellBefore;\n        }\n        // More whitespace / empty row not allowed at start.\n        return headDelimiterNok(code);\n    }\n    /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterCellBefore(code) {\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headDelimiterValueBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        return headDelimiterValueBefore(code);\n    }\n    /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterValueBefore(code) {\n        // Align: left.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n            sizeB += 1;\n            seen = true;\n            effects.enter(\"tableDelimiterMarker\");\n            effects.consume(code);\n            effects.exit(\"tableDelimiterMarker\");\n            return headDelimiterLeftAlignmentAfter;\n        }\n        // Align: none.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n            sizeB += 1;\n            // To do: seems weird that this *isn’t* left aligned, but that state is used?\n            return headDelimiterLeftAlignmentAfter(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            return headDelimiterCellAfter(code);\n        }\n        return headDelimiterNok(code);\n    }\n    /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterLeftAlignmentAfter(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n            effects.enter(\"tableDelimiterFiller\");\n            return headDelimiterFiller(code);\n        }\n        // Anything else is not ok after the left-align colon.\n        return headDelimiterNok(code);\n    }\n    /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterFiller(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n            effects.consume(code);\n            return headDelimiterFiller;\n        }\n        // Align is `center` if it was `left`, `right` otherwise.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n            seen = true;\n            effects.exit(\"tableDelimiterFiller\");\n            effects.enter(\"tableDelimiterMarker\");\n            effects.consume(code);\n            effects.exit(\"tableDelimiterMarker\");\n            return headDelimiterRightAlignmentAfter;\n        }\n        effects.exit(\"tableDelimiterFiller\");\n        return headDelimiterRightAlignmentAfter(code);\n    }\n    /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterRightAlignmentAfter(code) {\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headDelimiterCellAfter, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        return headDelimiterCellAfter(code);\n    }\n    /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterCellAfter(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            return headDelimiterBefore(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            // Exit when:\n            // * there was no `:` or `|` at all (it’s a thematic break or setext\n            //   underline instead)\n            // * the header cell count is not the delimiter cell count\n            if (!seen || size !== sizeB) {\n                return headDelimiterNok(code);\n            }\n            // Note: in markdown-rs`, a reset is needed here.\n            effects.exit(\"tableDelimiterRow\");\n            effects.exit(\"tableHead\");\n            // To do: in `markdown-rs`, resolvers need to be registered manually.\n            // effects.register_resolver(ResolveName::GfmTable)\n            return ok(code);\n        }\n        return headDelimiterNok(code);\n    }\n    /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterNok(code) {\n        // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n        return nok(code);\n    }\n    /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowStart(code) {\n        // Note: in `markdown-rs` we need to manually take care of a prefix,\n        // but in `micromark-js` that is done for us, so if we’re here, we’re\n        // never at whitespace.\n        effects.enter(\"tableRow\");\n        return bodyRowBreak(code);\n    }\n    /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowBreak(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.enter(\"tableCellDivider\");\n            effects.consume(code);\n            effects.exit(\"tableCellDivider\");\n            return bodyRowBreak;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            effects.exit(\"tableRow\");\n            return ok(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, bodyRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        // Anything else is cell content.\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n        return bodyRowData(code);\n    }\n    /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowData(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n            return bodyRowBreak(code);\n        }\n        effects.consume(code);\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? bodyRowEscape : bodyRowData;\n    }\n    /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowEscape(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.consume(code);\n            return bodyRowData;\n        }\n        return bodyRowData(code);\n    }\n}\n/** @type {Resolver} */ function resolveTable(events, context) {\n    let index = -1;\n    let inFirstCellAwaitingPipe = true;\n    /** @type {RowKind} */ let rowKind = 0;\n    /** @type {Range} */ let lastCell = [\n        0,\n        0,\n        0,\n        0\n    ];\n    /** @type {Range} */ let cell = [\n        0,\n        0,\n        0,\n        0\n    ];\n    let afterHeadAwaitingFirstBodyRow = false;\n    let lastTableEnd = 0;\n    /** @type {Token | undefined} */ let currentTable;\n    /** @type {Token | undefined} */ let currentBody;\n    /** @type {Token | undefined} */ let currentCell;\n    const map = new _edit_map_js__WEBPACK_IMPORTED_MODULE_6__.EditMap();\n    while(++index < events.length){\n        const event = events[index];\n        const token = event[1];\n        if (event[0] === \"enter\") {\n            // Start of head.\n            if (token.type === \"tableHead\") {\n                afterHeadAwaitingFirstBodyRow = false;\n                // Inject previous (body end and) table end.\n                if (lastTableEnd !== 0) {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, \"there should be a table opening\");\n                    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody);\n                    currentBody = undefined;\n                    lastTableEnd = 0;\n                }\n                // Inject table start.\n                currentTable = {\n                    type: \"table\",\n                    start: Object.assign({}, token.start),\n                    // Note: correct end is set later.\n                    end: Object.assign({}, token.end)\n                };\n                map.add(index, 0, [\n                    [\n                        \"enter\",\n                        currentTable,\n                        context\n                    ]\n                ]);\n            } else if (token.type === \"tableRow\" || token.type === \"tableDelimiterRow\") {\n                inFirstCellAwaitingPipe = true;\n                currentCell = undefined;\n                lastCell = [\n                    0,\n                    0,\n                    0,\n                    0\n                ];\n                cell = [\n                    0,\n                    index + 1,\n                    0,\n                    0\n                ];\n                // Inject table body start.\n                if (afterHeadAwaitingFirstBodyRow) {\n                    afterHeadAwaitingFirstBodyRow = false;\n                    currentBody = {\n                        type: \"tableBody\",\n                        start: Object.assign({}, token.start),\n                        // Note: correct end is set later.\n                        end: Object.assign({}, token.end)\n                    };\n                    map.add(index, 0, [\n                        [\n                            \"enter\",\n                            currentBody,\n                            context\n                        ]\n                    ]);\n                }\n                rowKind = token.type === \"tableDelimiterRow\" ? 2 : currentBody ? 3 : 1;\n            } else if (rowKind && (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data || token.type === \"tableDelimiterMarker\" || token.type === \"tableDelimiterFiller\")) {\n                inFirstCellAwaitingPipe = false;\n                // First value in cell.\n                if (cell[2] === 0) {\n                    if (lastCell[1] !== 0) {\n                        cell[0] = cell[1];\n                        currentCell = flushCell(map, context, lastCell, rowKind, undefined, currentCell);\n                        lastCell = [\n                            0,\n                            0,\n                            0,\n                            0\n                        ];\n                    }\n                    cell[2] = index;\n                }\n            } else if (token.type === \"tableCellDivider\") {\n                if (inFirstCellAwaitingPipe) {\n                    inFirstCellAwaitingPipe = false;\n                } else {\n                    if (lastCell[1] !== 0) {\n                        cell[0] = cell[1];\n                        currentCell = flushCell(map, context, lastCell, rowKind, undefined, currentCell);\n                    }\n                    lastCell = cell;\n                    cell = [\n                        lastCell[1],\n                        index,\n                        0,\n                        0\n                    ];\n                }\n            }\n        } else if (token.type === \"tableHead\") {\n            afterHeadAwaitingFirstBodyRow = true;\n            lastTableEnd = index;\n        } else if (token.type === \"tableRow\" || token.type === \"tableDelimiterRow\") {\n            lastTableEnd = index;\n            if (lastCell[1] !== 0) {\n                cell[0] = cell[1];\n                currentCell = flushCell(map, context, lastCell, rowKind, index, currentCell);\n            } else if (cell[1] !== 0) {\n                currentCell = flushCell(map, context, cell, rowKind, index, currentCell);\n            }\n            rowKind = 0;\n        } else if (rowKind && (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data || token.type === \"tableDelimiterMarker\" || token.type === \"tableDelimiterFiller\")) {\n            cell[3] = index;\n        }\n    }\n    if (lastTableEnd !== 0) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, \"expected table opening\");\n        flushTableEnd(map, context, lastTableEnd, currentTable, currentBody);\n    }\n    map.consume(context.events);\n    // To do: move this into `html`, when events are exposed there.\n    // That’s what `markdown-rs` does.\n    // That needs updates to `mdast-util-gfm-table`.\n    index = -1;\n    while(++index < context.events.length){\n        const event = context.events[index];\n        if (event[0] === \"enter\" && event[1].type === \"table\") {\n            event[1]._align = (0,_infer_js__WEBPACK_IMPORTED_MODULE_7__.gfmTableAlign)(context.events, index);\n        }\n    }\n    return events;\n}\n/**\n * Generate a cell.\n *\n * @param {EditMap} map\n * @param {Readonly<TokenizeContext>} context\n * @param {Readonly<Range>} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */ // eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n    // `markdown-rs` uses:\n    // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n    const groupName = rowKind === 1 ? \"tableHeader\" : rowKind === 2 ? \"tableDelimiter\" : \"tableData\";\n    // `markdown-rs` uses:\n    // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n    const valueName = \"tableContent\";\n    // Insert an exit for the previous cell, if there is one.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //          ^-- exit\n    //           ^^^^-- this cell\n    // ```\n    if (range[0] !== 0) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(previousCell, \"expected previous cell enter\");\n        previousCell.end = Object.assign({}, getPoint(context.events, range[0]));\n        map.add(range[0], 0, [\n            [\n                \"exit\",\n                previousCell,\n                context\n            ]\n        ]);\n    }\n    // Insert enter of this cell.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //           ^-- enter\n    //           ^^^^-- this cell\n    // ```\n    const now = getPoint(context.events, range[1]);\n    previousCell = {\n        type: groupName,\n        start: Object.assign({}, now),\n        // Note: correct end is set later.\n        end: Object.assign({}, now)\n    };\n    map.add(range[1], 0, [\n        [\n            \"enter\",\n            previousCell,\n            context\n        ]\n    ]);\n    // Insert text start at first data start and end at last data end, and\n    // remove events between.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //            ^-- enter\n    //             ^-- exit\n    //           ^^^^-- this cell\n    // ```\n    if (range[2] !== 0) {\n        const relatedStart = getPoint(context.events, range[2]);\n        const relatedEnd = getPoint(context.events, range[3]);\n        /** @type {Token} */ const valueToken = {\n            type: valueName,\n            start: Object.assign({}, relatedStart),\n            end: Object.assign({}, relatedEnd)\n        };\n        map.add(range[2], 0, [\n            [\n                \"enter\",\n                valueToken,\n                context\n            ]\n        ]);\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(range[3] !== 0);\n        if (rowKind !== 2) {\n            // Fix positional info on remaining events\n            const start = context.events[range[2]];\n            const end = context.events[range[3]];\n            start[1].end = Object.assign({}, end[1].end);\n            start[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkText;\n            start[1].contentType = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText;\n            // Remove if needed.\n            if (range[3] > range[2] + 1) {\n                const a = range[2] + 1;\n                const b = range[3] - range[2] - 1;\n                map.add(a, b, []);\n            }\n        }\n        map.add(range[3] + 1, 0, [\n            [\n                \"exit\",\n                valueToken,\n                context\n            ]\n        ]);\n    }\n    // Insert an exit for the last cell, if at the row end.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //                    ^-- exit\n    //               ^^^^^^-- this cell (the last one contains two “between” parts)\n    // ```\n    if (rowEnd !== undefined) {\n        previousCell.end = Object.assign({}, getPoint(context.events, rowEnd));\n        map.add(rowEnd, 0, [\n            [\n                \"exit\",\n                previousCell,\n                context\n            ]\n        ]);\n        previousCell = undefined;\n    }\n    return previousCell;\n}\n/**\n * Generate table end (and table body end).\n *\n * @param {Readonly<EditMap>} map\n * @param {Readonly<TokenizeContext>} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */ // eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n    /** @type {Array<Event>} */ const exits = [];\n    const related = getPoint(context.events, index);\n    if (tableBody) {\n        tableBody.end = Object.assign({}, related);\n        exits.push([\n            \"exit\",\n            tableBody,\n            context\n        ]);\n    }\n    table.end = Object.assign({}, related);\n    exits.push([\n        \"exit\",\n        table,\n        context\n    ]);\n    map.add(index + 1, 0, exits);\n}\n/**\n * @param {Readonly<Array<Event>>} events\n * @param {number} index\n * @returns {Readonly<Point>}\n */ function getPoint(events, index) {\n    const event = events[index];\n    const side = event[0] === \"enter\" ? \"start\" : \"end\";\n    return event[1][side];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/micromark-extension-gfm-table/dev/lib/syntax.js\n");

/***/ })

};
;
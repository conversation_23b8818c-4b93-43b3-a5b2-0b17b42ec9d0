#!/bin/bash
# nginx配置检查和修复脚本

echo "=========================================="
echo "     龙驰新材料商城 - nginx配置检查     "
echo "=========================================="

# 检查nginx是否安装
if ! command -v nginx &> /dev/null; then
    echo "❌ nginx未安装"
    echo "安装nginx: sudo apt update && sudo apt install nginx -y"
    exit 1
fi

echo "✅ nginx已安装，版本: $(nginx -v 2>&1)"

echo ""
echo "1. 检查nginx服务状态..."
if systemctl is-active --quiet nginx; then
    echo "✅ nginx服务正在运行"
else
    echo "❌ nginx服务未运行"
    echo "启动nginx: sudo systemctl start nginx"
    echo "设置开机自启: sudo systemctl enable nginx"
fi

echo ""
echo "2. 检查nginx配置文件..."
echo "主配置文件检查:"
if nginx -t; then
    echo "✅ nginx主配置语法正确"
else
    echo "❌ nginx主配置有语法错误"
fi

echo ""
echo "3. 检查站点配置..."
sites_available="/etc/nginx/sites-available/gdlongchi.cn"
sites_enabled="/etc/nginx/sites-enabled/gdlongchi.cn"

if [ -f "$sites_available" ]; then
    echo "✅ 站点配置文件存在: $sites_available"
else
    echo "❌ 站点配置文件不存在: $sites_available"
    echo ""
    echo "创建基础站点配置? (y/n)"
    read -r create_config
    if [ "$create_config" = "y" ]; then
        echo "正在创建nginx站点配置..."
        
        sudo tee "$sites_available" > /dev/null << 'EOF'
server {
    listen 80;
    server_name gdlongchi.cn www.gdlongchi.cn;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 日志配置
    access_log /var/log/nginx/gdlongchi.cn.access.log;
    error_log /var/log/nginx/gdlongchi.cn.error.log;
    
    # 主页面和静态资源代理到前端
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # API路由代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # 静态文件直接服务
    location /images/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # Next.js 静态资源
    location /_next/static/ {
        proxy_pass http://127.0.0.1:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # favicon等
    location ~* \.(ico|css|js|gif|jpe?g|png|svg|woff2?)$ {
        proxy_pass http://127.0.0.1:3000;
        expires 1y;
        add_header Cache-Control "public";
    }
}
EOF
        echo "✅ 基础站点配置已创建"
    fi
fi

if [ -L "$sites_enabled" ]; then
    echo "✅ 站点配置已启用"
elif [ -f "$sites_available" ]; then
    echo "❌ 站点配置未启用"
    echo "启用站点配置? (y/n)"
    read -r enable_site
    if [ "$enable_site" = "y" ]; then
        sudo ln -sf "$sites_available" "$sites_enabled"
        echo "✅ 站点配置已启用"
    fi
fi

echo ""
echo "4. 检查端口监听..."
echo "80端口监听状态:"
if netstat -tlnp | grep ":80 "; then
    echo "✅ 80端口有服务监听"
    netstat -tlnp | grep ":80 "
else
    echo "❌ 80端口无服务监听"
fi

echo ""
echo "5. 测试配置并重载..."
if nginx -t; then
    echo "✅ 配置文件语法正确"
    echo "重载nginx配置? (y/n)"
    read -r reload_nginx
    if [ "$reload_nginx" = "y" ]; then
        if sudo systemctl reload nginx; then
            echo "✅ nginx配置已重载"
        else
            echo "❌ nginx重载失败"
            sudo systemctl status nginx
        fi
    fi
else
    echo "❌ 配置文件有语法错误，请修复后再重载"
fi

echo ""
echo "6. 快速访问测试..."
sleep 2

echo "测试nginx反向代理:"
if curl -I -s http://localhost | head -1; then
    echo "✅ nginx反向代理工作正常"
else
    echo "❌ nginx反向代理无响应"
fi

echo "测试域名访问 (如果DNS已解析):"
if curl -I -s http://gdlongchi.cn | head -1; then
    echo "✅ 域名访问正常"
else
    echo "❌ 域名访问失败 (可能是DNS问题)"
fi

echo ""
echo "=========================================="
echo "nginx配置检查完成！"
echo "=========================================="
echo ""
echo "📋 完成后的验证步骤："
echo "1. 浏览器访问: http://gdlongchi.cn"
echo "2. 检查API: http://gdlongchi.cn/api/products"
echo "3. 查看nginx日志: sudo tail -f /var/log/nginx/gdlongchi.cn.access.log"
echo "4. 如有问题，查看错误日志: sudo tail -f /var/log/nginx/gdlongchi.cn.error.log"
echo "=========================================="

/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/products/edit/[id]/page";
exports.ids = ["app/admin/products/edit/[id]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&page=%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&page=%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'products',\n        {\n        children: [\n        'edit',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/products/edit/[id]/page.tsx */ \"(rsc)/./src/app/admin/products/edit/[id]/page.tsx\")), \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/products/edit/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/products/edit/[id]/page\",\n        pathname: \"/admin/products/edit/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&page=%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CAnalyticsTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CAnalyticsTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/AnalyticsTracker.tsx */ \"(ssr)/./src/components/analytics/AnalyticsTracker.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CAnalyticsTracker.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cedit%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cedit%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/products/edit/[id]/page.tsx */ \"(ssr)/./src/app/admin/products/edit/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUNMb25nQ2hpTWFsbCU1QyU1Q2xjLW1hbGwtbmV3JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhZG1pbiU1QyU1Q3Byb2R1Y3RzJTVDJTVDZWRpdCU1QyU1QyU1QmlkJTVEJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtNQUFvSSIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvP2UwYzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxnaXRodWJcXFxcTG9uZ0NoaU1hbGxcXFxcbGMtbWFsbC1uZXdcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhZG1pblxcXFxwcm9kdWN0c1xcXFxlZGl0XFxcXFtpZF1cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CLongChiMall%5C%5Clc-mall-new%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cedit%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/products/edit/[id]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/admin/products/edit/[id]/page.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_ImageUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../../components/ui/ImageUpload */ \"(ssr)/./src/components/ui/ImageUpload.tsx\");\n/* harmony import */ var _utils_adminAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/adminAuth */ \"(ssr)/./src/utils/adminAuth.ts\");\n/* harmony import */ var _shared_config_productCategories__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/shared/config/productCategories */ \"(ssr)/../shared/config/productCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst EditProduct = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const productId = params.id;\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialLoading, setInitialLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: \"\",\n        name: \"\",\n        description: \"\",\n        category: \"\",\n        price: 0,\n        unit: \"吨\",\n        minOrderQuantity: 1,\n        stockQuantity: 0,\n        specifications: {},\n        technicalData: {},\n        applications: [],\n        features: [],\n        images: [],\n        status: \"active\",\n        isFeatured: false,\n        isHot: false,\n        isNew: false\n    });\n    // 监控formData变化的调试useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadProduct = async ()=>{\n            try {\n                console.log(\"正在加载产品ID:\", productId);\n                setInitialLoading(true);\n                const response = await fetch(`/api/products/${productId}`, {\n                    method: \"GET\",\n                    headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_5__.getAdminHeaders)()\n                });\n                console.log(\"API响应状态:\", response.status);\n                if (!response.ok) {\n                    throw new Error(`加载产品失败: ${response.status}`);\n                }\n                const result = await response.json();\n                console.log(\"API返回的完整响应:\", result);\n                // 检查响应格式 - 后端使用ApiResponse.success包装数据\n                if (!result.success) {\n                    throw new Error(result.message || \"加载产品数据失败\");\n                }\n                const product = result.data;\n                console.log(\"解析出的产品数据:\", product);\n                if (!product) {\n                    throw new Error(\"产品数据为空\");\n                }\n                // Transform backend data to form data\n                const newFormData = {\n                    id: product.id || \"\",\n                    name: product.name || \"\",\n                    description: product.description || \"\",\n                    category: product.category || \"\",\n                    price: Number(product.price) || 0,\n                    unit: product.unit || \"吨\",\n                    minOrderQuantity: Number(product.minOrderQuantity) || 1,\n                    stockQuantity: Number(product.stock || product.stockQuantity) || 0,\n                    specifications: product.specifications || {},\n                    technicalData: product.technicalData || {},\n                    applications: Array.isArray(product.applications) ? product.applications : [],\n                    features: Array.isArray(product.features) ? product.features : [],\n                    images: Array.isArray(product.images) ? product.images : [],\n                    status: product.status || \"active\",\n                    isFeatured: Boolean(product.isFeatured),\n                    isHot: Boolean(product.isHot),\n                    isNew: Boolean(product.isNew)\n                };\n                console.log(\"设置的表单数据:\", newFormData);\n                console.log(\"分类字段值:\", newFormData.category);\n                console.log(\"可用的分类选项:\", _shared_config_productCategories__WEBPACK_IMPORTED_MODULE_6__.categoryOptions);\n                setFormData(newFormData);\n            } catch (error) {\n                console.error(\"加载产品数据时出错:\", error);\n                const errorMessage = error instanceof Error ? error.message : \"未知错误\";\n                alert(`加载产品数据失败: ${errorMessage}`);\n            } finally{\n                setInitialLoading(false);\n            }\n        };\n        if (productId) {\n            loadProduct();\n        } else {\n            console.error(\"产品ID为空\");\n            setInitialLoading(false);\n        }\n    }, [\n        productId\n    ]);\n    // 调试用：监控formData变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"FormData已更新:\", {\n            category: formData.category,\n            name: formData.name,\n            initialLoading\n        });\n    }, [\n        formData.category,\n        formData.name,\n        initialLoading\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSpecificationChange = (key, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: {\n                    ...prev.specifications,\n                    [key]: value\n                }\n            }));\n    };\n    const handleTechnicalDataChange = (key, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                technicalData: {\n                    ...prev.technicalData,\n                    [key]: value\n                }\n            }));\n    };\n    const addSpecification = ()=>{\n        const newKey = `spec_${Date.now()}`;\n        handleSpecificationChange(newKey, \"\");\n    };\n    const removeSpecification = (key)=>{\n        setFormData((prev)=>{\n            const newSpecs = {\n                ...prev.specifications\n            };\n            delete newSpecs[key];\n            return {\n                ...prev,\n                specifications: newSpecs\n            };\n        });\n    };\n    const addTechnicalData = ()=>{\n        const newKey = `tech_${Date.now()}`;\n        handleTechnicalDataChange(newKey, \"\");\n    };\n    const removeTechnicalData = (key)=>{\n        setFormData((prev)=>{\n            const newTech = {\n                ...prev.technicalData\n            };\n            delete newTech[key];\n            return {\n                ...prev,\n                technicalData: newTech\n            };\n        });\n    };\n    const addDefaultTechnicalData = ()=>{\n        const defaultTechnicalData = {\n            \"外观\": \"透明液体、无色或淡黄色等\",\n            \"密度\": \"1.02 g/ml（25℃）\",\n            \"闪点\": \"≥135℃\",\n            \"黏度\": \"500-800 mPa\\xb7s（25℃）\",\n            \"酸值\": \"≤0.1 mgKOH/g\",\n            \"水分含量\": \"≤0.05%\",\n            \"固化温度\": \"120-140℃\",\n            \"使用温度\": \"-40℃ ~ +80℃\"\n        };\n        setFormData((prev)=>({\n                ...prev,\n                technicalData: {\n                    ...prev.technicalData,\n                    ...defaultTechnicalData\n                }\n            }));\n    };\n    const addApplication = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                applications: [\n                    ...prev.applications,\n                    \"\"\n                ]\n            }));\n    };\n    const updateApplication = (index, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                applications: prev.applications.map((app, i)=>i === index ? value : app)\n            }));\n    };\n    const removeApplication = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                applications: prev.applications.filter((_, i)=>i !== index)\n            }));\n    };\n    const addFeature = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                features: [\n                    ...prev.features,\n                    \"\"\n                ]\n            }));\n    };\n    const updateFeature = (index, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                features: prev.features.map((feature, i)=>i === index ? value : feature)\n            }));\n    };\n    const removeFeature = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                features: prev.features.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.name.trim()) {\n            alert(\"请输入产品名称\");\n            return;\n        }\n        if (!formData.category) {\n            alert(\"请选择产品分类\");\n            return;\n        }\n        if (formData.price <= 0) {\n            alert(\"请输入有效的产品价格\");\n            return;\n        }\n        try {\n            setLoading(true); // Transform form data for API\n            const productData = {\n                name: formData.name.trim(),\n                description: formData.description.trim(),\n                category: formData.category,\n                price: formData.price,\n                unit: formData.unit,\n                minOrderQuantity: formData.minOrderQuantity,\n                stock: formData.stockQuantity,\n                specifications: formData.specifications,\n                technicalData: formData.technicalData,\n                applications: formData.applications.filter((app)=>app.trim()),\n                features: formData.features.filter((feature)=>feature.trim()),\n                images: formData.images,\n                status: formData.status,\n                isFeatured: formData.isFeatured,\n                isHot: formData.isHot,\n                isNew: formData.isNew\n            };\n            const response = await fetch(`/api/products/${productId}`, {\n                method: \"PUT\",\n                headers: (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_5__.getAdminHeaders)(),\n                body: JSON.stringify(productData)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.message || \"更新产品失败\");\n            }\n            alert(\"产品更新成功！\");\n            router.push(\"/admin/products\");\n        } catch (error) {\n            console.error(\"Error updating product:\", error);\n            alert(error instanceof Error ? error.message : \"更新产品失败，请重试\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (initialLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"加载产品数据中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 318,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 317,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto py-8 px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/admin\",\n                                    className: \"hover:text-blue-600\",\n                                    children: \"管理后台\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"before:content-['/'] before:mr-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/admin/products\",\n                                    className: \"hover:text-blue-600\",\n                                    children: \"产品管理\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"before:content-['/'] before:mr-2 text-gray-900\",\n                                children: \"编辑产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-6 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"编辑产品\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"修改产品信息和配置\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/admin/products\",\n                                className: \"px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                                children: \"返回列表\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"基本信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"产品名称 *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"请输入产品名称\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"产品分类 *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.category,\n                                                    onChange: (e)=>{\n                                                        console.log(\"分类选择器值变更:\", e.target.value);\n                                                        handleInputChange(\"category\", e.target.value);\n                                                    },\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    required: true,\n                                                    children: _shared_config_productCategories__WEBPACK_IMPORTED_MODULE_6__.categoryOptions.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: cat.value,\n                                                            children: cat.label\n                                                        }, cat.value, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: [\n                                                        '当前分类值: \"',\n                                                        formData.category,\n                                                        '\" (类型: ',\n                                                        typeof formData.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"产品状态\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.status,\n                                                    onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    children: _shared_config_productCategories__WEBPACK_IMPORTED_MODULE_6__.statusOptions.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: status.value,\n                                                            children: status.label\n                                                        }, status.value, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"产品描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: formData.description,\n                                                    onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                    rows: 4,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"请输入产品描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"价格与库存\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"产品价格 *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: formData.price,\n                                                    onChange: (e)=>handleInputChange(\"price\", parseFloat(e.target.value) || 0),\n                                                    step: \"0.01\",\n                                                    min: \"0\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"0.00\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"计量单位\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.unit,\n                                                    onChange: (e)=>handleInputChange(\"unit\", e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    children: _shared_config_productCategories__WEBPACK_IMPORTED_MODULE_6__.unitOptions.map((unit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: unit.value,\n                                                            children: unit.label\n                                                        }, unit.value, false, {\n                                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"最小订购量\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: formData.minOrderQuantity,\n                                                    onChange: (e)=>handleInputChange(\"minOrderQuantity\", parseInt(e.target.value) || 1),\n                                                    min: \"1\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"库存数量\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: formData.stockQuantity,\n                                                    onChange: (e)=>handleInputChange(\"stockQuantity\", parseInt(e.target.value) || 0),\n                                                    min: \"0\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"产品图片\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    images: formData.images || [],\n                                    onChange: (images)=>handleInputChange(\"images\", images),\n                                    maxImages: 5\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"产品规格\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addSpecification,\n                                            className: \"px-3 py-1 text-sm text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100\",\n                                            children: \"+ 添加规格\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: Object.entries(formData.specifications).map(([key, value], index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: key,\n                                                    onChange: (e)=>{\n                                                        const newKey = e.target.value;\n                                                        const newSpecs = {\n                                                            ...formData.specifications\n                                                        };\n                                                        delete newSpecs[key];\n                                                        newSpecs[newKey] = value;\n                                                        handleInputChange(\"specifications\", newSpecs);\n                                                    },\n                                                    className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"规格名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: value,\n                                                    onChange: (e)=>handleSpecificationChange(key, e.target.value),\n                                                    className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"规格值\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeSpecification(key),\n                                                    className: \"px-3 py-2 text-red-600 bg-red-50 rounded-lg hover:bg-red-100\",\n                                                    children: \"删除\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 31\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 11\n                        }, undefined),\n                        \"          \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"技术参数\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: addDefaultTechnicalData,\n                                                    className: \"px-3 py-1 text-sm text-green-600 bg-green-50 rounded-lg hover:bg-green-100\",\n                                                    children: \"\\uD83D\\uDCCB 默认参数\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: addTechnicalData,\n                                                    className: \"px-3 py-1 text-sm text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100\",\n                                                    children: \"+ 添加参数\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: Object.entries(formData.technicalData).map(([key, value], index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: key,\n                                                    onChange: (e)=>{\n                                                        const newKey = e.target.value;\n                                                        const newTech = {\n                                                            ...formData.technicalData\n                                                        };\n                                                        delete newTech[key];\n                                                        newTech[newKey] = value;\n                                                        handleInputChange(\"technicalData\", newTech);\n                                                    },\n                                                    className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"参数名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: value,\n                                                    onChange: (e)=>handleTechnicalDataChange(key, e.target.value),\n                                                    className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"参数值\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeTechnicalData(key),\n                                                    className: \"px-3 py-2 text-red-600 bg-red-50 rounded-lg hover:bg-red-100\",\n                                                    children: \"删除\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 558,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"应用场景\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addApplication,\n                                            className: \"px-3 py-1 text-sm text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100\",\n                                            children: \"+ 添加应用\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: formData.applications.map((application, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: application,\n                                                    onChange: (e)=>updateApplication(index, e.target.value),\n                                                    className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"应用场景描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeApplication(index),\n                                                    className: \"px-3 py-2 text-red-600 bg-red-50 rounded-lg hover:bg-red-100\",\n                                                    children: \"删除\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"产品特点\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addFeature,\n                                            className: \"px-3 py-1 text-sm text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100\",\n                                            children: \"+ 添加特点\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: formData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: feature,\n                                                    onChange: (e)=>updateFeature(index, e.target.value),\n                                                    className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"产品特点描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeFeature(index),\n                                                    className: \"px-3 py-2 text-red-600 bg-red-50 rounded-lg hover:bg-red-100\",\n                                                    children: \"删除\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/admin/products\",\n                                        className: \"px-6 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                        children: loading ? \"更新中...\" : \"更新产品\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 328,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\edit\\\\[id]\\\\page.tsx\",\n        lineNumber: 327,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EditProduct);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/products/edit/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 3000,\n                    style: {\n                        background: \"#fff\",\n                        color: \"#333\",\n                        borderRadius: \"8px\",\n                        boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                    },\n                    success: {\n                        iconTheme: {\n                            primary: \"#22c55e\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/analytics/AnalyticsTracker.tsx":
/*!*******************************************************!*\
  !*** ./src/components/analytics/AnalyticsTracker.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsTracker)\n/* harmony export */ });\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/hooks/useAnalytics */ \"(ssr)/./src/hooks/useAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ /**\r\n * 访问追踪组件\r\n * Analytics Tracker - 处理页面访问追踪\r\n */ \nfunction AnalyticsTracker() {\n    // 使用页面追踪Hook\n    (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_0__.usePageTracking)();\n    // 这个组件不渲染任何内容\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hbmFseXRpY3MvQW5hbHl0aWNzVHJhY2tlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRUE7OztDQUdDLEdBRXNEO0FBRXhDLFNBQVNDO0lBQ3RCLGFBQWE7SUFDYkQsb0VBQWVBO0lBRWYsY0FBYztJQUNkLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2xjLW1hbGwtZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9hbmFseXRpY3MvQW5hbHl0aWNzVHJhY2tlci50c3g/ZTc5OCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG4vKipcclxuICog6K6/6Zeu6L+96Liq57uE5Lu2XHJcbiAqIEFuYWx5dGljcyBUcmFja2VyIC0g5aSE55CG6aG16Z2i6K6/6Zeu6L+96LiqXHJcbiAqL1xyXG5cclxuaW1wb3J0IHsgdXNlUGFnZVRyYWNraW5nIH0gZnJvbSAnQC9ob29rcy91c2VBbmFseXRpY3MnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQW5hbHl0aWNzVHJhY2tlcigpIHtcclxuICAvLyDkvb/nlKjpobXpnaLov73ouKpIb29rXHJcbiAgdXNlUGFnZVRyYWNraW5nKCk7XHJcblxyXG4gIC8vIOi/meS4que7hOS7tuS4jea4suafk+S7u+S9leWGheWuuVxyXG4gIHJldHVybiBudWxsO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VQYWdlVHJhY2tpbmciLCJBbmFseXRpY3NUcmFja2VyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/analytics/AnalyticsTracker.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ImageUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/ImageUpload.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageUpload: () => (/* binding */ ImageUpload),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Eye_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Plus,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Plus,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Plus,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _utils_adminAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/adminAuth */ \"(ssr)/./src/utils/adminAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ ImageUpload,default auto */ \n\n\n\n\nfunction ImageUpload({ images, onChange, maxImages = 5, acceptedTypes = [\n    \"image/jpeg\",\n    \"image/png\",\n    \"image/webp\"\n] }) {\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleFileSelect = async (event)=>{\n        const files = event.target.files;\n        if (!files || files.length === 0) return;\n        if (images.length + files.length > maxImages) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(`最多只能上传 ${maxImages} 张图片`);\n            return;\n        }\n        setUploading(true);\n        const newImages = [];\n        try {\n            for(let i = 0; i < files.length; i++){\n                const file = files[i];\n                // 检查文件类型\n                if (!acceptedTypes.includes(file.type)) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(`不支持的文件格式: ${file.name}`);\n                    continue;\n                }\n                // 检查文件大小 (5MB限制)\n                if (file.size > 5 * 1024 * 1024) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(`文件 ${file.name} 过大，请选择小于5MB的图片`);\n                    continue;\n                } // 上传文件到服务器\n                const formData = new FormData();\n                formData.append(\"image\", file);\n                // 获取认证头但不包含Content-Type（让浏览器自动设置multipart/form-data）\n                const authHeaders = (0,_utils_adminAuth__WEBPACK_IMPORTED_MODULE_3__.getAdminHeaders)();\n                delete authHeaders[\"Content-Type\"];\n                const response = await fetch(\"/api/admin/upload/image\", {\n                    method: \"POST\",\n                    headers: authHeaders,\n                    body: formData\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    newImages.push(data.url);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(`${file.name} 上传成功`);\n                } else {\n                    const error = await response.json();\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(`上传失败: ${error.message || \"未知错误\"}`);\n                }\n            }\n            if (newImages.length > 0) {\n                onChange([\n                    ...images,\n                    ...newImages\n                ]);\n            }\n        } catch (error) {\n            console.error(\"Upload error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"上传失败，请稍后重试\");\n        } finally{\n            setUploading(false);\n            // 清空输入框\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const handleRemoveImage = (index)=>{\n        const newImages = images.filter((_, i)=>i !== index);\n        onChange(newImages);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"图片已移除\");\n    };\n    const handlePreviewImage = (imageUrl)=>{\n        setPreviewImage(imageUrl);\n    };\n    const handleClosePreview = ()=>{\n        setPreviewImage(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                children: [\n                    images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square border-2 border-gray-200 rounded-lg overflow-hidden bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: `产品图片 ${index + 1}`,\n                                        className: \"w-full h-full object-cover\",\n                                        onError: (e)=>{\n                                            const target = e.target;\n                                            target.src = \"/images/placeholder.svg\";\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handlePreviewImage(image),\n                                                className: \"p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 transition-colors\",\n                                                title: \"预览\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handleRemoveImage(index),\n                                                className: \"p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\",\n                                                title: \"删除\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-2 left-2 bg-orange-500 text-white px-2 py-1 rounded text-xs font-bold\",\n                                    children: \"主图\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)),\n                    images.length < maxImages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-square border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-blue-500 hover:bg-blue-50 transition-colors\",\n                        onClick: ()=>fileInputRef.current?.click(),\n                        children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500 mt-2\",\n                                    children: \"上传中...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-8 h-8 text-gray-400 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"添加图片\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                ref: fileInputRef,\n                type: \"file\",\n                multiple: true,\n                accept: acceptedTypes.join(\",\"),\n                onChange: handleFileSelect,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                        children: \"或通过URL添加图片\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                placeholder: \"输入图片URL地址\",\n                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                onKeyPress: (e)=>{\n                                    if (e.key === \"Enter\") {\n                                        e.preventDefault();\n                                        const input = e.target;\n                                        const url = input.value.trim();\n                                        if (url) {\n                                            if (images.length >= maxImages) {\n                                                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(`最多只能添加 ${maxImages} 张图片`);\n                                                return;\n                                            }\n                                            onChange([\n                                                ...images,\n                                                url\n                                            ]);\n                                            input.value = \"\";\n                                            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"图片URL已添加\");\n                                        }\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: (e)=>{\n                                    const input = e.target.previousElementSibling;\n                                    const url = input.value.trim();\n                                    if (url) {\n                                        if (images.length >= maxImages) {\n                                            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(`最多只能添加 ${maxImages} 张图片`);\n                                            return;\n                                        }\n                                        onChange([\n                                            ...images,\n                                            url\n                                        ]);\n                                        input.value = \"\";\n                                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"图片URL已添加\");\n                                    }\n                                },\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"添加\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm text-gray-500 space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"• 支持 JPG、PNG、WebP 格式\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"• 单个文件大小不超过 5MB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"• 建议尺寸：800x800 像素\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"• 第一张图片将作为产品主图显示\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"• 最多可上传 \",\n                            maxImages,\n                            \" 张图片\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            previewImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75\",\n                onClick: handleClosePreview,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-4xl max-h-4xl p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: previewImage,\n                            alt: \"图片预览\",\n                            className: \"max-w-full max-h-full object-contain\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClosePreview,\n                            className: \"absolute top-4 right-4 p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\components\\\\ui\\\\ImageUpload.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageUpload);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ImageUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAnalytics.ts":
/*!***********************************!*\
  !*** ./src/hooks/useAnalytics.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEventTracking: () => (/* binding */ useEventTracking),\n/* harmony export */   usePageTracking: () => (/* binding */ usePageTracking),\n/* harmony export */   useScrollTracking: () => (/* binding */ useScrollTracking),\n/* harmony export */   useTimeTracking: () => (/* binding */ useTimeTracking)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_analytics__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/analytics */ \"(ssr)/./src/services/analytics.ts\");\n/**\r\n * 用户访问追踪 Hook\r\n * Use Analytics Hook - React Hook for analytics tracking\r\n */ \n\n\n/**\r\n * 页面访问追踪 Hook\r\n */ function usePageTracking() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // 延迟执行以确保页面完全加载\n        const timer = setTimeout(()=>{\n            _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackPageView(pathname);\n        }, 100);\n        return ()=>clearTimeout(timer);\n    }, [\n        pathname\n    ]);\n}\n/**\r\n * 事件追踪 Hook\r\n */ function useEventTracking() {\n    const trackEvent = (event, eventData)=>{\n        _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackEvent(event, eventData);\n    };\n    const trackClick = (elementName, additionalData)=>{\n        trackEvent(\"click\", {\n            element: elementName,\n            ...additionalData\n        });\n    };\n    const trackFormSubmit = (formName, additionalData)=>{\n        trackEvent(\"form_submit\", {\n            form: formName,\n            ...additionalData\n        });\n    };\n    const trackDownload = (fileName, fileType)=>{\n        trackEvent(\"download\", {\n            fileName,\n            fileType\n        });\n    };\n    const trackSearch = (query, resultsCount)=>{\n        trackEvent(\"search\", {\n            query,\n            resultsCount\n        });\n    };\n    const trackProductView = (productId, productName, category)=>{\n        trackEvent(\"product_view\", {\n            productId,\n            productName,\n            category\n        });\n    };\n    const trackAddToCart = (productId, productName, quantity, price)=>{\n        trackEvent(\"add_to_cart\", {\n            productId,\n            productName,\n            quantity,\n            price\n        });\n    };\n    return {\n        trackEvent,\n        trackClick,\n        trackFormSubmit,\n        trackDownload,\n        trackSearch,\n        trackProductView,\n        trackAddToCart\n    };\n}\n/**\r\n * 滚动追踪 Hook\r\n */ function useScrollTracking(threshold = 50) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let hasTracked = false;\n        const trackingThreshold = threshold; // 滚动百分比阈值\n        const handleScroll = ()=>{\n            if (hasTracked) return;\n            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n            const documentHeight = document.documentElement.scrollHeight - window.innerHeight;\n            const scrollPercentage = scrollTop / documentHeight * 100;\n            if (scrollPercentage >= trackingThreshold) {\n                _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackEvent(\"scroll_threshold\", {\n                    threshold: trackingThreshold,\n                    percentage: Math.round(scrollPercentage)\n                });\n                hasTracked = true;\n            }\n        };\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        threshold\n    ]);\n}\n/**\r\n * 停留时间追踪 Hook\r\n */ function useTimeTracking(intervals = [\n    30,\n    60,\n    120,\n    300\n]) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const trackedIntervals = new Set();\n        const startTime = Date.now();\n        const checkIntervals = ()=>{\n            const elapsed = Math.floor((Date.now() - startTime) / 1000);\n            intervals.forEach((interval)=>{\n                if (elapsed >= interval && !trackedIntervals.has(interval)) {\n                    _services_analytics__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackEvent(\"time_on_page\", {\n                        interval,\n                        elapsed\n                    });\n                    trackedIntervals.add(interval);\n                }\n            });\n        };\n        const timer = setInterval(checkIntervals, 1000);\n        return ()=>clearInterval(timer);\n    }, [\n        intervals\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAnalytics.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/analytics.ts":
/*!***********************************!*\
  !*** ./src/services/analytics.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\r\n * 用户访问追踪服务\r\n * Analytics Service - 前端访问数据收集和发送\r\n */ class AnalyticsService {\n    constructor(){\n        this.sessionId = this.generateSessionId();\n        this.startTime = Date.now();\n        this.lastPath = \"\";\n        this.deviceInfo = this.getDeviceInfo();\n        this.isTracking = false;\n        // 只在客户端运行\n        if (false) {}\n    }\n    /**\r\n   * 初始化追踪\r\n   */ init() {\n        // 检查是否启用追踪\n        const trackingEnabled = process.env.NEXT_PUBLIC_ANALYTICS_ENABLED !== \"false\";\n        if (!trackingEnabled) {\n            console.log(\"Analytics tracking is disabled\");\n            return;\n        }\n        this.isTracking = true;\n        // 页面加载时记录访问\n        this.trackPageView();\n        // 监听页面变化（SPA路由）\n        this.setupRouteChangeTracking();\n        // 监听页面离开\n        this.setupBeforeUnload();\n        // 监听页面可见性变化\n        this.setupVisibilityChange();\n    }\n    /**\r\n   * 生成会话ID\r\n   */ generateSessionId() {\n        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    }\n    /**\r\n   * 获取设备信息\r\n   */ getDeviceInfo() {\n        if (true) {\n            return {\n                deviceType: \"unknown\",\n                browserName: \"unknown\",\n                browserVersion: \"unknown\",\n                osName: \"unknown\",\n                osVersion: \"unknown\"\n            };\n        }\n        const userAgent = navigator.userAgent;\n        // 检测设备类型\n        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n        const isTablet = /iPad|Android(?=.*Mobile)/i.test(userAgent);\n        const deviceType = isTablet ? \"tablet\" : isMobile ? \"mobile\" : \"desktop\";\n        // 检测浏览器\n        let browserName = \"unknown\";\n        let browserVersion = \"unknown\";\n        if (userAgent.indexOf(\"Chrome\") > -1) {\n            browserName = \"Chrome\";\n            browserVersion = userAgent.match(/Chrome\\/([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Firefox\") > -1) {\n            browserName = \"Firefox\";\n            browserVersion = userAgent.match(/Firefox\\/([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Safari\") > -1) {\n            browserName = \"Safari\";\n            browserVersion = userAgent.match(/Version\\/([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Edge\") > -1) {\n            browserName = \"Edge\";\n            browserVersion = userAgent.match(/Edge\\/([0-9.]+)/)?.[1] || \"unknown\";\n        }\n        // 检测操作系统\n        let osName = \"unknown\";\n        let osVersion = \"unknown\";\n        if (userAgent.indexOf(\"Windows\") > -1) {\n            osName = \"Windows\";\n            osVersion = userAgent.match(/Windows NT ([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"Mac\") > -1) {\n            osName = \"macOS\";\n            osVersion = userAgent.match(/Mac OS X ([0-9_]+)/)?.[1]?.replace(/_/g, \".\") || \"unknown\";\n        } else if (userAgent.indexOf(\"Linux\") > -1) {\n            osName = \"Linux\";\n        } else if (userAgent.indexOf(\"Android\") > -1) {\n            osName = \"Android\";\n            osVersion = userAgent.match(/Android ([0-9.]+)/)?.[1] || \"unknown\";\n        } else if (userAgent.indexOf(\"iOS\") > -1) {\n            osName = \"iOS\";\n            osVersion = userAgent.match(/OS ([0-9_]+)/)?.[1]?.replace(/_/g, \".\") || \"unknown\";\n        }\n        return {\n            deviceType,\n            browserName,\n            browserVersion,\n            osName,\n            osVersion\n        };\n    }\n    /**\r\n   * 记录页面访问\r\n   */ trackPageView(customPath) {\n        if (!this.isTracking || \"undefined\" === \"undefined\") return;\n        const currentPath = customPath || window.location.pathname;\n        // 如果路径没有变化且不是首次访问，则不记录\n        if (this.lastPath === currentPath && this.lastPath !== \"\") {\n            return;\n        }\n        // 计算上一页面的停留时间\n        const duration = this.lastPath ? Math.floor((Date.now() - this.startTime) / 1000) : 0;\n        const data = {\n            sessionId: this.sessionId,\n            url: window.location.href,\n            pathname: currentPath,\n            referer: document.referrer || \"\",\n            duration,\n            deviceType: this.deviceInfo.deviceType,\n            browserName: this.deviceInfo.browserName,\n            browserVersion: this.deviceInfo.browserVersion,\n            osName: this.deviceInfo.osName,\n            osVersion: this.deviceInfo.osVersion,\n            screenResolution: `${screen.width}x${screen.height}`,\n            language: navigator.language,\n            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n            event: \"pageview\"\n        };\n        this.sendData(data);\n        // 更新状态\n        this.lastPath = currentPath;\n        this.startTime = Date.now();\n    }\n    /**\r\n   * 记录自定义事件\r\n   */ trackEvent(event, eventData) {\n        if (!this.isTracking || \"undefined\" === \"undefined\") return;\n        const data = {\n            sessionId: this.sessionId,\n            url: window.location.href,\n            pathname: window.location.pathname,\n            referer: document.referrer || \"\",\n            deviceType: this.deviceInfo.deviceType,\n            browserName: this.deviceInfo.browserName,\n            browserVersion: this.deviceInfo.browserVersion,\n            osName: this.deviceInfo.osName,\n            osVersion: this.deviceInfo.osVersion,\n            screenResolution: `${screen.width}x${screen.height}`,\n            language: navigator.language,\n            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n            event,\n            eventData\n        };\n        this.sendData(data);\n    }\n    /**\r\n   * 设置路由变化监听\r\n   */ setupRouteChangeTracking() {\n        // 监听 popstate 事件（浏览器前进后退）\n        window.addEventListener(\"popstate\", ()=>{\n            setTimeout(()=>this.trackPageView(), 100);\n        });\n        // 监听 pushState 和 replaceState（SPA路由变化）\n        const originalPushState = history.pushState;\n        const originalReplaceState = history.replaceState;\n        history.pushState = function(state, title, url) {\n            originalPushState.apply(history, arguments);\n            setTimeout(()=>analyticsService.trackPageView(), 100);\n        };\n        history.replaceState = function(state, title, url) {\n            originalReplaceState.apply(history, arguments);\n            setTimeout(()=>analyticsService.trackPageView(), 100);\n        };\n    }\n    /**\r\n   * 设置页面离开监听\r\n   */ setupBeforeUnload() {\n        window.addEventListener(\"beforeunload\", ()=>{\n            // 记录最后一次停留时间\n            const duration = Math.floor((Date.now() - this.startTime) / 1000);\n            if (duration > 0) {\n                // 使用 sendBeacon 确保数据能发送出去\n                this.sendDataSync({\n                    sessionId: this.sessionId,\n                    url: window.location.href,\n                    pathname: window.location.pathname,\n                    referer: document.referrer || \"\",\n                    duration,\n                    deviceType: this.deviceInfo.deviceType,\n                    browserName: this.deviceInfo.browserName,\n                    browserVersion: this.deviceInfo.browserVersion,\n                    osName: this.deviceInfo.osName,\n                    osVersion: this.deviceInfo.osVersion,\n                    screenResolution: `${screen.width}x${screen.height}`,\n                    language: navigator.language,\n                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                    event: \"pageview\"\n                });\n            }\n        });\n    }\n    /**\r\n   * 设置页面可见性变化监听\r\n   */ setupVisibilityChange() {\n        document.addEventListener(\"visibilitychange\", ()=>{\n            if (document.hidden) {\n                // 页面隐藏时记录停留时间\n                const duration = Math.floor((Date.now() - this.startTime) / 1000);\n                if (duration > 5) {\n                    this.trackEvent(\"page_hidden\", {\n                        duration\n                    });\n                }\n            } else {\n                // 页面重新可见时重新开始计时\n                this.startTime = Date.now();\n            }\n        });\n    }\n    /**\r\n   * 发送数据到服务器\r\n   */ async sendData(data) {\n        try {\n            await fetch(\"/api/analytics/track\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n        } catch (error) {\n            console.warn(\"Analytics tracking failed:\", error);\n        }\n    }\n    /**\r\n   * 同步发送数据（用于页面离开时）\r\n   */ sendDataSync(data) {\n        try {\n            if (navigator.sendBeacon) {\n                navigator.sendBeacon(\"/api/analytics/track\", JSON.stringify(data));\n            }\n        } catch (error) {\n            console.warn(\"Analytics sync tracking failed:\", error);\n        }\n    }\n}\n// 创建全局实例\nconst analyticsService = new AnalyticsService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (analyticsService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/analytics.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/adminAuth.ts":
/*!********************************!*\
  !*** ./src/utils/adminAuth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminHeaders: () => (/* binding */ getAdminHeaders)\n/* harmony export */ });\n/**\r\n * 管理员认证相关工具函数\r\n */ /**\r\n * 获取管理员API请求头\r\n * @returns 包含Content-Type和管理员API密钥的请求头对象\r\n */ function getAdminHeaders() {\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // 获取管理员API密钥\n    let adminApiKey;\n    if (true) {\n        adminApiKey = \"lc_admin_dev_key_2025\" || 0 || 0;\n        console.log(\"Server-side Admin API Key source:\",  true ? \"NEXT_PUBLIC_ADMIN_API_KEY\" : 0);\n    } else {}\n    if (adminApiKey) {\n        headers[\"x-admin-api-key\"] = adminApiKey;\n    }\n    return headers;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvYWRtaW5BdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Q0FHQyxHQUNNLFNBQVNBO0lBQ2QsTUFBTUMsVUFBa0M7UUFDdEMsZ0JBQWdCO0lBQ2xCO0lBRUEsYUFBYTtJQUNiLElBQUlDO0lBQ0YsSUFBSSxJQUFrQixFQUFhO1FBQ25DQSxjQUFjQyx1QkFBcUMsSUFDdENBLENBQXlCLElBQ3pCO1FBQ2JJLFFBQVFDLEdBQUcsQ0FBQyxxQ0FDQUwsS0FBcUMsR0FBRyw4QkFDeENBLENBQThDO0lBQzdELE9BQU8sRUFJTDtJQUVELElBQUlELGFBQWE7UUFDZkQsT0FBTyxDQUFDLGtCQUFrQixHQUFHQztJQUMvQjtJQUVBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYy1tYWxsLWZyb250ZW5kLy4vc3JjL3V0aWxzL2FkbWluQXV0aC50cz8yNWM5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiDnrqHnkIblkZjorqTor4Hnm7jlhbPlt6Xlhbflh73mlbBcclxuICovXHJcblxyXG4vKipcclxuICog6I635Y+W566h55CG5ZGYQVBJ6K+35rGC5aS0XHJcbiAqIEByZXR1cm5zIOWMheWQq0NvbnRlbnQtVHlwZeWSjOeuoeeQhuWRmEFQSeWvhumSpeeahOivt+axguWktOWvueixoVxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGdldEFkbWluSGVhZGVycygpOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+IHtcclxuICBjb25zdCBoZWFkZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xyXG4gICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICB9O1xyXG4gIFxyXG4gIC8vIOiOt+WPlueuoeeQhuWRmEFQSeWvhumSpVxyXG4gIGxldCBhZG1pbkFwaUtleTogc3RyaW5nO1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7ICAgIC8vIOacjeWKoeWZqOerryAtIOS7jueOr+Wig+WPmOmHj+iOt+WPlu+8jOS8mOWFiOS9v+eUqE5FWFRfUFVCTElDX+WJjee8gOeahOWPmOmHj1xyXG4gICAgYWRtaW5BcGlLZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BRE1JTl9BUElfS0VZIHx8IFxyXG4gICAgICAgICAgICAgICAgIHByb2Nlc3MuZW52LkFETUlOX0FQSV9LRVkgfHwgXHJcbiAgICAgICAgICAgICAgICAgJ2xjX2FkbWluX2Rldl9rZXlfMjAyNSc7XHJcbiAgICBjb25zb2xlLmxvZygnU2VydmVyLXNpZGUgQWRtaW4gQVBJIEtleSBzb3VyY2U6JywgXHJcbiAgICAgICAgICAgICAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BRE1JTl9BUElfS0VZID8gJ05FWFRfUFVCTElDX0FETUlOX0FQSV9LRVknIDogXHJcbiAgICAgICAgICAgICAgICBwcm9jZXNzLmVudi5BRE1JTl9BUElfS0VZID8gJ0FETUlOX0FQSV9LRVknIDogJ2ZhbGxiYWNrJyk7ICBcclxuXHR9IGVsc2Uge1xyXG4gICAgLy8g5a6i5oi356uvIC0g5LuO5p6E5bu65pe25rOo5YWl55qE546v5aKD5Y+Y6YeP6I635Y+WXHJcbiAgICBhZG1pbkFwaUtleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FETUlOX0FQSV9LRVkgfHwgJ2xjX2FkbWluX2Rldl9rZXlfMjAyNSc7XHJcbiAgICBjb25zb2xlLmxvZygnQ2xpZW50LXNpZGUgdXNpbmcgTkVYVF9QVUJMSUNfQURNSU5fQVBJX0tFWScpO1xyXG4gIH1cclxuICBcclxuICBpZiAoYWRtaW5BcGlLZXkpIHtcclxuICAgIGhlYWRlcnNbJ3gtYWRtaW4tYXBpLWtleSddID0gYWRtaW5BcGlLZXk7XHJcbiAgfVxyXG4gIFxyXG4gIHJldHVybiBoZWFkZXJzO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJnZXRBZG1pbkhlYWRlcnMiLCJoZWFkZXJzIiwiYWRtaW5BcGlLZXkiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQURNSU5fQVBJX0tFWSIsIkFETUlOX0FQSV9LRVkiLCJjb25zb2xlIiwibG9nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/adminAuth.ts\n");

/***/ }),

/***/ "(ssr)/../shared/config/productCategories.ts":
/*!*********************************************!*\
  !*** ../shared/config/productCategories.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categoryDisplayNames: () => (/* binding */ categoryDisplayNames),\n/* harmony export */   categoryMapping: () => (/* binding */ categoryMapping),\n/* harmony export */   categoryOptions: () => (/* binding */ categoryOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getCategoryDisplayName: () => (/* binding */ getCategoryDisplayName),\n/* harmony export */   getCategoryValue: () => (/* binding */ getCategoryValue),\n/* harmony export */   isValidCategory: () => (/* binding */ isValidCategory),\n/* harmony export */   statusOptions: () => (/* binding */ statusOptions),\n/* harmony export */   unitOptions: () => (/* binding */ unitOptions)\n/* harmony export */ });\n/**\r\n * 产品分类配置 - 前后端共享\r\n * 包含分类映射、分类选项、分类显示名称等配置\r\n */ // 中文分类名称到英文分类值的映射\nconst categoryMapping = {\n    \"样品\": \"samples\",\n    \"潜固化剂\": \"latent-curing-agents\",\n    \"催化剂\": \"catalysts\",\n    \"分散剂\": \"dispersants\",\n    \"流平剂\": \"leveling-agents\",\n    \"助剂\": \"additives\",\n    \"原料\": \"raw-materials\",\n    \"附着力促进剂\": \"adhesion-promoters\",\n    \"固化剂\": \"curing-agents\",\n    \"设备\": \"equipment\",\n    \"扩链剂\": \"chain-extenders\",\n    \"消泡剂\": \"defoamers\",\n    \"生物基树脂\": \"bio-based-resins\",\n    \"粉体助剂\": \"powder-additives\",\n    \"防沉剂\": \"anti-settling-agents\"\n};\n// 分类选项配置（用于前端下拉选择器）\nconst categoryOptions = [\n    {\n        value: \"\",\n        label: \"请选择分类\"\n    },\n    {\n        value: \"catalysts\",\n        label: \"催化剂\"\n    },\n    {\n        value: \"curing-agents\",\n        label: \"固化剂\"\n    },\n    {\n        value: \"adhesion-promoters\",\n        label: \"附着力促进剂\"\n    },\n    {\n        value: \"dispersants\",\n        label: \"分散剂\"\n    },\n    {\n        value: \"chain-extenders\",\n        label: \"扩链剂\"\n    },\n    {\n        value: \"leveling-agents\",\n        label: \"流平剂\"\n    },\n    {\n        value: \"defoamers\",\n        label: \"消泡剂\"\n    },\n    {\n        value: \"latent-curing-agents\",\n        label: \"潜固化剂\"\n    },\n    {\n        value: \"bio-based-resins\",\n        label: \"生物基树脂\"\n    },\n    {\n        value: \"powder-additives\",\n        label: \"粉体助剂\"\n    },\n    {\n        value: \"anti-settling-agents\",\n        label: \"防沉剂\"\n    },\n    {\n        value: \"additives\",\n        label: \"助剂\"\n    },\n    {\n        value: \"raw-materials\",\n        label: \"原材料\"\n    },\n    {\n        value: \"equipment\",\n        label: \"设备\"\n    },\n    {\n        value: \"samples\",\n        label: \"样品\"\n    }\n];\n// 英文分类值到中文显示名称的映射\nconst categoryDisplayNames = {\n    \"catalysts\": \"催化剂\",\n    \"curing-agents\": \"固化剂\",\n    \"adhesion-promoters\": \"附着力促进剂\",\n    \"dispersants\": \"分散剂\",\n    \"chain-extenders\": \"扩链剂\",\n    \"leveling-agents\": \"流平剂\",\n    \"defoamers\": \"消泡剂\",\n    \"latent-curing-agents\": \"潜固化剂\",\n    \"bio-based-resins\": \"生物基树脂\",\n    \"powder-additives\": \"粉体助剂\",\n    \"anti-settling-agents\": \"防沉剂\",\n    \"additives\": \"助剂\",\n    \"raw-materials\": \"原材料\",\n    \"equipment\": \"设备\",\n    \"samples\": \"样品\"\n};\n// 产品状态选项\nconst statusOptions = [\n    {\n        value: \"\",\n        label: \"请选择状态\"\n    },\n    {\n        value: \"active\",\n        label: \"上架\"\n    },\n    {\n        value: \"inactive\",\n        label: \"下架\"\n    },\n    {\n        value: \"draft\",\n        label: \"草稿\"\n    },\n    {\n        value: \"out_of_stock\",\n        label: \"缺货\"\n    }\n];\n// 单位选项\nconst unitOptions = [\n    {\n        value: \"\",\n        label: \"请选择单位\"\n    },\n    {\n        value: \"g\",\n        label: \"克(g)\"\n    },\n    {\n        value: \"kg\",\n        label: \"千克(kg)\"\n    },\n    {\n        value: \"ml\",\n        label: \"毫升(ml)\"\n    },\n    {\n        value: \"l\",\n        label: \"升(l)\"\n    },\n    {\n        value: \"piece\",\n        label: \"件\"\n    },\n    {\n        value: \"box\",\n        label: \"盒\"\n    },\n    {\n        value: \"pack\",\n        label: \"包\"\n    },\n    {\n        value: \"set\",\n        label: \"套\"\n    }\n];\n/**\r\n * 根据分类值获取分类显示名称\r\n * @param {string} categoryValue - 英文分类值\r\n * @returns {string} 对应的中文分类名称，不存在则返回原值\r\n */ const getCategoryDisplayName = (categoryValue)=>{\n    return categoryDisplayNames[categoryValue] || categoryValue;\n};\n/**\r\n * 根据中文分类名称获取分类值\r\n * @param {string} categoryName - 中文分类名称\r\n * @returns {string|null} 对应的英文分类值，不存在则返回null\r\n */ const getCategoryValue = (categoryName)=>{\n    return categoryMapping[categoryName] || null;\n};\n/**\r\n * 检查分类值是否有效\r\n * @param {string} categoryValue - 分类值\r\n * @returns {boolean} 是否是有效的分类\r\n */ const isValidCategory = (categoryValue)=>{\n    return Object.values(categoryMapping).includes(categoryValue);\n};\n// 默认导出所有配置\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    categoryMapping,\n    categoryOptions,\n    categoryDisplayNames,\n    statusOptions,\n    unitOptions,\n    getCategoryDisplayName,\n    getCategoryValue,\n    isValidCategory\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../shared/config/productCategories.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"610e40cc88fd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGMtbWFsbC1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTViYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjYxMGU0MGNjODhmZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/products/edit/[id]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/admin/products/edit/[id]/page.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\LongChiMall\lc-mall-new\frontend\src\app\admin\products\edit\[id]\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _components_analytics_AnalyticsTracker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/analytics/AnalyticsTracker */ \"(rsc)/./src/components/analytics/AnalyticsTracker.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"龙驰新材料门户网站\",\n    description: \"专业聚氨酯材料供应商 - 提供优质的聚氨酯原料、助剂和技术支持\",\n    keywords: \"聚氨酯,聚氨酯材料,潜固化剂,催化剂,流平剂,分散剂,龙驰新材料\",\n    authors: [\n        {\n            name: \"广州市龙驰新材料科技有限公司\"\n        }\n    ],\n    creator: \"龙驰新材料\",\n    publisher: \"龙驰新材料\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(process.env.NEXT_PUBLIC_FRONTEND_URL || \"http://localhost:3000\"),\n    openGraph: {\n        type: \"website\",\n        locale: \"zh_CN\",\n        url: process.env.NEXT_PUBLIC_FRONTEND_URL || \"http://localhost:3000\",\n        title: \"龙驰新材料门户网站\",\n        description: \"专业聚氨酯材料供应商 - 提供优质的聚氨酯原料、助剂和技术支持\",\n        siteName: \"龙驰新材料\",\n        images: [\n            {\n                url: \"/images/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"龙驰新材料门户网站\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"龙驰新材料门户网站\",\n        description: \"专业聚氨酯材料供应商\",\n        images: [\n            \"/images/twitter-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    icons: {\n        icon: [\n            {\n                url: \"/favicon/favicon.ico\",\n                sizes: \"any\"\n            },\n            {\n                url: \"/favicon/favicon.svg\",\n                type: \"image/svg+xml\"\n            },\n            {\n                url: \"/favicon/favicon-96x96.png\",\n                sizes: \"96x96\",\n                type: \"image/png\"\n            }\n        ],\n        shortcut: \"/favicon/favicon.ico\",\n        apple: \"/favicon/apple-touch-icon.png\",\n        other: [\n            {\n                rel: \"icon\",\n                url: \"/favicon/web-app-manifest-192x192.png\",\n                sizes: \"192x192\",\n                type: \"image/png\"\n            },\n            {\n                rel: \"icon\",\n                url: \"/favicon/web-app-manifest-512x512.png\",\n                sizes: \"512x512\",\n                type: \"image/png\"\n            }\n        ]\n    },\n    manifest: \"/favicon/site.webmanifest\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.loli.net\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.loli.net/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-sans\",\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_3__.Providers, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_AnalyticsTracker__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 3000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\",\n                                borderRadius: \"8px\",\n                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                            },\n                            success: {\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 87,\n                columnNumber: 20\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 83,\n        columnNumber: 15\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\LongChiMall\lc-mall-new\frontend\src\app\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/analytics/AnalyticsTracker.tsx":
/*!*******************************************************!*\
  !*** ./src/components/analytics/AnalyticsTracker.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\LongChiMall\lc-mall-new\frontend\src\components\analytics\AnalyticsTracker.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&page=%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fedit%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
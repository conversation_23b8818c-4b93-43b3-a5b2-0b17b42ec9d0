"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/[id]/route";
exports.ids = ["app/api/products/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_LongChiMall_lc_mall_new_frontend_src_app_api_products_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/[id]/route.ts */ \"(rsc)/./src/app/api/products/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/[id]/route\",\n        pathname: \"/api/products/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/[id]/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\api\\\\products\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_LongChiMall_lc_mall_new_frontend_src_app_api_products_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/products/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/products/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/products/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// 禁用路由缓存\nconst dynamic = \"force-dynamic\";\nconst revalidate = 0;\n// Proxy to backend API for single product\nasync function GET(request, { params }) {\n    try {\n        const productId = params.id;\n        const apiBase = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n        // 转发请求头到后端\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n            \"Pragma\": \"no-cache\",\n            \"Expires\": \"0\"\n        };\n        // 转发认证头（如果存在）\n        const authHeader = request.headers.get(\"authorization\");\n        if (authHeader) {\n            headers[\"authorization\"] = authHeader;\n        }\n        const adminApiKey = request.headers.get(\"x-admin-api-key\");\n        if (adminApiKey) {\n            headers[\"x-admin-api-key\"] = adminApiKey;\n        }\n        const response = await fetch(`${apiBase}/products/${productId}`, {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    message: \"Unknown error\"\n                }));\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: errorData.message || \"Failed to fetch product\"\n            }, {\n                status: response.status\n            });\n        }\n        const result = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Update product\nasync function PUT(request, { params }) {\n    try {\n        const productId = params.id;\n        const apiBase = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n        const requestBody = await request.json();\n        // 转发请求头到后端\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        // 转发认证头（如果存在）\n        const authHeader = request.headers.get(\"authorization\");\n        if (authHeader) {\n            headers[\"authorization\"] = authHeader;\n        }\n        const adminApiKey = request.headers.get(\"x-admin-api-key\");\n        if (adminApiKey) {\n            headers[\"x-admin-api-key\"] = adminApiKey;\n        }\n        const response = await fetch(`${apiBase}/products/${productId}`, {\n            method: \"PUT\",\n            headers,\n            body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    message: \"Unknown error\"\n                }));\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: errorData.message || \"Failed to update product\"\n            }, {\n                status: response.status\n            });\n        }\n        const result = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error(\"Error updating product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Delete product\nasync function DELETE(request, { params }) {\n    try {\n        const productId = params.id;\n        const apiBase = \"http://localhost:5000/api\"?.replace(/\\/$/, \"\") || 0;\n        // 转发请求头到后端\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        // 转发认证头（如果存在）\n        const authHeader = request.headers.get(\"authorization\");\n        if (authHeader) {\n            headers[\"authorization\"] = authHeader;\n        }\n        const adminApiKey = request.headers.get(\"x-admin-api-key\");\n        if (adminApiKey) {\n            headers[\"x-admin-api-key\"] = adminApiKey;\n        }\n        const response = await fetch(`${apiBase}/products/${productId}`, {\n            method: \"DELETE\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    message: \"Unknown error\"\n                }));\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: errorData.message || \"Failed to delete product\"\n            }, {\n                status: response.status\n            });\n        }\n        const result = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error(\"Error deleting product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/[id]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
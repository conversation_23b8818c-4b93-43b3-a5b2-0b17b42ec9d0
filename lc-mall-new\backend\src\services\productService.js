/**
 * 产品服务 - 重构版本
 * 作为产品相关服务的统一入口，委托给专门的服务处理具体业务
 */

const ProductQueryService = require('./ProductQueryService');
const ProductManagementService = require('./ProductManagementService');
const logger = require('../utils/logger');

class ProductService {
  constructor() {
    this.queryService = new ProductQueryService();
    this.managementService = new ProductManagementService();
  }

  // ==================== 查询相关方法 ====================

  /**
   * 获取产品列表
   * @param {Object} options - 查询选项
   * @returns {Object} 产品列表和分页信息
   */
  async getProducts(options = {}) {
    try {
      return await this.queryService.getProducts(options);
    } catch (error) {
      logger.error('ProductService.getProducts error:', { error: error.message, options });
      throw error;
    }
  }

  /**
   * 根据ID获取产品详情
   * @param {string} productId - 产品ID
   * @param {boolean} incrementView - 是否增加浏览次数
   * @returns {Object} 产品详情
   */
  async getProductById(productId, incrementView = false) {
    try {
      return await this.queryService.getProductById(productId, incrementView);
    } catch (error) {
      logger.error('ProductService.getProductById error:', { error: error.message, productId });
      throw error;
    }
  }

  /**
   * 搜索产品
   * @param {Object} searchOptions - 搜索选项
   * @returns {Object} 搜索结果
   */
  async searchProducts(searchOptions = {}) {
    try {
      const { query, ...options } = searchOptions;
      return await this.queryService.searchProducts(query, options);
    } catch (error) {
      logger.error('ProductService.searchProducts error:', { error: error.message, searchOptions });
      throw error;
    }
  }

  /**
   * 根据分类获取产品
   * @param {string} category - 分类名称
   * @param {Object} options - 查询选项
   * @returns {Object} 产品列表
   */
  async getProductsByCategory(category, options = {}) {
    try {
      return await this.queryService.getProducts({ ...options, category });
    } catch (error) {
      logger.error('ProductService.getProductsByCategory error:', { error: error.message, category, options });
      throw error;
    }
  }

  /**
   * 获取热门产品
   * @param {number} limit - 返回数量限制
   * @returns {Object} 热门产品列表
   */
  async getHotProducts(limit = 10) {
    try {
      return await this.queryService.getHotProducts(limit);
    } catch (error) {
      logger.error('ProductService.getHotProducts error:', { error: error.message, limit });
      throw error;
    }
  }

  /**
   * 获取新品推荐
   * @param {number} limit - 返回数量限制
   * @returns {Object} 新品列表
   */
  async getNewProducts(limit = 10) {
    try {
      return await this.queryService.getNewProducts(limit);
    } catch (error) {
      logger.error('ProductService.getNewProducts error:', { error: error.message, limit });
      throw error;
    }
  }

  /**
   * 获取推荐产品
   * @param {number} limit - 返回数量限制
   * @returns {Object} 推荐产品列表
   */
  async getFeaturedProducts(limit = 10) {
    try {
      return await this.queryService.getProducts({ 
        limit, 
        filters: { featured: true, status: 'active' },
        sort: { sortBy: 'rating', sortOrder: 'desc' }
      });
    } catch (error) {
      logger.error('ProductService.getFeaturedProducts error:', { error: error.message, limit });
      throw error;
    }
  }

  /**
   * 获取相关产品推荐
   * @param {string} productId - 当前产品ID
   * @param {number} limit - 返回数量限制
   * @returns {Object} 相关产品列表
   */
  async getRelatedProducts(productId, limit = 6) {
    try {
      return await this.queryService.getRelatedProducts(productId, limit);
    } catch (error) {
      logger.error('ProductService.getRelatedProducts error:', { error: error.message, productId, limit });
      throw error;
    }
  }

  /**
   * 获取产品分类列表
   * @returns {Object} 分类列表
   */
  async getCategories() {
    try {
      return await this.queryService.getCategories();
    } catch (error) {
      logger.error('ProductService.getCategories error:', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取产品统计信息
   * @returns {Object} 统计信息
   */
  async getProductStats() {
    try {
      return await this.queryService.getProductStats();
    } catch (error) {
      logger.error('ProductService.getProductStats error:', { error: error.message });
      throw error;
    }
  }

  // ==================== 管理相关方法 ====================

  /**
   * 创建产品
   * @param {Object} productData - 产品数据
   * @param {string} createdBy - 创建者ID
   * @returns {Object} 创建结果
   */
  async createProduct(productData, createdBy = null) {
    try {
      return await this.managementService.createProduct(productData, createdBy);
    } catch (error) {
      logger.error('ProductService.createProduct error:', { error: error.message, productData, createdBy });
      throw error;
    }
  }

  /**
   * 更新产品
   * @param {string} productId - 产品ID
   * @param {Object} updateData - 更新数据
   * @param {string} updatedBy - 更新者ID
   * @returns {Object} 更新结果
   */
  async updateProduct(productId, updateData, updatedBy = null) {
    try {
      return await this.managementService.updateProduct(productId, updateData, updatedBy);
    } catch (error) {
      logger.error('ProductService.updateProduct error:', { error: error.message, productId, updateData, updatedBy });
      throw error;
    }
  }

  /**
   * 删除产品
   * @param {string} productId - 产品ID
   * @param {string} deletedBy - 删除者ID
   * @returns {Object} 删除结果
   */
  async deleteProduct(productId, deletedBy = null) {
    try {
      return await this.managementService.deleteProduct(productId, deletedBy);
    } catch (error) {
      logger.error('ProductService.deleteProduct error:', { error: error.message, productId, deletedBy });
      throw error;
    }
  }

  /**
   * 更新产品库存
   * @param {string} productId - 产品ID
   * @param {number} newStock - 新库存数量
   * @param {string} updatedBy - 更新者ID
   * @returns {Object} 更新结果
   */
  async updateStock(productId, newStock, updatedBy = null) {
    try {
      return await this.managementService.updateStock(productId, newStock, updatedBy);
    } catch (error) {
      logger.error('ProductService.updateStock error:', { error: error.message, productId, newStock, updatedBy });
      throw error;
    }
  }

  /**
   * 批量更新产品状态
   * @param {Array} productIds - 产品ID数组
   * @param {string} status - 新状态
   * @param {string} updatedBy - 更新者ID
   * @returns {Object} 批量更新结果
   */
  async batchUpdateStatus(productIds, status, updatedBy = null) {
    try {
      return await this.managementService.batchUpdateStatus(productIds, status, updatedBy);
    } catch (error) {
      logger.error('ProductService.batchUpdateStatus error:', { error: error.message, productIds, status, updatedBy });
      throw error;
    }
  }

  /**
   * 批量导入产品
   * @param {Array} productsData - 产品数据数组
   * @param {string} importedBy - 导入者ID
   * @returns {Object} 导入结果
   */
  async batchImportProducts(productsData, importedBy = null) {
    try {
      return await this.managementService.batchImportProducts(productsData, importedBy);
    } catch (error) {
      logger.error('ProductService.batchImportProducts error:', { error: error.message, count: productsData.length, importedBy });
      throw error;
    }
  }

  // ==================== 兼容性方法（保持向后兼容） ====================

  /**
   * 增加销量（兼容性方法）
   * @param {string} productId - 产品ID
   * @param {number} quantity - 销量增加数量
   */
  async incrementSales(productId, quantity = 1) {
    try {
      // 这个方法可以通过更新产品来实现
      const product = await this.queryService.getProductById(productId);
      if (product.success && product.data) {
        const newSalesCount = (product.data.salesCount || 0) + quantity;
        await this.managementService.updateProduct(productId, { salesCount: newSalesCount });
      }
      logger.debug('Product sales incremented', { productId, quantity });
    } catch (error) {
      logger.error('ProductService.incrementSales error:', { error: error.message, productId, quantity });
      throw error;
    }
  }

  // ==================== 内部工具方法 ====================

  /**
   * 生成产品slug
   * @param {string} name - 产品名称
   * @returns {string} slug
   */
  generateSlug(name) {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
}

// 导出单例实例，保持向后兼容
module.exports = new ProductService();

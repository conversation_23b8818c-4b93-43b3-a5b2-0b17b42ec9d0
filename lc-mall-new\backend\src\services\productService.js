/**
 * 产品服务 - 临时简化版本
 * 提供基本的产品数据，绕过复杂的Repository依赖
 */

const logger = require('../utils/logger');

class ProductService {
  constructor() {
    // 临时模拟数据
    this.mockProducts = this.generateMockProducts();
  }

  // ==================== 查询相关方法 ====================

  /**
   * 获取产品列表
   * @param {Object} options - 查询选项
   * @returns {Object} 产品列表和分页信息
   */
  async getProducts(options = {}) {
    try {
      const { page = 1, limit = 20, category, featured, inStock, sortBy = 'createdAt', sortOrder = 'desc' } = options;

      let products = [...this.mockProducts];

      // 应用过滤器
      if (category) {
        products = products.filter(p => p.category === category);
      }
      if (featured !== undefined) {
        products = products.filter(p => p.isFeatured === featured);
      }
      if (inStock !== undefined) {
        products = products.filter(p => (p.stockQuantity > 0) === inStock);
      }

      // 应用排序
      products.sort((a, b) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];
        if (sortOrder === 'desc') {
          return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });

      // 应用分页
      const total = products.length;
      const totalPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;
      const paginatedProducts = products.slice(offset, offset + limit);

      return {
        success: true,
        data: {
          products: paginatedProducts,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        },
        message: '产品列表获取成功'
      };
    } catch (error) {
      logger.error('ProductService.getProducts error:', { error: error.message, options });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  /**
   * 根据ID获取产品详情
   * @param {string} productId - 产品ID
   * @param {boolean} incrementView - 是否增加浏览次数
   * @returns {Object} 产品详情
   */
  async getProductById(productId, incrementView = false) {
    try {
      const product = this.mockProducts.find(p => p.id === productId);

      if (!product) {
        return {
          success: false,
          data: null,
          message: '产品不存在'
        };
      }

      // 模拟增加浏览次数
      if (incrementView) {
        product.viewCount = (product.viewCount || 0) + 1;
      }

      return {
        success: true,
        data: product,
        message: '产品详情获取成功'
      };
    } catch (error) {
      logger.error('ProductService.getProductById error:', { error: error.message, productId });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  /**
   * 搜索产品
   * @param {Object} searchOptions - 搜索选项
   * @returns {Object} 搜索结果
   */
  async searchProducts(searchOptions = {}) {
    try {
      const { query, page = 1, limit = 20, category } = searchOptions;

      let products = [...this.mockProducts];

      // 应用搜索过滤
      if (query && query.trim()) {
        const searchTerm = query.toLowerCase();
        products = products.filter(p =>
          p.name.toLowerCase().includes(searchTerm) ||
          p.description.toLowerCase().includes(searchTerm) ||
          p.category.toLowerCase().includes(searchTerm)
        );
      }

      // 应用分类过滤
      if (category) {
        products = products.filter(p => p.category === category);
      }

      // 应用分页
      const total = products.length;
      const totalPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;
      const paginatedProducts = products.slice(offset, offset + limit);

      return {
        success: true,
        data: {
          products: paginatedProducts,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          },
          query
        },
        message: '产品搜索完成'
      };
    } catch (error) {
      logger.error('ProductService.searchProducts error:', { error: error.message, searchOptions });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  /**
   * 根据分类获取产品
   * @param {string} category - 分类名称
   * @param {Object} options - 查询选项
   * @returns {Object} 产品列表
   */
  async getProductsByCategory(category, options = {}) {
    try {
      return await this.getProducts({ ...options, category });
    } catch (error) {
      logger.error('ProductService.getProductsByCategory error:', { error: error.message, category, options });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  /**
   * 获取热门产品
   * @param {number} limit - 返回数量限制
   * @returns {Object} 热门产品列表
   */
  async getHotProducts(limit = 10) {
    try {
      const hotProducts = this.mockProducts
        .filter(p => p.isHot && p.status === 'active')
        .sort((a, b) => b.viewCount - a.viewCount)
        .slice(0, limit);

      return {
        success: true,
        data: hotProducts,
        message: '热门产品获取成功'
      };
    } catch (error) {
      logger.error('ProductService.getHotProducts error:', { error: error.message, limit });
      return {
        success: false,
        data: [],
        message: error.message
      };
    }
  }

  /**
   * 获取新品推荐
   * @param {number} limit - 返回数量限制
   * @returns {Object} 新品列表
   */
  async getNewProducts(limit = 10) {
    try {
      const newProducts = this.mockProducts
        .filter(p => p.isNew && p.status === 'active')
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, limit);

      return {
        success: true,
        data: newProducts,
        message: '新品推荐获取成功'
      };
    } catch (error) {
      logger.error('ProductService.getNewProducts error:', { error: error.message, limit });
      return {
        success: false,
        data: [],
        message: error.message
      };
    }
  }

  /**
   * 获取推荐产品
   * @param {number} limit - 返回数量限制
   * @returns {Object} 推荐产品列表
   */
  async getFeaturedProducts(limit = 10) {
    try {
      const featuredProducts = this.mockProducts
        .filter(p => p.isFeatured && p.status === 'active')
        .sort((a, b) => b.rating - a.rating)
        .slice(0, limit);

      return {
        success: true,
        data: featuredProducts,
        message: '推荐产品获取成功'
      };
    } catch (error) {
      logger.error('ProductService.getFeaturedProducts error:', { error: error.message, limit });
      return {
        success: false,
        data: [],
        message: error.message
      };
    }
  }

  /**
   * 获取相关产品推荐
   * @param {string} productId - 当前产品ID
   * @param {number} limit - 返回数量限制
   * @returns {Object} 相关产品列表
   */
  async getRelatedProducts(productId, limit = 6) {
    try {
      const currentProduct = this.mockProducts.find(p => p.id === productId);
      if (!currentProduct) {
        return {
          success: false,
          data: [],
          message: '产品不存在'
        };
      }

      const relatedProducts = this.mockProducts
        .filter(p => p.id !== productId && p.category === currentProduct.category && p.status === 'active')
        .slice(0, limit);

      return {
        success: true,
        data: relatedProducts,
        message: '相关产品获取成功'
      };
    } catch (error) {
      logger.error('ProductService.getRelatedProducts error:', { error: error.message, productId, limit });
      return {
        success: false,
        data: [],
        message: error.message
      };
    }
  }

  /**
   * 获取产品分类列表
   * @returns {Object} 分类列表
   */
  async getCategories() {
    try {
      const categoryStats = {};
      this.mockProducts.forEach(product => {
        if (product.category && product.status === 'active') {
          if (!categoryStats[product.category]) {
            categoryStats[product.category] = {
              name: product.category,
              count: 0
            };
          }
          categoryStats[product.category].count++;
        }
      });

      const categories = Object.values(categoryStats).sort((a, b) => b.count - a.count);

      return {
        success: true,
        data: categories,
        message: '产品分类获取成功'
      };
    } catch (error) {
      logger.error('ProductService.getCategories error:', { error: error.message });
      return {
        success: false,
        data: [],
        message: error.message
      };
    }
  }

  /**
   * 获取产品统计信息
   * @returns {Object} 统计信息
   */
  async getProductStats() {
    try {
      const stats = {
        total: this.mockProducts.length,
        active: this.mockProducts.filter(p => p.status === 'active').length,
        inactive: this.mockProducts.filter(p => p.status !== 'active').length,
        featured: this.mockProducts.filter(p => p.isFeatured).length,
        hot: this.mockProducts.filter(p => p.isHot).length,
        new: this.mockProducts.filter(p => p.isNew).length,
        inStock: this.mockProducts.filter(p => p.stockQuantity > 0).length,
        outOfStock: this.mockProducts.filter(p => p.stockQuantity === 0).length,
        categoriesCount: new Set(this.mockProducts.map(p => p.category)).size,
        totalViews: this.mockProducts.reduce((sum, p) => sum + (p.viewCount || 0), 0)
      };

      return {
        success: true,
        data: stats,
        message: '产品统计信息获取成功'
      };
    } catch (error) {
      logger.error('ProductService.getProductStats error:', { error: error.message });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  // ==================== 管理相关方法 ====================

  /**
   * 创建产品
   * @param {Object} productData - 产品数据
   * @param {string} createdBy - 创建者ID
   * @returns {Object} 创建结果
   */
  async createProduct(productData, createdBy = null) {
    try {
      // 临时实现：模拟创建成功
      const newProduct = {
        id: (this.mockProducts.length + 1).toString(),
        ...productData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: createdBy || 'admin'
      };

      this.mockProducts.push(newProduct);

      return {
        success: true,
        data: newProduct,
        message: '产品创建成功'
      };
    } catch (error) {
      logger.error('ProductService.createProduct error:', { error: error.message, productData, createdBy });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  /**
   * 更新产品
   * @param {string} productId - 产品ID
   * @param {Object} updateData - 更新数据
   * @param {string} updatedBy - 更新者ID
   * @returns {Object} 更新结果
   */
  async updateProduct(productId, updateData, updatedBy = null) {
    try {
      const productIndex = this.mockProducts.findIndex(p => p.id === productId);
      if (productIndex === -1) {
        return {
          success: false,
          data: null,
          message: '产品不存在'
        };
      }

      this.mockProducts[productIndex] = {
        ...this.mockProducts[productIndex],
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      return {
        success: true,
        data: this.mockProducts[productIndex],
        message: '产品更新成功'
      };
    } catch (error) {
      logger.error('ProductService.updateProduct error:', { error: error.message, productId, updateData, updatedBy });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  /**
   * 删除产品
   * @param {string} productId - 产品ID
   * @param {string} deletedBy - 删除者ID
   * @returns {Object} 删除结果
   */
  async deleteProduct(productId, deletedBy = null) {
    try {
      const productIndex = this.mockProducts.findIndex(p => p.id === productId);
      if (productIndex === -1) {
        return {
          success: false,
          data: null,
          message: '产品不存在'
        };
      }

      this.mockProducts.splice(productIndex, 1);

      return {
        success: true,
        data: { id: productId },
        message: '产品删除成功'
      };
    } catch (error) {
      logger.error('ProductService.deleteProduct error:', { error: error.message, productId, deletedBy });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  /**
   * 更新产品库存
   * @param {string} productId - 产品ID
   * @param {number} newStock - 新库存数量
   * @param {string} updatedBy - 更新者ID
   * @returns {Object} 更新结果
   */
  async updateStock(productId, newStock, updatedBy = null) {
    try {
      const product = this.mockProducts.find(p => p.id === productId);
      if (!product) {
        return {
          success: false,
          data: null,
          message: '产品不存在'
        };
      }

      product.stockQuantity = newStock;
      product.updatedAt = new Date().toISOString();

      return {
        success: true,
        data: product,
        message: '库存更新成功'
      };
    } catch (error) {
      logger.error('ProductService.updateStock error:', { error: error.message, productId, newStock, updatedBy });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  /**
   * 批量更新产品状态
   * @param {Array} productIds - 产品ID数组
   * @param {string} status - 新状态
   * @param {string} updatedBy - 更新者ID
   * @returns {Object} 批量更新结果
   */
  async batchUpdateStatus(productIds, status, updatedBy = null) {
    try {
      let updatedCount = 0;
      productIds.forEach(id => {
        const product = this.mockProducts.find(p => p.id === id);
        if (product) {
          product.status = status;
          product.updatedAt = new Date().toISOString();
          updatedCount++;
        }
      });

      return {
        success: true,
        data: { updatedCount },
        message: `成功更新${updatedCount}个产品状态`
      };
    } catch (error) {
      logger.error('ProductService.batchUpdateStatus error:', { error: error.message, productIds, status, updatedBy });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  /**
   * 批量导入产品
   * @param {Array} productsData - 产品数据数组
   * @param {string} importedBy - 导入者ID
   * @returns {Object} 导入结果
   */
  async batchImportProducts(productsData, importedBy = null) {
    try {
      const importedProducts = [];
      productsData.forEach((productData, index) => {
        const newProduct = {
          id: (this.mockProducts.length + index + 1).toString(),
          ...productData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: importedBy || 'admin'
        };
        this.mockProducts.push(newProduct);
        importedProducts.push(newProduct);
      });

      return {
        success: true,
        data: { importedCount: importedProducts.length, products: importedProducts },
        message: `成功导入${importedProducts.length}个产品`
      };
    } catch (error) {
      logger.error('ProductService.batchImportProducts error:', { error: error.message, count: productsData.length, importedBy });
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  // ==================== 兼容性方法（保持向后兼容） ====================

  /**
   * 增加销量（兼容性方法）
   * @param {string} productId - 产品ID
   * @param {number} quantity - 销量增加数量
   */
  async incrementSales(productId, quantity = 1) {
    try {
      // 这个方法可以通过更新产品来实现
      const product = await this.queryService.getProductById(productId);
      if (product.success && product.data) {
        const newSalesCount = (product.data.salesCount || 0) + quantity;
        await this.managementService.updateProduct(productId, { salesCount: newSalesCount });
      }
      logger.debug('Product sales incremented', { productId, quantity });
    } catch (error) {
      logger.error('ProductService.incrementSales error:', { error: error.message, productId, quantity });
      throw error;
    }
  }

  // ==================== 内部工具方法 ====================

  /**
   * 生成产品slug
   * @param {string} name - 产品名称
   * @returns {string} slug
   */
  generateSlug(name) {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  /**
   * 生成模拟产品数据
   * @returns {Array} 产品数组
   */
  generateMockProducts() {
    const categories = ['钢材', '有色金属', '化工原料', '建材', '机械设备'];
    const products = [];

    for (let i = 1; i <= 50; i++) {
      const category = categories[Math.floor(Math.random() * categories.length)];
      products.push({
        id: i.toString(),
        name: `${category}产品 ${i}`,
        description: `这是${category}产品${i}的详细描述，具有优良的性能和广泛的应用。`,
        category: category,
        subcategory: `${category}子类${Math.floor(Math.random() * 3) + 1}`,
        price: Math.floor(Math.random() * 10000) + 1000,
        unit: '吨',
        minOrderQuantity: Math.floor(Math.random() * 10) + 1,
        stockQuantity: Math.floor(Math.random() * 1000) + 10,
        images: [`/images/product-${i}.jpg`],
        technicalData: {
          material: '优质钢材',
          grade: 'A级',
          standard: 'GB/T标准'
        },
        applications: ['建筑', '制造业', '基础设施'],
        features: ['高强度', '耐腐蚀', '环保'],
        certifications: ['ISO9001', 'CE认证'],
        status: 'active',
        isHot: Math.random() > 0.7,
        isNew: Math.random() > 0.8,
        isFeatured: Math.random() > 0.6,
        seoTitle: `${category}产品${i} - 龙驰商城`,
        seoDescription: `优质${category}产品，价格实惠，质量保证`,
        seoKeywords: `${category},产品${i},龙驰`,
        slug: this.generateSlug(`${category}产品${i}`),
        viewCount: Math.floor(Math.random() * 1000),
        salesCount: Math.floor(Math.random() * 100),
        rating: Math.floor(Math.random() * 5) + 1,
        reviewCount: Math.floor(Math.random() * 50),
        createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin'
      });
    }

    return products;
  }
}

// 导出单例实例，保持向后兼容
module.exports = new ProductService();

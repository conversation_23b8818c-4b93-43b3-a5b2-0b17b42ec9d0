#!/bin/bash
# 网络连接和域名访问诊断脚本

echo "=========================================="
echo "     龙驰新材料商城 - 网络诊断         "
echo "=========================================="

echo "1. 检查nginx状态..."
if systemctl is-active --quiet nginx; then
    echo "  ✅ nginx服务运行中"
    nginx_status="running"
else
    echo "  ❌ nginx服务未运行"
    nginx_status="stopped"
fi

if [ "$nginx_status" = "running" ]; then
    echo "  nginx进程:"
    ps aux | grep nginx | grep -v grep
    
    echo "  nginx端口监听:"
    netstat -tlnp | grep nginx | grep ":80"
fi

echo ""
echo "2. 检查域名解析..."
echo "  本地hosts文件中的gdlongchi.cn:"
grep gdlongchi.cn /etc/hosts 2>/dev/null || echo "  未在hosts文件中找到"

echo "  域名DNS解析:"
nslookup gdlongchi.cn 2>/dev/null || echo "  DNS解析失败"

echo ""
echo "3. 测试各种访问方式..."

# 测试localhost访问
echo "  localhost:3000 (前端直接访问):"
if curl -I -s http://localhost:3000 | head -1; then
    echo "    ✅ localhost:3000 可访问"
else
    echo "    ❌ localhost:3000 不可访问"
fi

echo "  localhost:5000 (后端直接访问):"
if curl -I -s http://localhost:5000 | head -1; then
    echo "    ✅ localhost:5000 可访问"
else
    echo "    ❌ localhost:5000 不可访问"
fi

# 测试域名访问
echo "  gdlongchi.cn:3000 (域名+端口):"
if curl -I -s http://gdlongchi.cn:3000 | head -1; then
    echo "    ✅ gdlongchi.cn:3000 可访问"
else
    echo "    ❌ gdlongchi.cn:3000 不可访问"
fi

echo "  gdlongchi.cn:5000 (域名+端口):"
if curl -I -s http://gdlongchi.cn:5000 | head -1; then
    echo "    ✅ gdlongchi.cn:5000 可访问"
else
    echo "    ❌ gdlongchi.cn:5000 不可访问"
fi

# 测试nginx反向代理
echo "  gdlongchi.cn (nginx反向代理):"
if curl -I -s http://gdlongchi.cn | head -1; then
    echo "    ✅ gdlongchi.cn (80端口) 可访问"
    echo "    响应头信息:"
    curl -I -s http://gdlongchi.cn | head -5
else
    echo "    ❌ gdlongchi.cn (80端口) 不可访问"
fi

echo ""
echo "4. 检查防火墙设置..."
echo "  iptables规则 (INPUT链):"
iptables -L INPUT -n --line-numbers | grep -E "(3000|5000|80)" || echo "  未找到相关端口规则"

echo "  ufw状态:"
ufw status 2>/dev/null || echo "  ufw未安装或未启用"

echo ""
echo "5. 检查nginx配置..."
if [ -f "/etc/nginx/sites-enabled/gdlongchi.cn" ]; then
    echo "  ✅ nginx站点配置存在"
    echo "  配置文件内容摘要:"
    grep -E "(server_name|proxy_pass|listen)" /etc/nginx/sites-enabled/gdlongchi.cn | head -10
else
    echo "  ❌ nginx站点配置不存在"
fi

echo "  nginx配置测试:"
nginx -t 2>&1 | head -5

echo ""
echo "6. 检查服务器网络接口..."
echo "  活跃的网络接口:"
ip addr show | grep -E "(inet |UP)" | head -10

echo ""
echo "7. 测试API接口..."
echo "  通过localhost测试API:"
if curl -s "http://localhost:5000/api/products?page=1&limit=1" | grep -q "success"; then
    echo "    ✅ localhost API正常"
else
    echo "    ❌ localhost API异常"
fi

echo "  通过域名测试API:"
if curl -s "http://gdlongchi.cn:5000/api/products?page=1&limit=1" | grep -q "success"; then
    echo "    ✅ 域名 API正常"
else
    echo "    ❌ 域名 API异常"
fi

echo ""
echo "8. 检查进程详情..."
echo "  前端进程详情:"
ps aux | grep "next" | grep -v grep

echo "  后端进程详情:"
ps aux | grep "node.*server.js" | grep -v grep

echo ""
echo "=========================================="
echo "网络诊断完成！"
echo "=========================================="
echo ""
echo "📋 问题排查建议："
echo "1. 如果nginx未运行，执行: sudo systemctl start nginx"
echo "2. 如果域名无法访问，检查DNS解析和防火墙设置"
echo "3. 如果nginx配置有误，修复后执行: sudo nginx -t && sudo systemctl reload nginx"
echo "4. 如果前端警告影响功能，考虑修改next.config.js配置"
echo "=========================================="

# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

- Nothing yet!

## [0.4.4] - 2023-03-30

### Changed

- Remove Tailwind CSS v3.3 warning ([#28](https://github.com/tailwindlabs/tailwindcss-line-clamp/pull/28))

## [0.4.3] - 2023-03-30

### Changed

- Warn when used in Tailwind CSS v3.3+ ([#26](https://github.com/tailwindlabs/tailwindcss-line-clamp/pull/26))

## [0.4.2] - 2022-09-02

### Fixed

- Update TypeScript types ([#23](https://github.com/tailwindlabs/tailwindcss-line-clamp/pull/23))

## [0.4.1] - 2022-09-01

### Added

- Add typescript types ([#22](https://github.com/tailwindlabs/tailwindcss-line-clamp/pull/22))

## [0.4.0] - 2022-04-27

This release of `@tailwindcss/line-clamp` is designed for Tailwind CSS v3.0+, and is not compatible with earlier versions.

### Added

- Add support for arbitrary values ([#10](https://github.com/tailwindlabs/tailwindcss-line-clamp/pull/10))

## [0.3.1] - 2021-12-09

### Fixed

- Add LICENSE file ([#13](https://github.com/tailwindlabs/tailwindcss-line-clamp/pull/13))

### Changed

- Remove `dist` folder and related dependencies ([#11](https://github.com/tailwindlabs/tailwindcss-line-clamp/pull/11))

## [0.3.0] - 2021-12-09

### Added

- Make sure that Tailwind CSS v3 is in peerDependencies ([c9a9115](https://github.com/tailwindlabs/tailwindcss-line-clamp/commit/c9a9115))

## [0.2.2] - 2021-10-01

## Changed

- Update peer dependencies to support v3 alpha

## [0.2.1] - 2021-05-24

### Fixed

- Mark `tailwindcss` as a peer-dependency for monorepo support ([#6](https://github.com/tailwindlabs/tailwindcss-line-clamp/pull/6))

## [0.2.0] - 2021-01-22

### Added

- Allow using numbers instead of strings in your config ([#3](https://github.com/tailwindlabs/tailwindcss-line-clamp/pull/3))

## [0.1.0] - 2021-01-06

Initial release!

[unreleased]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.4.4...HEAD
[0.4.4]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.4.3...v0.4.4
[0.4.3]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.4.2...v0.4.3
[0.4.2]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.4.1...v0.4.2
[0.4.1]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.4.0...v0.4.1
[0.4.0]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.3.1...v0.4.0
[0.3.1]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.3.0...v0.3.1
[0.3.0]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.2.1...v0.3.0
[0.2.1]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/tailwindlabs/tailwindcss-line-clamp/compare/v0.1.0...v0.2.0
[0.1.0]: https://github.com/tailwindlabs/tailwindcss-line-clamp/releases/tag/v0.1.0

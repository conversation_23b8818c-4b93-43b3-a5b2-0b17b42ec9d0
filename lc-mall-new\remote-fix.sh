#!/bin/bash
# 远程服务器快速修复脚本
# 适用于常见的API 404等问题

echo "=========================================="
echo "    龙驰商城 - 远程快速修复工具"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 设置生产环境
export NODE_ENV=production

# 检查是否在项目根目录
if [ ! -f "package.json" ] || [ ! -d "frontend" ] || [ ! -d "backend" ]; then
    echo -e "${RED}错误: 请在项目根目录执行此脚本${NC}"
    exit 1
fi

# 1. 强制停止所有相关进程
echo -e "${BLUE}[步骤1] 停止现有服务...${NC}"
pkill -f "node.*server.js" 2>/dev/null || true
pkill -f "next start" 2>/dev/null || true
pkill -f "npm start" 2>/dev/null || true

# 等待进程完全停止
sleep 3

# 强制清理端口
echo "清理端口3000和5000..."
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
lsof -ti:5000 | xargs kill -9 2>/dev/null || true

# 2. 检查环境配置
echo -e "${BLUE}[步骤2] 检查环境配置...${NC}"
if [ ! -f ".env.production" ]; then
    echo -e "${RED}错误: .env.production文件不存在${NC}"
    echo "请确保环境变量文件存在并配置正确"
    exit 1
fi

# 检查关键环境变量
source .env.production
if [ -z "$NEXT_PUBLIC_API_URL" ]; then
    echo -e "${YELLOW}警告: NEXT_PUBLIC_API_URL未设置${NC}"
fi

# 3. 创建必要目录
echo -e "${BLUE}[步骤3] 创建运行目录...${NC}"
mkdir -p logs uploads ssl
chmod 755 logs uploads ssl

# 4. 修复后端服务
echo -e "${BLUE}[步骤4] 修复后端服务...${NC}"
cd backend

# 检查后端依赖
if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
    echo "重新安装后端依赖..."
    rm -rf node_modules package-lock.json
    npm install
fi

# 测试后端启动
echo "启动后端服务..."
nohup npm start > ../logs/backend.log 2>&1 &
backend_pid=$!
echo $backend_pid > ../logs/backend.pid
echo -e "${GREEN}后端PID: $backend_pid${NC}"

# 等待后端启动
echo "等待后端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:5000/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 后端服务启动成功${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}✗ 后端服务启动失败${NC}"
        echo "后端日志:"
        tail -20 ../logs/backend.log
        exit 1
    fi
    sleep 2
done

cd ..

# 5. 修复前端服务
echo -e "${BLUE}[步骤5] 修复前端服务...${NC}"
cd frontend

# 检查前端依赖
if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
    echo "重新安装前端依赖..."
    rm -rf node_modules .next package-lock.json
    npm install
fi

# 重新构建前端
echo "重新构建前端..."
rm -rf .next
NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL npm run build

if [ $? -ne 0 ]; then
    echo -e "${RED}前端构建失败${NC}"
    exit 1
fi

# 启动前端服务
echo "启动前端服务..."
nohup npm start > ../logs/frontend.log 2>&1 &
frontend_pid=$!
echo $frontend_pid > ../logs/frontend.pid
echo -e "${GREEN}前端PID: $frontend_pid${NC}"

# 等待前端启动
echo "等待前端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 前端服务启动成功${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}✗ 前端服务启动失败${NC}"
        echo "前端日志:"
        tail -20 ../logs/frontend.log
        exit 1
    fi
    sleep 2
done

cd ..

# 6. 测试API接口
echo -e "${BLUE}[步骤6] 测试API接口...${NC}"

# 测试后端API
echo "测试后端API..."
for api in "/health" "/api/products" "/api/news"; do
    url="http://localhost:5000$api"
    if curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo -e "${GREEN}✓ $api 响应正常${NC}"
    else
        echo -e "${RED}✗ $api 响应异常${NC}"
    fi
done

# 测试前端API
echo "测试前端API..."
for api in "/" "/api/videos" "/api/admin/stats"; do
    url="http://localhost:3000$api"
    if curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo -e "${GREEN}✓ $api 响应正常${NC}"
    else
        echo -e "${RED}✗ $api 响应异常${NC}"
    fi
done

# 7. 重启Nginx (如果需要)
echo -e "${BLUE}[步骤7] 检查Nginx状态...${NC}"
if command -v nginx > /dev/null; then
    if systemctl is-active nginx > /dev/null 2>&1; then
        echo "重新加载Nginx配置..."
        sudo nginx -s reload 2>/dev/null || echo "需要手动重启Nginx"
    else
        echo "启动Nginx服务..."
        sudo systemctl start nginx 2>/dev/null || echo "需要手动启动Nginx"
    fi
else
    echo -e "${YELLOW}Nginx未安装，跳过${NC}"
fi

# 8. 输出服务状态
echo -e "${BLUE}[完成] 服务修复完成！${NC}"
echo ""
echo "=== 服务状态 ==="
echo "后端PID: $(cat logs/backend.pid 2>/dev/null || echo '未知')"
echo "前端PID: $(cat logs/frontend.pid 2>/dev/null || echo '未知')"
echo ""
echo "=== 访问地址 ==="
echo "前端: http://gdlongchi.cn (通过Nginx)"
echo "前端直接: http://gdlongchi.cn:3000"
echo "后端API: http://gdlongchi.cn:5000"
echo ""
echo "=== 日志监控 ==="
echo "后端日志: tail -f logs/backend.log"
echo "前端日志: tail -f logs/frontend.log"
echo ""
echo "=== 快速检查 ==="
echo "curl http://localhost:5000/health"
echo "curl http://localhost:3000"
echo "curl http://gdlongchi.cn"

echo ""
echo -e "${GREEN}修复完成！如果仍有问题，请查看日志文件。${NC}"

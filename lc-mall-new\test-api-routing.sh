#!/bin/bash
# API路由测试脚本

echo "=========================================="
echo "     API路由问题专项测试                "
echo "=========================================="

echo "🔍 测试场景："
echo "• 直接访问: gdlongchi.cn:3000 ✅ 视频正常"
echo "• nginx代理: gdlongchi.cn ❌ API endpoint not found"
echo ""

echo "1. 对比测试两种访问方式..."
echo ""

echo "📱 直接访问前端 (gdlongchi.cn:3000):"
echo -n "• /api/videos: "
direct_videos=$(curl -s "http://gdlongchi.cn:3000/api/videos" 2>/dev/null)
if echo "$direct_videos" | grep -q "success"; then
    echo "✅ 成功"
    echo "  数据: $(echo "$direct_videos" | grep -o '"data":\[[^]]*\]' | head -1)"
else
    echo "❌ 失败"
    echo "  响应: $(echo "$direct_videos" | head -1)"
fi

echo ""
echo "🌐 nginx代理访问 (gdlongchi.cn):"
echo -n "• /api/videos: "
proxy_videos=$(curl -s "http://gdlongchi.cn/api/videos" 2>/dev/null)
if echo "$proxy_videos" | grep -q "success"; then
    echo "✅ 成功"
    echo "  数据: $(echo "$proxy_videos" | grep -o '"data":\[[^]]*\]' | head -1)"
else
    echo "❌ 失败"
    echo "  响应: $(echo "$proxy_videos" | head -1)"
fi

echo ""
echo "2. 检查nginx当前配置..."
echo "当前API路由配置:"
grep -A 5 -B 1 "location.*api" /etc/nginx/sites-available/gdlongchi.cn 2>/dev/null || echo "无法读取配置文件"

echo ""
echo "3. 查看nginx错误日志 (最近10行)..."
sudo tail -10 /var/log/nginx/error.log 2>/dev/null | grep -E "(gdlongchi|api|error)" || echo "无相关错误日志"

echo ""
echo "4. 分析问题..."
if echo "$direct_videos" | grep -q "success" && ! echo "$proxy_videos" | grep -q "success"; then
    echo "🎯 确认问题：nginx反向代理配置导致API路由失败"
    echo ""
    echo "问题分析:"
    echo "• 前端服务本身正常 (3000端口直接访问成功)"
    echo "• nginx代理配置有误，/api/videos请求被错误路由"
    echo ""
    echo "解决方案:"
    echo "1. 修复nginx配置，确保前端API路由正确"
    echo "2. 运行修复脚本: ./fix-nginx-api-routing.sh"
    echo ""
elif echo "$proxy_videos" | grep -q "success"; then
    echo "✅ 问题已解决！API路由正常工作"
else
    echo "❓ 需要进一步诊断，两种访问方式都有问题"
fi

echo ""
echo "5. 详细的API端点测试..."

# 测试各个前端API端点
echo ""
echo "前端API端点测试 (应该路由到3000端口):"

endpoints=("/api/videos" "/api/admin/stats" "/api/admin/test")
for endpoint in "${endpoints[@]}"; do
    echo -n "• $endpoint: "
    
    # 直接访问
    direct_result=$(curl -s "http://gdlongchi.cn:3000$endpoint" 2>/dev/null | head -c 100)
    # nginx代理访问  
    proxy_result=$(curl -s "http://gdlongchi.cn$endpoint" 2>/dev/null | head -c 100)
    
    if [[ "$direct_result" == "$proxy_result" ]] && [[ -n "$direct_result" ]]; then
        echo "✅ 路由正确"
    else
        echo "❌ 路由错误"
        echo "    直接访问: $(echo "$direct_result" | head -c 50)..."
        echo "    代理访问: $(echo "$proxy_result" | head -c 50)..."
    fi
done

echo ""
echo "后端API端点测试 (应该路由到5000端口):"

backend_endpoints=("/api/products" "/api/health" "/api/news")
for endpoint in "${backend_endpoints[@]}"; do
    echo -n "• $endpoint: "
    
    # 直接访问后端
    direct_result=$(curl -s "http://gdlongchi.cn:5000$endpoint" 2>/dev/null | head -c 100)
    # nginx代理访问
    proxy_result=$(curl -s "http://gdlongchi.cn$endpoint" 2>/dev/null | head -c 100)
    
    if [[ "$direct_result" == "$proxy_result" ]] && [[ -n "$direct_result" ]]; then
        echo "✅ 路由正确"
    else
        echo "❌ 路由错误"
        echo "    直接访问: $(echo "$direct_result" | head -c 50)..."
        echo "    代理访问: $(echo "$proxy_result" | head -c 50)..."
    fi
done

echo ""
echo "=========================================="
echo "🔧 修复建议"
echo "=========================================="
echo ""
echo "基于测试结果，建议执行以下步骤:"
echo ""
echo "1. 立即修复nginx配置:"
echo "   chmod +x fix-nginx-api-routing.sh"
echo "   ./fix-nginx-api-routing.sh"
echo ""
echo "2. 修复后重新测试:"
echo "   ./test-api-routing.sh"
echo ""
echo "3. 浏览器验证:"
echo "   访问 http://gdlongchi.cn"
echo "   打开开发者工具 → Network标签"
echo "   刷新页面，检查 /api/videos 请求状态"
echo ""
echo "4. 如仍有问题，查看详细日志:"
echo "   sudo tail -f /var/log/nginx/gdlongchi.cn.error.log"
echo "=========================================="

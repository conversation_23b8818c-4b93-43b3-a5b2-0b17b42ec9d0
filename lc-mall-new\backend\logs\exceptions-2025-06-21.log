{"date":"Sat Jun 21 2025 11:08:33 GMT+0800 (中国标准时间)","environment":"production","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../services/productService'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\nError: Cannot find module '../services/productService'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:1:24)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","os":{"loadavg":[0,0,0],"uptime":345833.109},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":63393792,"heapUsed":33351912,"rss":108883968},"pid":37524,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Cannot find module '../services/productService'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:1:24)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","timestamp":"2025-06-21 11:08:33","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1055,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":24,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false}]}
{"date":"Sat Jun 21 2025 11:09:46 GMT+0800 (中国标准时间)","environment":"production","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\newsService.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\aiNewsService.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\schedulerService.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\newsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\aiNewsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\schedulerService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\nError: Cannot find module '../config/database'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\newsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\aiNewsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\schedulerService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js:5:25)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","os":{"loadavg":[0,0,0],"uptime":345905.609},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":63655936,"heapUsed":33408040,"rss":108933120},"pid":26112,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Cannot find module '../config/database'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\newsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\aiNewsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\schedulerService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js:5:25)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","timestamp":"2025-06-21 11:09:46","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1055,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":25,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false}]}
{"date":"Sat Jun 21 2025 11:14:10 GMT+0800 (中国标准时间)","environment":"production","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/productController'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\nError: Cannot find module '../controllers/productController'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:3:27)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","os":{"loadavg":[0,0,0],"uptime":346170.046},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":63131648,"heapUsed":33462176,"rss":108699648},"pid":48008,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Cannot find module '../controllers/productController'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:3:27)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","timestamp":"2025-06-21 11:14:10","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1055,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":27,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false}]}
{"date":"Sat Jun 21 2025 11:14:18 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346177.859},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":44487656,"rss":109776896},"pid":44564,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:14:18","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:14:54 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346214.046},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":63393792,"heapUsed":44667624,"rss":109158400},"pid":42096,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:14:54","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:15:48 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346268.109},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":43868320,"rss":110424064},"pid":46624,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:15:48","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:16:18 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346298.421},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64180224,"heapUsed":44512648,"rss":109936640},"pid":47436,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:16:18","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:16:34 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346314.187},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":43797520,"rss":110129152},"pid":42048,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:16:34","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:24:25 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346784.828},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":43644072,"rss":110059520},"pid":47492,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:24:25","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:27:41 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346980.765},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":43854088,"rss":110030848},"pid":44168,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:27:41","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}

{"date":"Sat Jun 21 2025 11:08:33 GMT+0800 (中国标准时间)","environment":"production","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../services/productService'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\nError: Cannot find module '../services/productService'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:1:24)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","os":{"loadavg":[0,0,0],"uptime":345833.109},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":63393792,"heapUsed":33351912,"rss":108883968},"pid":37524,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Cannot find module '../services/productService'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js:1:24)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","timestamp":"2025-06-21 11:08:33","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1055,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":24,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\controllers\\productController.js","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false}]}
{"date":"Sat Jun 21 2025 11:09:46 GMT+0800 (中国标准时间)","environment":"production","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\newsService.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\aiNewsService.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\schedulerService.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\newsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\aiNewsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\schedulerService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\nError: Cannot find module '../config/database'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\newsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\aiNewsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\schedulerService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js:5:25)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","os":{"loadavg":[0,0,0],"uptime":345905.609},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":63655936,"heapUsed":33408040,"rss":108933120},"pid":26112,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Cannot find module '../config/database'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\newsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\aiNewsService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\schedulerService.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js:5:25)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","timestamp":"2025-06-21 11:09:46","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1055,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":25,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\models\\News.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false}]}
{"date":"Sat Jun 21 2025 11:14:10 GMT+0800 (中国标准时间)","environment":"production","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/productController'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\nError: Cannot find module '../controllers/productController'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:3:27)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","os":{"loadavg":[0,0,0],"uptime":346170.046},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":63131648,"heapUsed":33462176,"rss":108699648},"pid":48008,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Cannot find module '../controllers/productController'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\app.js\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:3:27)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","timestamp":"2025-06-21 11:14:10","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1055,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":27,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false}]}
{"date":"Sat Jun 21 2025 11:14:18 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346177.859},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":44487656,"rss":109776896},"pid":44564,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:14:18","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:14:54 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346214.046},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":63393792,"heapUsed":44667624,"rss":109158400},"pid":42096,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:14:54","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:15:48 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346268.109},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":43868320,"rss":110424064},"pid":46624,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:15:48","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:16:18 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346298.421},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64180224,"heapUsed":44512648,"rss":109936640},"pid":47436,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:16:18","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:16:34 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346314.187},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":43797520,"rss":110129152},"pid":42048,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:16:34","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:24:25 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346784.828},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":43644072,"rss":110059520},"pid":47492,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:24:25","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:27:41 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":346980.765},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64442368,"heapUsed":43854088,"rss":110030848},"pid":44168,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:27:41","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:36:16 GMT+0800 (中国标准时间)","environment":"production","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":347496.5},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":64180224,"heapUsed":43659752,"rss":110108672},"pid":46400,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:36:16","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:37:40 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":347580.406},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":65228800,"heapUsed":45508704,"rss":110972928},"pid":21072,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:37:40","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:38:11 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":347610.875},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65933,"external":3373781,"heapTotal":65228800,"heapUsed":45100848,"rss":111108096},"pid":47740,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:38:11","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:38:33 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":347633.39},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":65228800,"heapUsed":46478752,"rss":110411776},"pid":36252,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:38:33","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:38:47 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":347646.609},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66109,"external":3399264,"heapTotal":65490944,"heapUsed":45429800,"rss":111030272},"pid":44344,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:38:47","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:39:00 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","os":{"loadavg":[0,0,0],"uptime":347660.234},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65933,"external":3373781,"heapTotal":64966656,"heapUsed":45518960,"rss":110538752},"pid":42888,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as get] (D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js:30:8)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)","timestamp":"2025-06-21 11:39:00","trace":[{"column":15,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\routes\\products.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"date":"Sat Jun 21 2025 11:41:23 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":347802.89},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68792320,"heapUsed":42595552,"rss":119590912},"pid":36536,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 11:41:23","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 11:42:13 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":347852.781},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68792320,"heapUsed":42651200,"rss":119791616},"pid":47836,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 11:42:13","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 11:46:11 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":348091.531},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":69054464,"heapUsed":42715464,"rss":120193024},"pid":47584,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 11:46:11","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 11:59:10 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: this.generateMockProducts is not a function\nTypeError: this.generateMockProducts is not a function\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:11:30)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:296:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":348870},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":63918080,"heapUsed":35523736,"rss":109420544},"pid":48368,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: this.generateMockProducts is not a function\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:11:30)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:296:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-21 11:59:10","trace":[{"column":30,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js","function":"new ProductService","line":11,"method":null,"native":false},{"column":18,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js","function":null,"line":296,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Sat Jun 21 2025 11:59:10 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: this.generateMockProducts is not a function\nTypeError: this.generateMockProducts is not a function\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:11:30)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:296:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":348870},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":64442368,"heapUsed":34810008,"rss":110288896},"pid":34264,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: this.generateMockProducts is not a function\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:11:30)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:296:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-21 11:59:10","trace":[{"column":30,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js","function":"new ProductService","line":11,"method":null,"native":false},{"column":18,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js","function":null,"line":296,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Sat Jun 21 2025 11:59:30 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: this.generateMockProducts is not a function\nTypeError: this.generateMockProducts is not a function\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:11:30)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:346:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":348890.25},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":64180224,"heapUsed":35178896,"rss":109875200},"pid":45140,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: this.generateMockProducts is not a function\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:11:30)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:346:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-21 11:59:30","trace":[{"column":30,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js","function":"new ProductService","line":11,"method":null,"native":false},{"column":18,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js","function":null,"line":346,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Sat Jun 21 2025 11:59:30 GMT+0800 (中国标准时间)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: this.generateMockProducts is not a function\nTypeError: this.generateMockProducts is not a function\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:11:30)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:346:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","os":{"loadavg":[0,0,0],"uptime":348890.265},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66108,"external":3399263,"heapTotal":63918080,"heapUsed":35421128,"rss":109531136},"pid":36068,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"TypeError: this.generateMockProducts is not a function\n    at new ProductService (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:11:30)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js:346:18)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)","timestamp":"2025-06-21 11:59:30","trace":[{"column":30,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js","function":"new ProductService","line":11,"method":null,"native":false},{"column":18,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\services\\productService.js","function":null,"line":346,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1108,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false}]}
{"date":"Sat Jun 21 2025 11:59:58 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":348918.437},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68792320,"heapUsed":42297688,"rss":120025088},"pid":44352,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 11:59:58","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 12:00:18 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":348938},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68792320,"heapUsed":42495728,"rss":120033280},"pid":48960,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 12:00:18","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 12:01:13 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":348993.296},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68530176,"heapUsed":42658560,"rss":119709696},"pid":13232,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 12:01:13","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 12:02:01 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":349040.671},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68792320,"heapUsed":42380480,"rss":119943168},"pid":47676,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 12:02:01","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 12:02:58 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":349098.265},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68792320,"heapUsed":42780208,"rss":119853056},"pid":15296,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 12:02:58","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 12:05:12 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":349231.968},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66190,"external":3562161,"heapTotal":68792320,"heapUsed":42420792,"rss":119992320},"pid":37192,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 12:05:12","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 12:06:33 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":349312.671},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66014,"external":3536678,"heapTotal":68530176,"heapUsed":42639152,"rss":119242752},"pid":43444,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 12:06:33","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 12:07:45 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5000\nError: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","os":{"loadavg":[0,0,0],"uptime":349385.031},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\src\\server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66014,"external":3536678,"heapTotal":68530176,"heapUsed":42674904,"rss":119656448},"pid":34716,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: listen EADDRINUSE: address already in use ::1:5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at GetAddrInfoReqWrap.callback (node:net:2203:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:132:8)","timestamp":"2025-06-21 12:07:45","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2203,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":132,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sat Jun 21 2025 12:08:47 GMT+0800 (中国标准时间)","environment":"development","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './src/routes/videos'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js\nError: Cannot find module './src/routes/videos'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js:48:21)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","os":{"loadavg":[0,0,0],"uptime":349446.75},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16875,"external":2136026,"heapTotal":69423104,"heapUsed":38517664,"rss":114974720},"pid":37544,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Cannot find module './src/routes/videos'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js:48:21)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","timestamp":"2025-06-21 12:08:47","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1055,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":21,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js","function":null,"line":48,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false}]}
{"date":"Sat Jun 21 2025 12:09:04 GMT+0800 (中国标准时间)","environment":"development","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './src/routes/videos'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js\nError: Cannot find module './src/routes/videos'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js:48:21)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","os":{"loadavg":[0,0,0],"uptime":349464.359},"process":{"argv":["D:\\Programs\\nodejs\\node.exe","D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js"],"cwd":"D:\\github\\LongChiMall\\lc-mall-new\\backend","execPath":"D:\\Programs\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16875,"external":2136026,"heapTotal":69160960,"heapUsed":38677680,"rss":114991104},"pid":32552,"uid":null,"version":"v22.14.0"},"service":"lc-mall-backend","stack":"Error: Cannot find module './src/routes/videos'\nRequire stack:\n- D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js:48:21)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","timestamp":"2025-06-21 12:09:04","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1055,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":21,"file":"D:\\github\\LongChiMall\\lc-mall-new\\backend\\simple-server.js","function":null,"line":48,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false}]}

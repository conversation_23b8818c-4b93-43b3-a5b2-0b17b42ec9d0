"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/videos/route";
exports.ids = ["app/api/videos/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvideos%2Froute&page=%2Fapi%2Fvideos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideos%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvideos%2Froute&page=%2Fapi%2Fvideos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideos%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_LongChiMall_lc_mall_new_frontend_src_app_api_videos_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/videos/route.ts */ \"(rsc)/./src/app/api/videos/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/videos/route\",\n        pathname: \"/api/videos\",\n        filename: \"route\",\n        bundlePath: \"app/api/videos/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\LongChiMall\\\\lc-mall-new\\\\frontend\\\\src\\\\app\\\\api\\\\videos\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_LongChiMall_lc_mall_new_frontend_src_app_api_videos_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/videos/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvideos%2Froute&page=%2Fapi%2Fvideos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideos%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/videos/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/videos/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(request) {\n    try {\n        const videoDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"public\", \"images\", \"video\");\n        // 检查目录是否存在\n        if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(videoDir)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Video directory not found\"\n            }, {\n                status: 404\n            });\n        }\n        // 读取目录中的所有文件\n        const files = fs__WEBPACK_IMPORTED_MODULE_1___default().readdirSync(videoDir);\n        // 过滤出MP4文件\n        const videoFiles = files.filter((file)=>file.toLowerCase().endsWith(\".mp4\")).sort() // 按文件名排序\n        ;\n        // 生成视频数据对象\n        const videos = videoFiles.map((file, index)=>{\n            const baseName = path__WEBPACK_IMPORTED_MODULE_2___default().parse(file).name;\n            // 根据文件名生成标题和描述\n            let title = \"\";\n            let description = \"\";\n            switch(baseName){\n                case \"0001\":\n                    title = \"龙驰新材料\";\n                    description = \"专业的化工材料解决方案提供商，致力于为客户提供高品质的产品和服务\";\n                    break;\n                case \"0002\":\n                    title = \"创新技术\";\n                    description = \"采用先进的生产工艺和严格的质量控制体系，确保产品质量和稳定性\";\n                    break;\n                case \"0003\":\n                    title = \"优质服务\";\n                    description = \"专业的技术团队和完善的售后服务体系，为客户提供全方位的技术支持\";\n                    break;\n                case \"0004\":\n                    title = \"可持续发展\";\n                    description = \"致力于环保和可持续发展，为构建绿色化工产业贡献力量\";\n                    break;\n                default:\n                    title = `企业视频 ${index + 1}`;\n                    description = \"了解我们的企业文化和发展历程\";\n            }\n            return {\n                id: `video_${baseName}`,\n                src: `/images/video/${file}`,\n                title,\n                description\n            };\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: videos,\n            message: `Found ${videos.length} video files`\n        });\n    } catch (error) {\n        console.error(\"Error reading video directory:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/videos/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvideos%2Froute&page=%2Fapi%2Fvideos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideos%2Froute.ts&appDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=D%3A%5Cgithub%5CLongChiMall%5Clc-mall-new%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
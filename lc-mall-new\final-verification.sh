#!/bin/bash
# 最终验证脚本 - 确认所有功能正常

echo "=========================================="
echo "   龙驰新材料商城 - 最终功能验证        "
echo "=========================================="

echo "🌐 网络诊断显示所有服务正常，现在进行详细功能验证..."
echo ""

echo "1. 验证主要访问路径..."
echo ""

echo "📱 前端页面访问测试："
echo "  • localhost:3000 主页:"
if curl -s http://localhost:3000 | grep -q "<title>"; then
    echo "    ✅ 成功访问，页面正常"
else
    echo "    ❌ 页面访问异常"
fi

echo "  • gdlongchi.cn 主页 (nginx代理):"
if curl -s http://gdlongchi.cn | grep -q "<title>"; then
    echo "    ✅ 成功访问，nginx代理正常"
else
    echo "    ❌ nginx代理异常"
fi

echo ""
echo "🔌 API接口测试："

echo "  • 后端健康检查:"
health_result=$(curl -s http://gdlongchi.cn/api/health 2>/dev/null)
if echo "$health_result" | grep -q "healthy\|OK"; then
    echo "    ✅ 健康检查通过"
    echo "    Redis状态: $(echo "$health_result" | grep -o '"redis":"[^"]*"' | cut -d'"' -f4)"
else
    echo "    ❌ 健康检查失败"
fi

echo "  • 产品API测试:"
products_result=$(curl -s "http://gdlongchi.cn/api/products?page=1&limit=1" 2>/dev/null)
if echo "$products_result" | grep -q "success"; then
    echo "    ✅ 产品API正常"
    echo "    数据: $(echo "$products_result" | grep -o '"total":[0-9]*' | head -1)"
else
    echo "    ❌ 产品API异常"
    echo "    响应: $(echo "$products_result" | head -1)"
fi

echo "  • 前端API路由测试:"
videos_result=$(curl -s "http://gdlongchi.cn/api/videos" 2>/dev/null)
if echo "$videos_result" | grep -q "success"; then
    echo "    ✅ 前端API正常 (videos)"
else
    echo "    ❌ 前端API异常"
fi

echo ""
echo "2. 检查前端警告状态..."
if [ -f "logs/frontend.log" ]; then
    if grep -q "does not work with.*standalone" logs/frontend.log; then
        echo "  ⚠️  仍有standalone警告，建议运行修复脚本"
        echo "     运行: ./fix-frontend-warning.sh"
    else
        echo "  ✅ 未发现standalone警告"
    fi
    
    echo "  最新日志 (最后5行):"
    tail -5 logs/frontend.log | sed 's/^/    /'
else
    echo "  ❌ 前端日志文件不存在"
fi

echo ""
echo "3. 性能和连接测试..."

echo "  • 响应时间测试:"
echo -n "    前端首页: "
response_time=$(curl -w "%{time_total}" -s -o /dev/null http://gdlongchi.cn)
echo "${response_time}s"

echo -n "    API响应: "
api_time=$(curl -w "%{time_total}" -s -o /dev/null http://gdlongchi.cn/api/health)
echo "${api_time}s"

echo "  • 并发连接测试:"
echo -n "    nginx连接数: "
netstat -an | grep :80 | grep ESTABLISHED | wc -l

echo ""
echo "4. 检查资源使用情况..."
echo "  • 服务器资源:"
echo -n "    内存使用: "
free -h | grep "Mem:" | awk '{printf "已用: %s / 总计: %s (%.1f%%)\n", $3, $2, ($3/$2)*100}'

echo "  • 进程资源:"
echo "    前端进程 CPU/内存:"
ps aux | grep "next-server" | grep -v grep | awk '{printf "    CPU: %s%%, 内存: %s%%\n", $3, $4}'

echo "    后端进程 CPU/内存:"
ps aux | grep "node.*server.js" | grep -v grep | awk '{printf "    CPU: %s%%, 内存: %s%%\n", $3, $4}'

echo ""
echo "=========================================="
echo "🎯 验证结果总结"
echo "=========================================="

# 计算总体状态
total_checks=0
passed_checks=0

# 检查各项服务
if curl -s http://gdlongchi.cn | grep -q "<title>"; then
    ((passed_checks++))
fi
((total_checks++))

if curl -s http://gdlongchi.cn/api/health | grep -q "healthy\|OK"; then
    ((passed_checks++))
fi
((total_checks++))

if curl -s "http://gdlongchi.cn/api/products?page=1&limit=1" | grep -q "success"; then
    ((passed_checks++))
fi
((total_checks++))

if curl -s "http://gdlongchi.cn/api/videos" | grep -q "success"; then
    ((passed_checks++))
fi
((total_checks++))

echo "✅ 通过检查: $passed_checks/$total_checks"

if [ $passed_checks -eq $total_checks ]; then
    echo "🎉 所有核心功能正常！系统运行状态优秀！"
    echo ""
    echo "🌟 你的龙驰新材料商城已经完全可用："
    echo "   • 用户访问: http://gdlongchi.cn"
    echo "   • 管理后台: http://gdlongchi.cn/admin"
    echo "   • API文档: http://gdlongchi.cn/api"
    echo ""
    
    if grep -q "does not work with.*standalone" logs/frontend.log 2>/dev/null; then
        echo "💡 建议修复前端警告 (不影响功能):"
        echo "   ./fix-frontend-warning.sh"
    fi
else
    echo "⚠️  部分功能需要检查，运行具体修复脚本"
fi

echo ""
echo "📊 监控命令:"
echo "• 查看访问日志: sudo tail -f /var/log/nginx/access.log"
echo "• 查看错误日志: sudo tail -f /var/log/nginx/error.log"
echo "• 查看应用日志: tail -f logs/backend.log logs/frontend.log"
echo "• 服务状态检查: ./check-services.sh"
echo "=========================================="

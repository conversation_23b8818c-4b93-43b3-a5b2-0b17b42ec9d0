/** @type {import('next').NextConfig} */
const nextConfig = {
  // 显式指定源代码目录
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],

  // 图片优化配置
  images: {
    domains: ['localhost'],
    unoptimized: true,
    minimumCacheTTL: 60,
  },
      // API 路由配置
  async rewrites() {
    // 统一使用 NEXT_PUBLIC_API_URL，自动拼接 path
    const apiBase = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, '') || 'http://localhost:5000/api';
    const serverBase = process.env.NEXT_PUBLIC_API_URL?.replace(/\/api$/, '') || 'http://localhost:5000';
    
    return [
      // 静态图片文件代理到后端
      {
        source: '/images/:path*',
        destination: `${serverBase}/images/:path*`,
      },
        // 新闻相关的API请求使用前端的API路由，不重定向
      // 注意：这里不需要重写规则，因为我们希望使用前端的 /api/news/* 路由
      // 管理员API路由也使用前端API路由，不重定向
      // 注意：这里不包含 /api/admin/* 重写规则，因为我们希望使用前端的 /api/admin/* 路由      
	  // 其他API请求重定向到后端（明确指定需要重定向的路径）
      // 注意：不包含 /api/products 重写，让前端API路由处理产品相关请求
      {
        source: '/api/orders/:path*',
        destination: `${apiBase}/orders/:path*`,
      },
      {
        source: '/api/users/:path*',
        destination: `${apiBase}/users/:path*`,
      },
      {
        source: '/api/auth/:path*',
        destination: `${apiBase}/auth/:path*`,
      },// 移除上传相关的重写规则，现在由前端API路由处理
      // 上传路由现在在前端处理，会自动添加认证头转发到后端
      // {
      //   source: '/api/admin/:path*',
      //   destination: `${apiBase}/admin/:path*`,
      // },
      // 可以根据需要添加更多特定的API路由重定向
    ];
  },
  
  // 环境变量配置
  env: {
    CUSTOM_KEY: 'lc-mall',
  },
    // 压缩配置
  compress: true,

  // 生成静态导出
  trailingSlash: true,
  
  // TypeScript 配置
  typescript: {
    ignoreBuildErrors: false,
  },
  
  // ESLint 配置
  eslint: {
    ignoreDuringBuilds: false,
  },
  // 实验性功能
  experimental: {
    serverComponentsExternalPackages: ['redis'],
  },
};

module.exports = nextConfig;

/** @type {import('next').NextConfig} */
const nextConfig = {
  // 显式指定源代码目录
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],
  
  // 图片优化配置
  images: {
    domains: ['localhost'],
    unoptimized: true,
  },
    // API 路由配置
  async rewrites() {
    // 统一使用 NEXT_PUBLIC_API_URL，自动拼接 path
    const apiBase = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, '') || 'http://localhost:5000/api';
    return [
      // 新闻相关的API请求使用前端的API路由，不重定向
      // 注意：这里不需要重写规则，因为我们希望使用前端的 /api/news/* 路由

      // 其他API请求重定向到后端（明确指定需要重定向的路径）
      {
        source: '/api/products/:path*',
        destination: `${apiBase}/products/:path*`,
      },
      {
        source: '/api/orders/:path*',
        destination: `${apiBase}/orders/:path*`,
      },
      {
        source: '/api/users/:path*',
        destination: `${apiBase}/users/:path*`,
      },
      {
        source: '/api/auth/:path*',
        destination: `${apiBase}/auth/:path*`,
      },
      // 可以根据需要添加更多特定的API路由重定向
    ];
  },
  
  // 环境变量配置
  env: {
    CUSTOM_KEY: 'lc-mall',
  },
    // 输出配置
  output: 'standalone',
  
  // 静态资源路径 (移除 assetPrefix，让 Next.js 使用默认路径)
  // assetPrefix: process.env.NODE_ENV === 'production' ? '/assets' : '',
  
  // 压缩配置
  compress: true,
  
  // 生成静态导出
  trailingSlash: true,
  
  // TypeScript 配置
  typescript: {
    ignoreBuildErrors: false,
  },
  
  // ESLint 配置
  eslint: {
    ignoreDuringBuilds: false,
  },
  
  // 实验性功能
  experimental: {
    serverComponentsExternalPackages: ['redis'],
  },
};

module.exports = nextConfig;

// frontend/src/app/api/news/[id]/route.ts
import { NextResponse } from 'next/server';
import { NewsArticle, newsCategories, NewsCategory } from '@/shared/types/News';
import { findArticle, incrementViews, mockNewsDb } from '@/shared/mockdata/news';
import { v4 as uuidv4 } from 'uuid';

// --- Redis Client Setup (Placeholder) ---
// import { createClient } from 'redis';
// const redisClient = createClient({ url: process.env.REDIS_URL || 'redis://localhost:6379' });
// redisClient.on('error', (err) => console.error('Redis Client Error', err));
// async function connectRedis() {
//   if (!redisClient.isOpen) {
//     await redisClient.connect();
//   }
// }
// connectRedis();

// Mock data is imported from shared location - no local declaration needed

interface Params {
  id: string;
}

export async function GET(request: Request, { params }: { params: Params }) {
  const { id } = params;
  try {
    // TODO: Replace with actual Redis data fetching
    // For Redis implementation, you would need to:
    // 1. Try to get by ID first: const articleData = await redisClient.hGetAll(`news:${id}`);
    // 2. If not found and id looks like a slug, try: const slugToId = await redisClient.hGet('news:slugs', id);
    // 3. Then fetch by real ID: const articleData = await redisClient.hGetAll(`news:${slugToId}`);

    // Find article by ID or slug using shared helper
    const article = findArticle(id);
    if (!article) {
      return NextResponse.json({ message: 'News article not found' }, { status: 404 });
    }
    
    // Increment views using shared helper
    incrementViews(id);

    return NextResponse.json({ article });

  } catch (error) {
    console.error(`API Error fetching news article ${id}:`, error);
    return NextResponse.json({ message: 'Error fetching news article', error: (error as Error).message }, { status: 500 });
  }
}

export async function PUT(request: Request, { params }: { params: Params }) {
  // Admin functionality - Placeholder
  // TODO: Implement authentication and authorization
  const { id } = params;
  try {
    const updatedArticleData = await request.json() as Partial<Omit<NewsArticle, 'id' | 'createdAt' | 'slug'>>;

    // TODO: Validate updatedArticleData
    // TODO: Fetch existing article from Redis
    // await connectRedis();
    // const existingArticle = await redisClient.hGetAll(`news:${id}`);
    // if (!existingArticle || Object.keys(existingArticle).length === 0) {
    //   return NextResponse.json({ message: 'News article not found for update' }, { status: 404 });
    // }
    
    // const articleToUpdate = { ...existingArticle, ...updatedArticleData, updatedAt: new Date().toISOString() };
    // if (updatedArticleData.title && !updatedArticleData.slug) {
    //   articleToUpdate.slug = updatedArticleData.title.toLowerCase().replace(/\s+/g, '-').slice(0, 50);
    // }
    // await redisClient.hSet(`news:${id}`, articleToUpdate as any);    // Update mock DB
    const articleIndex = mockNewsDb.findIndex(n => n.id === id || n.slug === id);
    if (articleIndex === -1) {
      return NextResponse.json({ message: 'News article not found for update' }, { status: 404 });
    }
    mockNewsDb[articleIndex] = { 
      ...mockNewsDb[articleIndex], 
      ...updatedArticleData, 
      updatedAt: new Date().toISOString(),
      category: (() => {
        const existingCategory = mockNewsDb[articleIndex].category;
        let newCategory: NewsCategory | undefined = undefined;

        // Check if updatedArticleData.category is a string (meaning it's an ID)
        if (typeof updatedArticleData.category === 'string') {
          const categoryIdString: string = updatedArticleData.category;
          newCategory = newsCategories.find(c => c.id === categoryIdString);
          // If a category was found by ID, use it. Otherwise, fallback to the existing category.
          return newCategory || existingCategory;
        } else if (updatedArticleData.category && typeof updatedArticleData.category === 'object' && 'id' in updatedArticleData.category) {
          // If updatedArticleData.category is an object and looks like a NewsCategory
          newCategory = updatedArticleData.category as NewsCategory; // Use the provided category object
          return newCategory;
        }
        // If updatedArticleData.category was not provided or not in a recognized format, keep the existing category.
        return existingCategory;
      })(),
    };


    return NextResponse.json({ message: 'News article updated successfully', article: mockNewsDb[articleIndex] });

  } catch (error) {
    console.error(`API Error updating news article ${id}:`, error);
    return NextResponse.json({ message: 'Error updating news article', error: (error as Error).message }, { status: 500 });
  }
}

export async function DELETE(request: Request, { params }: { params: Params }) {
  // Admin functionality - Placeholder
  // TODO: Implement authentication and authorization
  const { id } = params;
  try {
    // TODO: Delete from Redis
    // await connectRedis();
    // const articleData = await redisClient.hGetAll(`news:${id}`); // Get category before deleting
    // const result = await redisClient.del(`news:${id}`);
    // if (result === 0) {
    //   return NextResponse.json({ message: 'News article not found for deletion' }, { status: 404 });
    // }
    // // Remove from sorted sets
    // await redisClient.zRem('news:all:published_at', id);
    // if (articleData && articleData.category) {
    //   const categoryObj = JSON.parse(articleData.category); // Assuming category is stored as JSON string or just ID
    //   await redisClient.zRem(`news:cat:${categoryObj.id || articleData.category}:published_at`, id);
    // }
      // Delete from mock DB - support both ID and slug
    const initialLength = mockNewsDb.length;
    const filteredDb = mockNewsDb.filter(n => n.id !== id && n.slug !== id);
    if (filteredDb.length === initialLength) {
      return NextResponse.json({ message: 'News article not found for deletion' }, { status: 404 });
    }
    // Update the shared mock database
    mockNewsDb.length = 0;
    mockNewsDb.push(...filteredDb);

    return NextResponse.json({ message: 'News article deleted successfully' }, { status: 200 }); // Or 204 No Content

  } catch (error) {
    console.error(`API Error deleting news article ${id}:`, error);
    return NextResponse.json({ message: 'Error deleting news article', error: (error as Error).message }, { status: 500 });
  }
}

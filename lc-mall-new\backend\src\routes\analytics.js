/**
 * 访问统计路由
 * Analytics Routes - 访问统计相关的API路由
 */

const express = require('express');
const router = express.Router();
const AnalyticsController = require('../controllers/analyticsController');
const auth = require('../middleware/auth');
const rateLimit = require('express-rate-limit');

// 访问记录限流（防止刷量）
const trackingRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 后台访问限流
const adminRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 100, // 每分钟最多100次请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 公共路由（用于前端访问追踪）
router.post('/track', trackingRateLimit, AnalyticsController.track);

// 管理员路由（需要API key认证）
router.use('/admin', adminRateLimit, auth.authenticateAdminApiKey);

// 获取访问记录列表
router.get('/admin/records', AnalyticsController.getRecords);

// 获取统计数据
router.get('/admin/stats', AnalyticsController.getStats);

// 获取实时统计数据
router.get('/admin/realtime', AnalyticsController.getRealTimeStats);

// 获取热门页面
router.get('/admin/popular-pages', AnalyticsController.getPopularPages);

// 获取地理统计数据
router.get('/admin/geographic', AnalyticsController.getGeographicStats);

// 获取技术统计数据（设备、浏览器等）
router.get('/admin/technology', AnalyticsController.getTechnologyStats);

// 导出数据
router.get('/admin/export', AnalyticsController.exportData);

// 清理过期数据
router.post('/admin/cleanup', AnalyticsController.cleanup);

module.exports = router;

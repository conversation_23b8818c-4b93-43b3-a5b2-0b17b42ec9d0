/**
 * 测试API返回的产品数量
 */

const http = require('http');

function testAPI() {
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/products?limit=50',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('🔍 API测试结果:');
        console.log('状态码:', res.statusCode);
        console.log('响应成功:', response.success);
        console.log('消息:', response.message);
        
        if (response.data && response.data.products) {
          console.log('产品数量:', response.data.products.length);
          console.log('分页信息:', response.data.pagination);
          
          // 显示前3个产品的基本信息
          console.log('\n前3个产品:');
          response.data.products.slice(0, 3).forEach((product, index) => {
            console.log(`${index + 1}. ${product.name} (ID: ${product.id}, 分类: ${product.category})`);
          });
        }
        
        console.log('\n响应大小:', data.length, '字节');
      } catch (error) {
        console.error('解析响应失败:', error);
        console.log('原始响应:', data.substring(0, 500));
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求失败:', error);
  });

  req.end();
}

console.log('🚀 开始测试API...');
testAPI();

import { NextRequest, NextResponse } from 'next/server';

// 禁用路由缓存
export const dynamic = 'force-dynamic';
export const revalidate = 0;

// Proxy to backend API for products
export async function GET(request: NextRequest) {
  try {
    const apiBase = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, '') || 'http://localhost:5000/api';

    // 转发URL参数到后端
    const { searchParams } = new URL(request.url);
    const backendQuery = new URLSearchParams(searchParams);

    const backendUrl = `${apiBase}/products?${backendQuery.toString()}`;
    console.log('Frontend API: Fetching from backend:', backendUrl);
    console.log('Environment NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);

    // 添加缓存控制头
    const response = await fetch(backendUrl, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    console.log('Backend response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend API error:', response.status, errorText);
      throw new Error(`Backend API request failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Backend response success:', result.success, 'Products count:', result.data?.products?.length);

    // Backend returns data in ApiResponse format: { success: true, data: { products: [...], pagination: {...} }, message: '' }
    if (result.success && result.data) {
      // 返回完整的数据结构，包括products和pagination
      const responseData = NextResponse.json(result.data);

      // 设置响应头禁用缓存
      responseData.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      responseData.headers.set('Pragma', 'no-cache');
      responseData.headers.set('Expires', '0');

      return responseData;
    } else {
      console.error('Invalid backend response format:', result);
      throw new Error('Invalid backend response format');
    }
  } catch (error) {
    console.error('Error proxying to backend:', error);

    return NextResponse.json(
      { error: 'Failed to fetch products', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Get single product by ID
export async function POST(request: NextRequest) {
  let id: string | undefined;
  
  try {
    const requestBody = await request.json();
    id = requestBody.id;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    const apiBase = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, '') || 'http://localhost:5000/api';
    const response = await fetch(`${apiBase}/products/${id}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json(
          { error: 'Product not found' },
          { status: 404 }
        );
      }
      throw new Error('Backend API request failed');
    }
    
    const result = await response.json();
    
    if (result.success && result.data) {
      return NextResponse.json(result.data);
    } else {
      throw new Error('Invalid backend response format');
    }
  } catch (error) {
    console.error('Error fetching product:', error);
    
    return NextResponse.json(
      { error: 'Product not found' },
      { status: 404 }
    ); 
  } 
}

#!/bin/bash

# 产品数据清理工具 (Linux/Mac 版本)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${MAGENTA}"
echo "============================================"
echo "           产品数据清理工具"
echo "============================================"
echo -e "${NC}"
echo
echo -e "${YELLOW}此工具将清空所有现有产品数据${NC}"
echo -e "${YELLOW}为重新运行 importProducts.js 做准备${NC}"
echo
echo -e "${RED}注意: 此操作不可恢复，请谨慎使用！${NC}"
echo

# 获取脚本所在目录
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$DIR"

# 运行清理脚本
node clearProducts.js

echo
echo -e "${BLUE}脚本执行完成${NC}"

const fs = require('fs');
const FormData = require('form-data');
const fetch = require('node-fetch');

const testImageUpload = async () => {
  try {
    console.log('Testing actual image upload...');
    
    // 创建一个简单的测试图片文件（1x1 PNG）
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x6B, 0x8E, 0x24, 0x02, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    // 保存测试图片文件
    const testImagePath = './test-image.png';
    fs.writeFileSync(testImagePath, testImageBuffer);
    
    // 创建FormData
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));
    
    // 发送上传请求
    const response = await fetch('http://gdlongchi.cn/api/admin/upload/image', {
      method: 'POST',
      headers: {
        'x-admin-api-key': 'lc_admin_dev_key_2025',
        ...formData.getHeaders()
      },
      body: formData
    });
    
    console.log('Upload response status:', response.status);
    
    const result = await response.text();
    console.log('Upload response body:', result);
    
    // 清理测试文件
    fs.unlinkSync(testImagePath);
    
    if (response.ok) {
      console.log('✅ Image upload test PASSED');
      
      try {
        const jsonResult = JSON.parse(result);
        if (jsonResult.success && jsonResult.data && jsonResult.data.url) {
          console.log('📷 Uploaded image URL:', jsonResult.data.url);
        }
      } catch (e) {
        // 忽略JSON解析错误
      }
    } else {
      console.log('❌ Image upload test FAILED');
    }
    
  } catch (error) {
    console.error('❌ Upload test error:', error.message);
  }
};

testImageUpload();

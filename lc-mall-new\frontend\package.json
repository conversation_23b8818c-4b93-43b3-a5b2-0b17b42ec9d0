{"name": "lc-mall-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.16", "axios": "^1.6.2", "cannon-es": "^0.20.0", "clsx": "^2.0.0", "framer-motion": "^12.17.0", "lucide-react": "^0.294.0", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "three": "^0.177.0", "uuid": "^11.1.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/three": "^0.177.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}}
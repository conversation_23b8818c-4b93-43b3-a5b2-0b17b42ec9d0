/**
 * 产品分类配置 - 前端专用（支持 ES6 模块）
 * 从共享配置导入并重新导出
 */

// 中文分类名称到英文分类值的映射
export const categoryMapping = {
  '样品': 'samples',
  '潜固化剂': 'latent-curing-agents',
  '催化剂': 'catalysts',
  '分散剂': 'dispersants',
  '流平剂': 'leveling-agents',
  '助剂': 'additives',
  '原料': 'raw-materials',
  '附着力促进剂': 'raw-materials', // 添加附着力促进剂映射
  '固化剂': 'curing-agents',
  '设备': 'equipment'
};

// 分类选项配置（用于前端下拉选择器）
export const categoryOptions = [
  { value: '', label: '请选择分类' },
  { value: 'catalysts', label: '催化剂' },
  { value: 'curing-agents', label: '固化剂' },
  { value: 'additives', label: '助剂' },
  { value: 'raw-materials', label: '原材料' },
  { value: 'equipment', label: '设备' },
  { value: 'samples', label: '样品' },
  { value: 'latent-curing-agents', label: '潜固化剂' },
  { value: 'dispersants', label: '分散剂' },
  { value: 'leveling-agents', label: '流平剂' }
];

// 英文分类值到中文显示名称的映射
export const categoryDisplayNames = {
  'catalysts': '催化剂',
  'curing-agents': '固化剂',
  'additives': '助剂',
  'raw-materials': '原材料',
  'equipment': '设备',
  'samples': '样品',
  'latent-curing-agents': '潜固化剂',
  'dispersants': '分散剂',
  'leveling-agents': '流平剂'
};

// 产品状态选项
export const statusOptions = [
  { value: 'active', label: '上架' },
  { value: 'inactive', label: '下架' },
  { value: 'draft', label: '草稿' }
];

// 计量单位选项
export const unitOptions = [
  { value: '吨', label: '吨' },
  { value: '公斤', label: '公斤' },
  { value: 'kg', label: 'kg' },
  { value: '升', label: '升' },
  { value: '桶', label: '桶' },
  { value: '个', label: '个' },
  { value: '件', label: '件' }
];

// 获取分类显示名称
export const getCategoryDisplayName = (categoryValue: string): string => {
  return categoryDisplayNames[categoryValue as keyof typeof categoryDisplayNames] || categoryValue;
};

// 根据中文分类名称获取英文分类值
export const getCategoryValue = (chineseName: string): string => {
  return categoryMapping[chineseName as keyof typeof categoryMapping] || 'raw-materials';
};

// 验证分类值是否有效
export const isValidCategory = (categoryValue: string): boolean => {
  return Object.values(categoryMapping).includes(categoryValue) || 
         categoryOptions.some(option => option.value === categoryValue);
};
